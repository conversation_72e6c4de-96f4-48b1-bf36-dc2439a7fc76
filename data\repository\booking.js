import firebaseAuth from "@react-native-firebase/auth";
import axios from "axios";

import { CONSTANTS } from "../../constants/constants";
import { Booking } from "../model/booking";


export class BookingRepository {
  /**
   *
   * @param {object} param0
   * @param {String} param0.token
   */
  constructor() {}
  static getBookingByID = async ({ bookingID }) => {
    try {
      const url = `${
        CONSTANTS.API_SERVER
      }${CONSTANTS.APIS.BOOKING.getBookingByID(bookingID)}`;
      const bearerToken = await firebaseAuth().currentUser.getIdToken();
      if (bearerToken) {
        const response = await axios.get(url, {
          headers: { authorization: `Bearer ${bearerToken}` },
        });
        if (response.data && response.data.success === true) {
          return new Booking(response.data.booking);
        } else if (response.data.error) {
          throw response.data.error;
        } else {
          throw CONSTANTS.ERROR_CODES.SERVER_ERROR;
        }
      } else {
        throw CONSTANTS.ERROR_CODES.USER_AUTH_TOKEN_NOT_FOUND;
      }
    } catch (error) {
      throw error;
    }
  };
}

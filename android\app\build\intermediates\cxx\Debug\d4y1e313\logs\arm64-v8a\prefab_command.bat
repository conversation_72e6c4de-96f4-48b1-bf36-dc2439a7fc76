@echo off
"C:\\Program Files\\Microsoft\\jdk-17.0.10.7-hotspot\\bin\\java" ^
  --class-path ^
  "C:\\Users\\<USER>\\.gradle\\caches\\modules-2\\files-2.1\\com.google.prefab\\cli\\2.1.0\\aa32fec809c44fa531f01dcfb739b5b3304d3050\\cli-2.1.0-all.jar" ^
  com.google.prefab.cli.AppKt ^
  --build-system ^
  cmake ^
  --platform ^
  android ^
  --abi ^
  arm64-v8a ^
  --os-version ^
  24 ^
  --stl ^
  c++_shared ^
  --ndk-version ^
  27 ^
  --output ^
  "C:\\Users\\<USER>\\AppData\\Local\\Temp\\agp-prefab-staging198126683963531929\\staged-cli-output" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d3dbf06d81207983406d2bec1cfbc8a3\\transformed\\react-android-0.79.5-debug\\prefab" ^
  "D:\\PROJECTS\\ORDERS\\driver\\android\\app\\build\\intermediates\\cxx\\refs\\react-native-reanimated\\81c4an2d" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\2467392bc4735b5fa4189df5eec8fe47\\transformed\\hermes-android-0.79.5-debug\\prefab" ^
  "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\940c6befb88fdf8d1bc6da42a2c94d46\\transformed\\fbjni-0.7.0\\prefab"

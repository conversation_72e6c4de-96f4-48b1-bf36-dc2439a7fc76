{"logs": [{"outputFile": "co.auter.hcorp.app-mergeDebugResources-75:/values-ru/values-ru.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\aabce3f69efc1e684aed892eac0d7652\\transformed\\core-1.13.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,457,562,665,782", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "148,250,351,452,557,660,777,878"}, "to": {"startLines": "43,44,45,46,47,48,49,163", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3880,3978,4080,4181,4282,4387,4490,14687", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "3973,4075,4176,4277,4382,4485,4602,14783"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\fb136d748d22122296a1ea8ffd25a4a4\\transformed\\appcompat-1.7.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,421,507,612,733,812,888,980,1074,1169,1262,1357,1451,1547,1642,1734,1826,1915,2021,2128,2226,2335,2442,2556,2722,2822", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "215,317,416,502,607,728,807,883,975,1069,1164,1257,1352,1446,1542,1637,1729,1821,1910,2016,2123,2221,2330,2437,2551,2717,2817,2899"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,151", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "431,546,648,747,833,938,1059,1138,1214,1306,1400,1495,1588,1683,1777,1873,1968,2060,2152,2241,2347,2454,2552,2661,2768,2882,3048,13714", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "541,643,742,828,933,1054,1133,1209,1301,1395,1490,1583,1678,1772,1868,1963,2055,2147,2236,2342,2449,2547,2656,2763,2877,3043,3143,13791"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\d3dbf06d81207983406d2bec1cfbc8a3\\transformed\\react-android-0.79.5-debug\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,129,218,292,367,456,525,592,669,748,837,934,1006,1090,1183,1258,1340,1423,1500,1572,1647,1732,1804,1884,1954", "endColumns": "73,88,73,74,88,68,66,76,78,88,96,71,83,92,74,81,82,76,71,74,84,71,79,69,84", "endOffsets": "124,213,287,362,451,520,587,664,743,832,929,1001,1085,1178,1253,1335,1418,1495,1567,1642,1727,1799,1879,1949,2034"}, "to": {"startLines": "35,53,79,81,82,84,98,99,100,147,148,149,150,155,156,157,158,159,160,161,162,164,165,166,167", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3148,4933,7965,8116,8191,8343,9389,9456,9533,13372,13461,13558,13630,14045,14138,14213,14295,14378,14455,14527,14602,14788,14860,14940,15010", "endColumns": "73,88,73,74,88,68,66,76,78,88,96,71,83,92,74,81,82,76,71,74,84,71,79,69,84", "endOffsets": "3217,5017,8034,8186,8275,8407,9451,9528,9607,13456,13553,13625,13709,14133,14208,14290,14373,14450,14522,14597,14682,14855,14935,15005,15090"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\0208c1dcf45a74103da4e70fe6ed0843\\transformed\\browser-1.6.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,163,268,380", "endColumns": "107,104,111,104", "endOffsets": "158,263,375,480"}, "to": {"startLines": "72,76,77,78", "startColumns": "4,4,4,4", "startOffsets": "7295,7643,7748,7860", "endColumns": "107,104,111,104", "endOffsets": "7398,7743,7855,7960"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\c840b354751b833ba967f669749ddf08\\transformed\\play-services-basement-18.5.0\\res\\values-ru\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "152", "endOffsets": "347"}, "to": {"startLines": "62", "startColumns": "4", "startOffsets": "6021", "endColumns": "156", "endOffsets": "6173"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\e7b600ce1a3b12fd88ac7a70edae70a3\\transformed\\material-1.12.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,381,459,537,621,719,810,907,1044,1136,1211,1277,1376,1453,1516,1634,1695,1760,1817,1887,1948,2002,2118,2175,2237,2291,2365,2493,2581,2668,2771,2863,2949,3086,3170,3255,3389,3480,3556,3610,3661,3727,3799,3877,3948,4030,4110,4186,4263,4340,4447,4536,4609,4699,4794,4868,4949,5042,5097,5178,5244,5330,5415,5477,5541,5604,5676,5774,5873,5968,6060,6118,6173,6253,6347,6423", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,77,77,83,97,90,96,136,91,74,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,86,102,91,85,136,83,84,133,90,75,53,50,65,71,77,70,81,79,75,76,76,106,88,72,89,94,73,80,92,54,80,65,85,84,61,63,62,71,97,98,94,91,57,54,79,93,75,78", "endOffsets": "376,454,532,616,714,805,902,1039,1131,1206,1272,1371,1448,1511,1629,1690,1755,1812,1882,1943,1997,2113,2170,2232,2286,2360,2488,2576,2663,2766,2858,2944,3081,3165,3250,3384,3475,3551,3605,3656,3722,3794,3872,3943,4025,4105,4181,4258,4335,4442,4531,4604,4694,4789,4863,4944,5037,5092,5173,5239,5325,5410,5472,5536,5599,5671,5769,5868,5963,6055,6113,6168,6248,6342,6418,6497"}, "to": {"startLines": "2,38,39,40,41,42,50,51,52,73,74,75,80,83,85,86,87,88,89,90,91,92,93,94,95,96,97,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,152,153,154", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3451,3529,3607,3691,3789,4607,4704,4841,7403,7478,7544,8039,8280,8412,8530,8591,8656,8713,8783,8844,8898,9014,9071,9133,9187,9261,9612,9700,9787,9890,9982,10068,10205,10289,10374,10508,10599,10675,10729,10780,10846,10918,10996,11067,11149,11229,11305,11382,11459,11566,11655,11728,11818,11913,11987,12068,12161,12216,12297,12363,12449,12534,12596,12660,12723,12795,12893,12992,13087,13179,13237,13292,13796,13890,13966", "endLines": "7,38,39,40,41,42,50,51,52,73,74,75,80,83,85,86,87,88,89,90,91,92,93,94,95,96,97,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,152,153,154", "endColumns": "12,77,77,83,97,90,96,136,91,74,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,86,102,91,85,136,83,84,133,90,75,53,50,65,71,77,70,81,79,75,76,76,106,88,72,89,94,73,80,92,54,80,65,85,84,61,63,62,71,97,98,94,91,57,54,79,93,75,78", "endOffsets": "426,3524,3602,3686,3784,3875,4699,4836,4928,7473,7539,7638,8111,8338,8525,8586,8651,8708,8778,8839,8893,9009,9066,9128,9182,9256,9384,9695,9782,9885,9977,10063,10200,10284,10369,10503,10594,10670,10724,10775,10841,10913,10991,11062,11144,11224,11300,11377,11454,11561,11650,11723,11813,11908,11982,12063,12156,12211,12292,12358,12444,12529,12591,12655,12718,12790,12888,12987,13082,13174,13232,13287,13367,13885,13961,14040"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\6f32c56df1f09404e35eae2beaed3ad9\\transformed\\play-services-base-18.5.0\\res\\values-ru\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,458,580,686,824,949,1060,1160,1337,1440,1599,1721,1884,2038,2103,2159", "endColumns": "102,161,121,105,137,124,110,99,176,102,158,121,162,153,64,55,81", "endOffsets": "295,457,579,685,823,948,1059,1159,1336,1439,1598,1720,1883,2037,2102,2158,2240"}, "to": {"startLines": "54,55,56,57,58,59,60,61,63,64,65,66,67,68,69,70,71", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5022,5129,5295,5421,5531,5673,5802,5917,6178,6359,6466,6629,6755,6922,7080,7149,7209", "endColumns": "106,165,125,109,141,128,114,103,180,106,162,125,166,157,68,59,85", "endOffsets": "5124,5290,5416,5526,5668,5797,5912,6016,6354,6461,6624,6750,6917,7075,7144,7204,7290"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.13\\transforms\\4b777fbb16acd4827e263db9b1a694ca\\transformed\\credentials-1.2.0-rc01\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,118", "endOffsets": "160,279"}, "to": {"startLines": "36,37", "startColumns": "4,4", "startOffsets": "3222,3332", "endColumns": "109,118", "endOffsets": "3327,3446"}}]}]}
import firebaseAuth from '@react-native-firebase/auth';
import axios from 'axios';

import { CONSTANTS } from '../../constants/constants';
import { Driver } from '../model/driver';

export class DriverRepository {
  /**
   *
   * @param {object} param0
   * @param {String} param0.token
   */
  constructor() {}

  static getDBDriver = async () => {
    try {
      const url = `${CONSTANTS.API_SERVER}${CONSTANTS.APIS.DRIVER.getSelf()}`;
      const bearerToken = await firebaseAuth().currentUser.getIdToken();
      if (bearerToken) {
        const response = await axios.get(url, {
          headers: { authorization: `Bearer ${bearerToken}` },
        });
        if (response.data && response.data.success === true) {
          return new Driver({ ...response.data.driver });
        } else if (response.data.error) {
          throw response.data.error;
        } else {
          throw CONSTANTS.ERROR_CODES.SERVER_ERROR;
        }
      } else {
        throw CONSTANTS.ERROR_CODES.USER_AUTH_TOKEN_NOT_FOUND;
      }
    } catch (error) {
      throw error;
    }
  };

  static updateProfile = async ({ firstName, lastName, email, address1 }) => {
    console.log("updateProfile", firstName, lastName, email, address1);
    try {
      const url = `${
        CONSTANTS.API_SERVER
      }${CONSTANTS.APIS.DRIVER.updateProfile()}`;
      const bearerToken = await firebaseAuth().currentUser.getIdToken();
      if (bearerToken) {
        const response = await axios.patch(
          url,
          { first_name: firstName, last_name: lastName, email, address1 },
          {
            headers: { authorization: `Bearer ${bearerToken}` },
          }
        );
        if (response.data && response.data.success === true) {
          return new Driver({ ...response.data.user });
        } else if (response.data.error) {
          throw response.data.error;
        } else {
          throw CONSTANTS.ERROR_CODES.SERVER_ERROR;
        }
      } else {
        throw CONSTANTS.ERROR_CODES.USER_AUTH_TOKEN_NOT_FOUND;
      }
    } catch (error) {
      throw error;
    }
  };

  static updateAvailabilityStatus = async ({ driverAvailabilityStatus }) => {
    try {
      const url = `${
        CONSTANTS.API_SERVER
      }${CONSTANTS.APIS.DRIVER.updateAvailabilityStatus()}`;
      const bearerToken = await firebaseAuth().currentUser.getIdToken();
      if (bearerToken) {
        const response = await axios.patch(
          url,
          {
            availabilityStatus: driverAvailabilityStatus,
          },
          {
            headers: { authorization: `Bearer ${bearerToken}` },
          }
        );
        if (response.data && response.data.success === true) {
          return new Driver({ ...response.data.user });
        } else if (response.data.error) {
          throw response.data.error;
        } else {
          throw CONSTANTS.ERROR_CODES.SERVER_ERROR;
        }
      } else {
        throw CONSTANTS.ERROR_CODES.USER_AUTH_TOKEN_NOT_FOUND;
      }
    } catch (error) {
      throw error;
    }
  };

  static updatePushNotificationToken = async ({ token }) => {
    try {
      const url = `${
        CONSTANTS.API_SERVER
      }${CONSTANTS.APIS.DRIVER.updatePushNotificationToken()}`;
      const bearerToken = await firebaseAuth().currentUser.getIdToken();
      if (bearerToken) {
        const response = await axios.post(
          url,
          { push_notification_token: token },
          {
            headers: { authorization: `Bearer ${bearerToken}` },
          }
        );
        if (response.data && response.data.success === true) {
          return true;
        } else if (response.data.error) {
          throw response.data.error;
        } else {
          throw CONSTANTS.ERROR_CODES.SERVER_ERROR;
        }
      } else {
        throw CONSTANTS.ERROR_CODES.USER_AUTH_TOKEN_NOT_FOUND;
      }
    } catch (error) {
      throw error;
    }
  };

  static updateFCMToken = async ({ token }) => {
    try {
      const url = `${CONSTANTS.API_SERVER}${CONSTANTS.APIS.DRIVER.updateFCMToken()}`;
      const bearerToken = await firebaseAuth().currentUser.getIdToken();
      if (bearerToken) {
        const response = await axios.patch(
          url,
          { fcmToken: token },
          {
            headers: { authorization: `Bearer ${bearerToken}` },
          }
        );
        if (response.data && response.data.success === true) {
          return true;
        } else if (response.data.error) {
          throw response.data.error;
        } else {
          throw CONSTANTS.ERROR_CODES.SERVER_ERROR;
        }
      } else {
        throw CONSTANTS.ERROR_CODES.USER_AUTH_TOKEN_NOT_FOUND;
      }
    } catch (error) {
      throw error;
    }
  };
}

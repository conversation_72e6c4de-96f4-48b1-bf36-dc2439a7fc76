import * as Location from "expo-location";
import * as Notifications from "expo-notifications";
import { createContext, useContext, useEffect, useState } from "react";
import { PermissionsAndroid, Platform } from "react-native";

const PermissionsStateContext = createContext(undefined);
const PermissionsActionsContext = createContext(undefined);

export const PermissionsContextProvider = ({ children }) => {
    const [permissionsState, setPermissionsState] = useState({
        isLoading: true,
        granted: [],
        denied: [],
        error: null,
    });

    const isPermissionsLoading = permissionsState.isLoading;
    const permissionGranted = permissionsState.denied.length === 0;
    const remainingPermissions = permissionsState.denied;
    const permissionError = permissionsState.error;

    useEffect(() => {
        checkAndRequestRequiredPermissions();
    }, []);

    const checkRequiredPermissions = async () => {
        setPermissionsState((prev) => ({
            ...prev,
            isLoading: true,
            granted: [],
            denied: [],
            error: null,
        }));

        const granted = [];
        const denied = [];

        try {
            if (Platform.OS === "android") {
                const apiLevel = parseInt(Platform.Version.toString(), 10);

                
                // Foreground + background location
                const fgLoc = await Location.getForegroundPermissionsAsync();
                console.log("fgLoc", fgLoc);
                if (fgLoc.status === "granted") {
                    granted.push({permission: "ACCESS_FINE_LOCATION", status: fgLoc.status ,message: "Foreground location permission granted" });
                } else {
                    denied.push({ permission: "ACCESS_FINE_LOCATION", status: fgLoc.status ,message: "Foreground location permission denied" });
                }

                const bgLoc = await Location.getBackgroundPermissionsAsync();
                console.log("bgLoc", bgLoc);
                if (bgLoc.status === "granted") {
                    granted.push({permission: "ACCESS_BACKGROUND_LOCATION", status: bgLoc.status ,message: "Background location permission granted" });
                } else {
                    denied.push({ permission: "ACCESS_BACKGROUND_LOCATION", status: bgLoc.status ,message: "Background location permission denied" });
                }

                // Notifications
                const notifPerm = await Notifications.getPermissionsAsync();
                console.log("notifPerm", notifPerm);
                if (notifPerm.status === "granted") {
                    granted.push({permission: "POST_NOTIFICATIONS", status: notifPerm.status ,message: "Notifications permission granted" });
                } else {
                    denied.push({ permission: "POST_NOTIFICATIONS", status: notifPerm.status ,message: "Notifications permission denied" });
                }

                // Overlay
                
            }
        } catch (error) {
            setPermissionsState((prev) => ({
                ...prev,
                isLoading: false,
                error,
            }));
            console.log("error", error);
            return { granted, denied };
        }

        setPermissionsState({
            isLoading: false,
            granted,
            denied,
            error: null,
        });

        return { granted, denied };
    };

    const requestRequiredPermissions = async (permissions) => {
        try {
            if (Platform.OS !== "android") return;

            for (const perm of permissions) {
                switch (perm) {
                    case "ACCESS_FINE_LOCATION":
                        await Location.requestForegroundPermissionsAsync();
                        break;
                    case "ACCESS_BACKGROUND_LOCATION":
                        await Location.requestBackgroundPermissionsAsync();
                        break;
                    case "POST_NOTIFICATIONS":
                        await Notifications.requestPermissionsAsync();
                        break;
                    case "READ_MEDIA_IMAGES":
                        await PermissionsAndroid.request(
                            PermissionsAndroid.PERMISSIONS.READ_MEDIA_IMAGES
                        );
                        break;
                    case "READ_EXTERNAL_STORAGE":
                        await PermissionsAndroid.request(
                            PermissionsAndroid.PERMISSIONS.READ_EXTERNAL_STORAGE
                        );
                        break;
                    case "SYSTEM_ALERT_WINDOW":
                        await PermissionsAndroid.request(
                            PermissionsAndroid.PERMISSIONS.SYSTEM_ALERT_WINDOW
                        );
                        break;
                    default:
                        break;
                }
            }

            // Re-check after requesting
            await checkRequiredPermissions();
        } catch (err) {
            console.error("Permission request error:", err);
        }
    };

    const checkAndRequestRequiredPermissions = async () => {
        const { denied } = await checkRequiredPermissions();
        if (denied.length > 0) {
            await requestRequiredPermissions(denied);
        }
    };

    return (
        <PermissionsStateContext.Provider
            value={{
                isPermissionsLoading,
                permissionGranted,
                remainingPermissions,
                permissionError,
            }}
        >
            <PermissionsActionsContext.Provider
                value={{
                    checkRequiredPermissions,
                    requestRequiredPermissions,
                    checkAndRequestRequiredPermissions,
                }}
            >
                {children}
            </PermissionsActionsContext.Provider>
        </PermissionsStateContext.Provider>
    );
};

export const usePermissionsState = () => {
    const context = useContext(PermissionsStateContext);
    if (context === undefined) {
        throw new Error("usePermissionsState must be used inside PermissionsContextProvider");
    }
    return context;
};

export const usePermissionsActions = () => {
    const context = useContext(PermissionsActionsContext);
    if (context === undefined) {
        throw new Error("usePermissionsActions must be used inside PermissionsContextProvider");
    }
    return context;
};

1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="co.auter.hcorp"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
11-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:10:3-75
11-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:10:20-73
12    <uses-permission android:name="android.permission.ACCESS_BACKGROUND_LOCATION" />
12-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:2:3-82
12-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:2:20-80
13    <uses-permission android:name="android.permission.ACCESS_COARSE_LOCATION" />
13-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:3:3-78
13-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:3:20-76
14    <uses-permission android:name="android.permission.ACCESS_FINE_LOCATION" />
14-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:4:3-76
14-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:4:20-74
15    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
15-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:5:3-74
15-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:5:20-72
16    <uses-permission android:name="android.permission.FOREGROUND_SERVICE_LOCATION" />
16-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:6:3-83
16-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:6:20-81
17    <uses-permission android:name="android.permission.INTERNET" />
17-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:7:3-64
17-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:7:20-62
18    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
18-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:8:3-74
18-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:8:20-72
19    <uses-permission
19-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:9:3-77
20        android:name="android.permission.READ_EXTERNAL_STORAGE"
20-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:9:20-75
21        android:maxSdkVersion="32" />
21-->[BareExpo:expo.modules.image:2.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\9a002a705198c4a20326b7b86bb4bdf8\transformed\expo.modules.image-2.4.0\AndroidManifest.xml:17:9-35
22    <uses-permission android:name="android.permission.VIBRATE" />
22-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:11:3-63
22-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:11:20-61
23    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
23-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:12:3-78
23-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:12:20-76
24
25    <queries>
25-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:13:3-19:13
26        <intent>
26-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:14:5-18:14
27            <action android:name="android.intent.action.VIEW" />
27-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:15:7-58
27-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:15:15-56
28
29            <category android:name="android.intent.category.BROWSABLE" />
29-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:16:7-67
29-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:16:17-65
30
31            <data android:scheme="https" />
31-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:17:7-37
31-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:17:13-35
32        </intent>
33        <!-- Query open documents -->
34        <intent>
34-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\0ec2b2613645c132734cc6d66ce0081b\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:15:9-17:18
35            <action android:name="android.intent.action.OPEN_DOCUMENT_TREE" />
35-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\0ec2b2613645c132734cc6d66ce0081b\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:13-79
35-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\0ec2b2613645c132734cc6d66ce0081b\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:16:21-76
36        </intent>
37        <intent>
37-->[host.exp.exponent:expo.modules.webbrowser:14.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4efa077fdff83a851f1385750a13e9ad\transformed\expo.modules.webbrowser-14.2.0\AndroidManifest.xml:8:9-12:18
38
39            <!-- Required for opening tabs if targeting API 30 -->
40            <action android:name="android.support.customtabs.action.CustomTabsService" />
40-->[host.exp.exponent:expo.modules.webbrowser:14.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4efa077fdff83a851f1385750a13e9ad\transformed\expo.modules.webbrowser-14.2.0\AndroidManifest.xml:11:13-90
40-->[host.exp.exponent:expo.modules.webbrowser:14.2.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\4efa077fdff83a851f1385750a13e9ad\transformed\expo.modules.webbrowser-14.2.0\AndroidManifest.xml:11:21-87
41        </intent>
42    </queries>
43
44    <uses-permission android:name="android.permission.WAKE_LOCK" />
44-->[:react-native-firebase_auth] D:\PROJECTS\ORDERS\driver\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:5-68
44-->[:react-native-firebase_auth] D:\PROJECTS\ORDERS\driver\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:22-65
45    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
45-->[:react-native-firebase_auth] D:\PROJECTS\ORDERS\driver\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:5-79
45-->[:react-native-firebase_auth] D:\PROJECTS\ORDERS\driver\node_modules\@react-native-firebase\auth\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:22-76
46    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" /> <!-- Required by older versions of Google Play services to create IID tokens -->
46-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:7:5-81
46-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:7:22-78
47    <uses-permission android:name="com.google.android.c2dm.permission.RECEIVE" />
47-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb053893dfd80c0208d2029bf61dadd3\transformed\firebase-messaging-25.0.0\AndroidManifest.xml:26:5-82
47-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb053893dfd80c0208d2029bf61dadd3\transformed\firebase-messaging-25.0.0\AndroidManifest.xml:26:22-79
48    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
48-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\20fa9879568f2fafdd64ed8da79cb65e\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:5-98
48-->[com.google.android.recaptcha:recaptcha:18.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\20fa9879568f2fafdd64ed8da79cb65e\transformed\recaptcha-18.6.1\AndroidManifest.xml:9:22-95
49
50    <permission
50-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\aabce3f69efc1e684aed892eac0d7652\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
51        android:name="co.auter.hcorp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
51-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\aabce3f69efc1e684aed892eac0d7652\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
52        android:protectionLevel="signature" />
52-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\aabce3f69efc1e684aed892eac0d7652\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
53
54    <uses-permission android:name="co.auter.hcorp.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
54-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\aabce3f69efc1e684aed892eac0d7652\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
54-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\aabce3f69efc1e684aed892eac0d7652\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
55    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" /> <!-- for android -->
55-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4574e0db00724a4641f8ce68a008e333\transformed\installreferrer-2.2\AndroidManifest.xml:9:5-110
55-->[com.android.installreferrer:installreferrer:2.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\4574e0db00724a4641f8ce68a008e333\transformed\installreferrer-2.2\AndroidManifest.xml:9:22-107
56    <!-- <uses-permission android:name="com.android.launcher.permission.READ_SETTINGS"/> -->
57    <!-- <uses-permission android:name="com.android.launcher.permission.WRITE_SETTINGS"/> -->
58    <!-- <uses-permission android:name="com.android.launcher.permission.INSTALL_SHORTCUT" /> -->
59    <!-- <uses-permission android:name="com.android.launcher.permission.UNINSTALL_SHORTCUT" /> -->
60    <!-- for Samsung -->
61    <uses-permission android:name="com.sec.android.provider.badge.permission.READ" />
61-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\05b2fd90e25e22a7c2deb1fd34e637b4\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:5-86
61-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\05b2fd90e25e22a7c2deb1fd34e637b4\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:19:22-83
62    <uses-permission android:name="com.sec.android.provider.badge.permission.WRITE" /> <!-- for htc -->
62-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\05b2fd90e25e22a7c2deb1fd34e637b4\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:5-87
62-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\05b2fd90e25e22a7c2deb1fd34e637b4\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:20:22-84
63    <uses-permission android:name="com.htc.launcher.permission.READ_SETTINGS" />
63-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\05b2fd90e25e22a7c2deb1fd34e637b4\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:5-81
63-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\05b2fd90e25e22a7c2deb1fd34e637b4\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:23:22-78
64    <uses-permission android:name="com.htc.launcher.permission.UPDATE_SHORTCUT" /> <!-- for sony -->
64-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\05b2fd90e25e22a7c2deb1fd34e637b4\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:5-83
64-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\05b2fd90e25e22a7c2deb1fd34e637b4\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:24:22-80
65    <uses-permission android:name="com.sonyericsson.home.permission.BROADCAST_BADGE" />
65-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\05b2fd90e25e22a7c2deb1fd34e637b4\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:5-88
65-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\05b2fd90e25e22a7c2deb1fd34e637b4\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:27:22-85
66    <uses-permission android:name="com.sonymobile.home.permission.PROVIDER_INSERT_BADGE" /> <!-- for apex -->
66-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\05b2fd90e25e22a7c2deb1fd34e637b4\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:5-92
66-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\05b2fd90e25e22a7c2deb1fd34e637b4\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:28:22-89
67    <uses-permission android:name="com.anddoes.launcher.permission.UPDATE_COUNT" /> <!-- for solid -->
67-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\05b2fd90e25e22a7c2deb1fd34e637b4\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:5-84
67-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\05b2fd90e25e22a7c2deb1fd34e637b4\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:31:22-81
68    <uses-permission android:name="com.majeur.launcher.permission.UPDATE_BADGE" /> <!-- for huawei -->
68-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\05b2fd90e25e22a7c2deb1fd34e637b4\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:5-83
68-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\05b2fd90e25e22a7c2deb1fd34e637b4\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:34:22-80
69    <uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />
69-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\05b2fd90e25e22a7c2deb1fd34e637b4\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:5-91
69-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\05b2fd90e25e22a7c2deb1fd34e637b4\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:37:22-88
70    <uses-permission android:name="com.huawei.android.launcher.permission.READ_SETTINGS" />
70-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\05b2fd90e25e22a7c2deb1fd34e637b4\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:5-92
70-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\05b2fd90e25e22a7c2deb1fd34e637b4\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:38:22-89
71    <uses-permission android:name="com.huawei.android.launcher.permission.WRITE_SETTINGS" /> <!-- for ZUK -->
71-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\05b2fd90e25e22a7c2deb1fd34e637b4\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:5-93
71-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\05b2fd90e25e22a7c2deb1fd34e637b4\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:39:22-90
72    <uses-permission android:name="android.permission.READ_APP_BADGE" /> <!-- for OPPO -->
72-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\05b2fd90e25e22a7c2deb1fd34e637b4\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:5-73
72-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\05b2fd90e25e22a7c2deb1fd34e637b4\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:42:22-70
73    <uses-permission android:name="com.oppo.launcher.permission.READ_SETTINGS" />
73-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\05b2fd90e25e22a7c2deb1fd34e637b4\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:5-82
73-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\05b2fd90e25e22a7c2deb1fd34e637b4\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:45:22-79
74    <uses-permission android:name="com.oppo.launcher.permission.WRITE_SETTINGS" /> <!-- for EvMe -->
74-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\05b2fd90e25e22a7c2deb1fd34e637b4\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:5-83
74-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\05b2fd90e25e22a7c2deb1fd34e637b4\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:46:22-80
75    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_READ" />
75-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\05b2fd90e25e22a7c2deb1fd34e637b4\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:5-88
75-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\05b2fd90e25e22a7c2deb1fd34e637b4\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:49:22-85
76    <uses-permission android:name="me.everything.badger.permission.BADGE_COUNT_WRITE" />
76-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\05b2fd90e25e22a7c2deb1fd34e637b4\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:5-89
76-->[me.leolin:ShortcutBadger:1.1.22] C:\Users\<USER>\.gradle\caches\8.13\transforms\05b2fd90e25e22a7c2deb1fd34e637b4\transformed\ShortcutBadger-1.1.22\AndroidManifest.xml:50:22-86
77
78    <application
78-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:20:3-36:17
79        android:name="co.auter.hcorp.MainApplication"
79-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:20:16-47
80        android:allowBackup="true"
80-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:20:162-188
81        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
81-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\aabce3f69efc1e684aed892eac0d7652\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
82        android:debuggable="true"
83        android:extractNativeLibs="false"
84        android:icon="@mipmap/ic_launcher"
84-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:20:81-115
85        android:label="@string/app_name"
85-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:20:48-80
86        android:roundIcon="@mipmap/ic_launcher_round"
86-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:20:116-161
87        android:supportsRtl="true"
87-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:20:221-247
88        android:theme="@style/AppTheme"
88-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:20:189-220
89        android:usesCleartextTraffic="true" >
89-->D:\PROJECTS\ORDERS\driver\android\app\src\debug\AndroidManifest.xml:6:18-53
90        <meta-data
90-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:21:5-83
91            android:name="expo.modules.updates.ENABLED"
91-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:21:16-59
92            android:value="false" />
92-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:21:60-81
93        <meta-data
93-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:22:5-105
94            android:name="expo.modules.updates.EXPO_UPDATES_CHECK_ON_LAUNCH"
94-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:22:16-80
95            android:value="ALWAYS" />
95-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:22:81-103
96        <meta-data
96-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:23:5-99
97            android:name="expo.modules.updates.EXPO_UPDATES_LAUNCH_WAIT_MS"
97-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:23:16-79
98            android:value="0" />
98-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:23:80-97
99
100        <activity
100-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:24:5-35:16
101            android:name="co.auter.hcorp.MainActivity"
101-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:24:15-43
102            android:configChanges="keyboard|keyboardHidden|orientation|screenSize|screenLayout|uiMode"
102-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:24:44-134
103            android:exported="true"
103-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:24:256-279
104            android:launchMode="singleTask"
104-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:24:135-166
105            android:screenOrientation="portrait"
105-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:24:280-316
106            android:theme="@style/Theme.App.SplashScreen"
106-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:24:210-255
107            android:windowSoftInputMode="adjustResize" >
107-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:24:167-209
108            <intent-filter>
108-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:25:7-28:23
109                <action android:name="android.intent.action.MAIN" />
109-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:26:9-60
109-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:26:17-58
110
111                <category android:name="android.intent.category.LAUNCHER" />
111-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:27:9-68
111-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:27:19-66
112            </intent-filter>
113            <intent-filter>
113-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:29:7-34:23
114                <action android:name="android.intent.action.VIEW" />
114-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:15:7-58
114-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:15:15-56
115
116                <category android:name="android.intent.category.DEFAULT" />
116-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:31:9-67
116-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:31:19-65
117                <category android:name="android.intent.category.BROWSABLE" />
117-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:16:7-67
117-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:16:17-65
118
119                <data android:scheme="driver" />
119-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:17:7-37
119-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:17:13-35
120            </intent-filter>
121        </activity>
122
123        <provider
123-->[:react-native-webview] D:\PROJECTS\ORDERS\driver\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:8:9-16:20
124            android:name="com.reactnativecommunity.webview.RNCWebViewFileProvider"
124-->[:react-native-webview] D:\PROJECTS\ORDERS\driver\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:13-83
125            android:authorities="co.auter.hcorp.fileprovider"
125-->[:react-native-webview] D:\PROJECTS\ORDERS\driver\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-64
126            android:exported="false"
126-->[:react-native-webview] D:\PROJECTS\ORDERS\driver\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-37
127            android:grantUriPermissions="true" >
127-->[:react-native-webview] D:\PROJECTS\ORDERS\driver\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-47
128            <meta-data
128-->[:react-native-webview] D:\PROJECTS\ORDERS\driver\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
129                android:name="android.support.FILE_PROVIDER_PATHS"
129-->[:react-native-webview] D:\PROJECTS\ORDERS\driver\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
130                android:resource="@xml/file_provider_paths" />
130-->[:react-native-webview] D:\PROJECTS\ORDERS\driver\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
131        </provider> <!-- Disable crashlytics by default so we can custom init with CrashlyticsNdk support -->
132        <meta-data
132-->[:react-native-firebase_crashlytics] D:\PROJECTS\ORDERS\driver\node_modules\@react-native-firebase\crashlytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:9-12:37
133            android:name="firebase_crashlytics_collection_enabled"
133-->[:react-native-firebase_crashlytics] D:\PROJECTS\ORDERS\driver\node_modules\@react-native-firebase\crashlytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-67
134            android:value="false" />
134-->[:react-native-firebase_crashlytics] D:\PROJECTS\ORDERS\driver\node_modules\@react-native-firebase\crashlytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:13-34
135
136        <provider
136-->[:react-native-firebase_crashlytics] D:\PROJECTS\ORDERS\driver\node_modules\@react-native-firebase\crashlytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:9-18:38
137            android:name="io.invertase.firebase.crashlytics.ReactNativeFirebaseCrashlyticsInitProvider"
137-->[:react-native-firebase_crashlytics] D:\PROJECTS\ORDERS\driver\node_modules\@react-native-firebase\crashlytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-104
138            android:authorities="co.auter.hcorp.reactnativefirebasecrashlyticsinitprovider"
138-->[:react-native-firebase_crashlytics] D:\PROJECTS\ORDERS\driver\node_modules\@react-native-firebase\crashlytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-94
139            android:exported="false"
139-->[:react-native-firebase_crashlytics] D:\PROJECTS\ORDERS\driver\node_modules\@react-native-firebase\crashlytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:17:13-37
140            android:initOrder="98" />
140-->[:react-native-firebase_crashlytics] D:\PROJECTS\ORDERS\driver\node_modules\@react-native-firebase\crashlytics\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-35
141
142        <meta-data
142-->[:react-native-firebase_app] D:\PROJECTS\ORDERS\driver\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:36
143            android:name="app_data_collection_default_enabled"
143-->[:react-native-firebase_app] D:\PROJECTS\ORDERS\driver\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-63
144            android:value="true" />
144-->[:react-native-firebase_app] D:\PROJECTS\ORDERS\driver\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-33
145
146        <service
146-->[:react-native-firebase_app] D:\PROJECTS\ORDERS\driver\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:9-21:19
147            android:name="com.google.firebase.components.ComponentDiscoveryService"
147-->[:react-native-firebase_app] D:\PROJECTS\ORDERS\driver\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-84
148            android:directBootAware="true"
148-->[:react-native-firebase_app] D:\PROJECTS\ORDERS\driver\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:13-43
149            android:exported="false" >
149-->[:react-native-firebase_app] D:\PROJECTS\ORDERS\driver\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:16:13-37
150            <meta-data
150-->[:react-native-firebase_app] D:\PROJECTS\ORDERS\driver\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:18:13-20:85
151                android:name="com.google.firebase.components:io.invertase.firebase.app.ReactNativeFirebaseAppRegistrar"
151-->[:react-native-firebase_app] D:\PROJECTS\ORDERS\driver\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:19:17-120
152                android:value="com.google.firebase.components.ComponentRegistrar" />
152-->[:react-native-firebase_app] D:\PROJECTS\ORDERS\driver\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:20:17-82
153            <meta-data
153-->[com.google.firebase:firebase-crashlytics-ndk:20.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e8e05ae67070cf8e094b1f271748d35\transformed\firebase-crashlytics-ndk-20.0.0\AndroidManifest.xml:32:13-34:85
154                android:name="com.google.firebase.components:com.google.firebase.crashlytics.ndk.CrashlyticsNdkRegistrar"
154-->[com.google.firebase:firebase-crashlytics-ndk:20.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e8e05ae67070cf8e094b1f271748d35\transformed\firebase-crashlytics-ndk-20.0.0\AndroidManifest.xml:33:17-122
155                android:value="com.google.firebase.components.ComponentRegistrar" />
155-->[com.google.firebase:firebase-crashlytics-ndk:20.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\1e8e05ae67070cf8e094b1f271748d35\transformed\firebase-crashlytics-ndk-20.0.0\AndroidManifest.xml:34:17-82
156            <meta-data
156-->[com.google.firebase:firebase-crashlytics:20.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\49e07a6d149d84a56b3d37c19168b5e4\transformed\firebase-crashlytics-20.0.0\AndroidManifest.xml:15:13-17:85
157                android:name="com.google.firebase.components:com.google.firebase.crashlytics.FirebaseCrashlyticsKtxRegistrar"
157-->[com.google.firebase:firebase-crashlytics:20.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\49e07a6d149d84a56b3d37c19168b5e4\transformed\firebase-crashlytics-20.0.0\AndroidManifest.xml:16:17-126
158                android:value="com.google.firebase.components.ComponentRegistrar" />
158-->[com.google.firebase:firebase-crashlytics:20.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\49e07a6d149d84a56b3d37c19168b5e4\transformed\firebase-crashlytics-20.0.0\AndroidManifest.xml:17:17-82
159            <meta-data
159-->[com.google.firebase:firebase-crashlytics:20.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\49e07a6d149d84a56b3d37c19168b5e4\transformed\firebase-crashlytics-20.0.0\AndroidManifest.xml:18:13-20:85
160                android:name="com.google.firebase.components:com.google.firebase.crashlytics.CrashlyticsRegistrar"
160-->[com.google.firebase:firebase-crashlytics:20.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\49e07a6d149d84a56b3d37c19168b5e4\transformed\firebase-crashlytics-20.0.0\AndroidManifest.xml:19:17-115
161                android:value="com.google.firebase.components.ComponentRegistrar" />
161-->[com.google.firebase:firebase-crashlytics:20.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\49e07a6d149d84a56b3d37c19168b5e4\transformed\firebase-crashlytics-20.0.0\AndroidManifest.xml:20:17-82
162            <meta-data
162-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb053893dfd80c0208d2029bf61dadd3\transformed\firebase-messaging-25.0.0\AndroidManifest.xml:57:13-59:85
163                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingKtxRegistrar"
163-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb053893dfd80c0208d2029bf61dadd3\transformed\firebase-messaging-25.0.0\AndroidManifest.xml:58:17-122
164                android:value="com.google.firebase.components.ComponentRegistrar" />
164-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb053893dfd80c0208d2029bf61dadd3\transformed\firebase-messaging-25.0.0\AndroidManifest.xml:59:17-82
165            <meta-data
165-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb053893dfd80c0208d2029bf61dadd3\transformed\firebase-messaging-25.0.0\AndroidManifest.xml:60:13-62:85
166                android:name="com.google.firebase.components:com.google.firebase.messaging.FirebaseMessagingRegistrar"
166-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb053893dfd80c0208d2029bf61dadd3\transformed\firebase-messaging-25.0.0\AndroidManifest.xml:61:17-119
167                android:value="com.google.firebase.components.ComponentRegistrar" />
167-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb053893dfd80c0208d2029bf61dadd3\transformed\firebase-messaging-25.0.0\AndroidManifest.xml:62:17-82
168            <meta-data
168-->[com.google.firebase:firebase-sessions:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\861328eb967e67bde503b1b08baf4685\transformed\firebase-sessions-3.0.0\AndroidManifest.xml:25:13-27:85
169                android:name="com.google.firebase.components:com.google.firebase.sessions.FirebaseSessionsRegistrar"
169-->[com.google.firebase:firebase-sessions:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\861328eb967e67bde503b1b08baf4685\transformed\firebase-sessions-3.0.0\AndroidManifest.xml:26:17-117
170                android:value="com.google.firebase.components.ComponentRegistrar" />
170-->[com.google.firebase:firebase-sessions:3.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\861328eb967e67bde503b1b08baf4685\transformed\firebase-sessions-3.0.0\AndroidManifest.xml:27:17-82
171            <meta-data
171-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42301e3cf0e8efa2b2485f84831eb5af\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:25:13-27:85
172                android:name="com.google.firebase.components:com.google.firebase.datatransport.TransportRegistrar"
172-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42301e3cf0e8efa2b2485f84831eb5af\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:26:17-115
173                android:value="com.google.firebase.components.ComponentRegistrar" />
173-->[com.google.firebase:firebase-datatransport:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\42301e3cf0e8efa2b2485f84831eb5af\transformed\firebase-datatransport-19.0.0\AndroidManifest.xml:27:17-82
174            <meta-data
174-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\94921a4d9a4028612659c771a37a3c23\transformed\firebase-auth-24.0.1\AndroidManifest.xml:69:13-71:85
175                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
175-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\94921a4d9a4028612659c771a37a3c23\transformed\firebase-auth-24.0.1\AndroidManifest.xml:70:17-109
176                android:value="com.google.firebase.components.ComponentRegistrar" />
176-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\94921a4d9a4028612659c771a37a3c23\transformed\firebase-auth-24.0.1\AndroidManifest.xml:71:17-82
177            <meta-data
177-->[com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b447e0ede44fb5d0bfbff97c89875b1\transformed\firebase-installations-19.0.0\AndroidManifest.xml:15:13-17:85
178                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
178-->[com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b447e0ede44fb5d0bfbff97c89875b1\transformed\firebase-installations-19.0.0\AndroidManifest.xml:16:17-130
179                android:value="com.google.firebase.components.ComponentRegistrar" />
179-->[com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b447e0ede44fb5d0bfbff97c89875b1\transformed\firebase-installations-19.0.0\AndroidManifest.xml:17:17-82
180            <meta-data
180-->[com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b447e0ede44fb5d0bfbff97c89875b1\transformed\firebase-installations-19.0.0\AndroidManifest.xml:18:13-20:85
181                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
181-->[com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b447e0ede44fb5d0bfbff97c89875b1\transformed\firebase-installations-19.0.0\AndroidManifest.xml:19:17-127
182                android:value="com.google.firebase.components.ComponentRegistrar" />
182-->[com.google.firebase:firebase-installations:19.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6b447e0ede44fb5d0bfbff97c89875b1\transformed\firebase-installations-19.0.0\AndroidManifest.xml:20:17-82
183            <meta-data
183-->[com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\83bcdcd1a4185502f68fcb8b6dd4be22\transformed\firebase-common-22.0.0\AndroidManifest.xml:35:13-37:85
184                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
184-->[com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\83bcdcd1a4185502f68fcb8b6dd4be22\transformed\firebase-common-22.0.0\AndroidManifest.xml:36:17-109
185                android:value="com.google.firebase.components.ComponentRegistrar" />
185-->[com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\83bcdcd1a4185502f68fcb8b6dd4be22\transformed\firebase-common-22.0.0\AndroidManifest.xml:37:17-82
186        </service>
187
188        <provider
188-->[:react-native-firebase_app] D:\PROJECTS\ORDERS\driver\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:23:9-27:38
189            android:name="io.invertase.firebase.app.ReactNativeFirebaseAppInitProvider"
189-->[:react-native-firebase_app] D:\PROJECTS\ORDERS\driver\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:24:13-88
190            android:authorities="co.auter.hcorp.reactnativefirebaseappinitprovider"
190-->[:react-native-firebase_app] D:\PROJECTS\ORDERS\driver\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:25:13-86
191            android:exported="false"
191-->[:react-native-firebase_app] D:\PROJECTS\ORDERS\driver\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:26:13-37
192            android:initOrder="99" />
192-->[:react-native-firebase_app] D:\PROJECTS\ORDERS\driver\node_modules\@react-native-firebase\app\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:27:13-35
193
194        <meta-data
194-->[:expo-modules-core] D:\PROJECTS\ORDERS\driver\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:9:9-11:89
195            android:name="org.unimodules.core.AppLoader#react-native-headless"
195-->[:expo-modules-core] D:\PROJECTS\ORDERS\driver\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:10:13-79
196            android:value="expo.modules.adapters.react.apploader.RNHeadlessAppLoader" />
196-->[:expo-modules-core] D:\PROJECTS\ORDERS\driver\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:11:13-86
197        <meta-data
197-->[:expo-modules-core] D:\PROJECTS\ORDERS\driver\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:12:9-15:45
198            android:name="com.facebook.soloader.enabled"
198-->[:expo-modules-core] D:\PROJECTS\ORDERS\driver\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-57
199            android:value="true" />
199-->[:expo-modules-core] D:\PROJECTS\ORDERS\driver\node_modules\expo-modules-core\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:13-33
200
201        <activity
201-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3dbf06d81207983406d2bec1cfbc8a3\transformed\react-android-0.79.5-debug\AndroidManifest.xml:19:9-21:40
202            android:name="com.facebook.react.devsupport.DevSettingsActivity"
202-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3dbf06d81207983406d2bec1cfbc8a3\transformed\react-android-0.79.5-debug\AndroidManifest.xml:20:13-77
203            android:exported="false" />
203-->[com.facebook.react:react-android:0.79.5] C:\Users\<USER>\.gradle\caches\8.13\transforms\d3dbf06d81207983406d2bec1cfbc8a3\transformed\react-android-0.79.5-debug\AndroidManifest.xml:21:13-37
204
205        <provider
205-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\0ec2b2613645c132734cc6d66ce0081b\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:21:9-30:20
206            android:name="expo.modules.filesystem.FileSystemFileProvider"
206-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\0ec2b2613645c132734cc6d66ce0081b\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:22:13-74
207            android:authorities="co.auter.hcorp.FileSystemFileProvider"
207-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\0ec2b2613645c132734cc6d66ce0081b\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:23:13-74
208            android:exported="false"
208-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\0ec2b2613645c132734cc6d66ce0081b\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:24:13-37
209            android:grantUriPermissions="true" >
209-->[host.exp.exponent:expo.modules.filesystem:18.1.11] C:\Users\<USER>\.gradle\caches\8.13\transforms\0ec2b2613645c132734cc6d66ce0081b\transformed\expo.modules.filesystem-18.1.11\AndroidManifest.xml:25:13-47
210            <meta-data
210-->[:react-native-webview] D:\PROJECTS\ORDERS\driver\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:13:13-15:63
211                android:name="android.support.FILE_PROVIDER_PATHS"
211-->[:react-native-webview] D:\PROJECTS\ORDERS\driver\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:14:17-67
212                android:resource="@xml/file_system_provider_paths" />
212-->[:react-native-webview] D:\PROJECTS\ORDERS\driver\node_modules\react-native-webview\android\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:15:17-60
213        </provider>
214
215        <service
215-->[host.exp.exponent:expo.modules.location:18.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\87f1ddbd194f17880f16a692d9d1ae7e\transformed\expo.modules.location-18.1.6\AndroidManifest.xml:11:9-14:56
216            android:name="expo.modules.location.services.LocationTaskService"
216-->[host.exp.exponent:expo.modules.location:18.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\87f1ddbd194f17880f16a692d9d1ae7e\transformed\expo.modules.location-18.1.6\AndroidManifest.xml:12:13-78
217            android:exported="false"
217-->[host.exp.exponent:expo.modules.location:18.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\87f1ddbd194f17880f16a692d9d1ae7e\transformed\expo.modules.location-18.1.6\AndroidManifest.xml:13:13-37
218            android:foregroundServiceType="location" />
218-->[host.exp.exponent:expo.modules.location:18.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\87f1ddbd194f17880f16a692d9d1ae7e\transformed\expo.modules.location-18.1.6\AndroidManifest.xml:14:13-53
219        <service
219-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:11:9-17:19
220            android:name="expo.modules.notifications.service.ExpoFirebaseMessagingService"
220-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:12:13-91
221            android:exported="false" >
221-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:13:13-37
222            <intent-filter android:priority="-1" >
222-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:14:13-16:29
222-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:14:28-49
223                <action android:name="com.google.firebase.MESSAGING_EVENT" />
223-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:15:17-78
223-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:15:25-75
224            </intent-filter>
225        </service>
226
227        <receiver
227-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:19:9-31:20
228            android:name="expo.modules.notifications.service.NotificationsService"
228-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:20:13-83
229            android:enabled="true"
229-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:21:13-35
230            android:exported="false" >
230-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:22:13-37
231            <intent-filter android:priority="-1" >
231-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:23:13-30:29
231-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:23:28-49
232                <action android:name="expo.modules.notifications.NOTIFICATION_EVENT" />
232-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:24:17-88
232-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:24:25-85
233                <action android:name="android.intent.action.BOOT_COMPLETED" />
233-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:25:17-79
233-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:25:25-76
234                <action android:name="android.intent.action.REBOOT" />
234-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:26:17-71
234-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:26:25-68
235                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
235-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:27:17-82
235-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:27:25-79
236                <action android:name="com.htc.intent.action.QUICKBOOT_POWERON" />
236-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:28:17-82
236-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:28:25-79
237                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
237-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:29:17-84
237-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:29:25-81
238            </intent-filter>
239        </receiver>
240
241        <activity
241-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:33:9-40:75
242            android:name="expo.modules.notifications.service.NotificationForwarderActivity"
242-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:34:13-92
243            android:excludeFromRecents="true"
243-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:35:13-46
244            android:exported="false"
244-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:36:13-37
245            android:launchMode="standard"
245-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:37:13-42
246            android:noHistory="true"
246-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:38:13-37
247            android:taskAffinity=""
247-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:39:13-36
248            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
248-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:40:13-72
249
250        <receiver
250-->[host.exp.exponent:expo.modules.taskmanager:13.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e4598a00e4a7307049b735104d3ca0a\transformed\expo.modules.taskmanager-13.1.6\AndroidManifest.xml:8:9-16:20
251            android:name="expo.modules.taskManager.TaskBroadcastReceiver"
251-->[host.exp.exponent:expo.modules.taskmanager:13.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e4598a00e4a7307049b735104d3ca0a\transformed\expo.modules.taskmanager-13.1.6\AndroidManifest.xml:9:13-74
252            android:exported="false" >
252-->[host.exp.exponent:expo.modules.taskmanager:13.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e4598a00e4a7307049b735104d3ca0a\transformed\expo.modules.taskmanager-13.1.6\AndroidManifest.xml:10:13-37
253            <intent-filter>
253-->[host.exp.exponent:expo.modules.taskmanager:13.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e4598a00e4a7307049b735104d3ca0a\transformed\expo.modules.taskmanager-13.1.6\AndroidManifest.xml:11:13-15:29
254                <action android:name="expo.modules.taskManager.TaskBroadcastReceiver.INTENT_ACTION" />
254-->[host.exp.exponent:expo.modules.taskmanager:13.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e4598a00e4a7307049b735104d3ca0a\transformed\expo.modules.taskmanager-13.1.6\AndroidManifest.xml:12:17-103
254-->[host.exp.exponent:expo.modules.taskmanager:13.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e4598a00e4a7307049b735104d3ca0a\transformed\expo.modules.taskmanager-13.1.6\AndroidManifest.xml:12:25-100
255                <action android:name="android.intent.action.BOOT_COMPLETED" />
255-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:25:17-79
255-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:25:25-76
256                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
256-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:29:17-84
256-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:29:25-81
257            </intent-filter>
258        </receiver>
259
260        <service
260-->[host.exp.exponent:expo.modules.taskmanager:13.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e4598a00e4a7307049b735104d3ca0a\transformed\expo.modules.taskmanager-13.1.6\AndroidManifest.xml:18:9-22:72
261            android:name="expo.modules.taskManager.TaskJobService"
261-->[host.exp.exponent:expo.modules.taskmanager:13.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e4598a00e4a7307049b735104d3ca0a\transformed\expo.modules.taskmanager-13.1.6\AndroidManifest.xml:19:13-67
262            android:enabled="true"
262-->[host.exp.exponent:expo.modules.taskmanager:13.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e4598a00e4a7307049b735104d3ca0a\transformed\expo.modules.taskmanager-13.1.6\AndroidManifest.xml:20:13-35
263            android:exported="false"
263-->[host.exp.exponent:expo.modules.taskmanager:13.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e4598a00e4a7307049b735104d3ca0a\transformed\expo.modules.taskmanager-13.1.6\AndroidManifest.xml:21:13-37
264            android:permission="android.permission.BIND_JOB_SERVICE" />
264-->[host.exp.exponent:expo.modules.taskmanager:13.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e4598a00e4a7307049b735104d3ca0a\transformed\expo.modules.taskmanager-13.1.6\AndroidManifest.xml:22:13-69
265
266        <meta-data
266-->[host.exp.exponent:expo.modules.taskmanager:13.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e4598a00e4a7307049b735104d3ca0a\transformed\expo.modules.taskmanager-13.1.6\AndroidManifest.xml:24:9-26:36
267            android:name="expo.modules.taskManager.oneAppId"
267-->[host.exp.exponent:expo.modules.taskmanager:13.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e4598a00e4a7307049b735104d3ca0a\transformed\expo.modules.taskmanager-13.1.6\AndroidManifest.xml:25:13-61
268            android:value="true" />
268-->[host.exp.exponent:expo.modules.taskmanager:13.1.6] C:\Users\<USER>\.gradle\caches\8.13\transforms\8e4598a00e4a7307049b735104d3ca0a\transformed\expo.modules.taskmanager-13.1.6\AndroidManifest.xml:26:13-33
269
270        <receiver
270-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb053893dfd80c0208d2029bf61dadd3\transformed\firebase-messaging-25.0.0\AndroidManifest.xml:29:9-40:20
271            android:name="com.google.firebase.iid.FirebaseInstanceIdReceiver"
271-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb053893dfd80c0208d2029bf61dadd3\transformed\firebase-messaging-25.0.0\AndroidManifest.xml:30:13-78
272            android:exported="true"
272-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb053893dfd80c0208d2029bf61dadd3\transformed\firebase-messaging-25.0.0\AndroidManifest.xml:31:13-36
273            android:permission="com.google.android.c2dm.permission.SEND" >
273-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb053893dfd80c0208d2029bf61dadd3\transformed\firebase-messaging-25.0.0\AndroidManifest.xml:32:13-73
274            <intent-filter>
274-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb053893dfd80c0208d2029bf61dadd3\transformed\firebase-messaging-25.0.0\AndroidManifest.xml:33:13-35:29
275                <action android:name="com.google.android.c2dm.intent.RECEIVE" />
275-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb053893dfd80c0208d2029bf61dadd3\transformed\firebase-messaging-25.0.0\AndroidManifest.xml:34:17-81
275-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb053893dfd80c0208d2029bf61dadd3\transformed\firebase-messaging-25.0.0\AndroidManifest.xml:34:25-78
276            </intent-filter>
277
278            <meta-data
278-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb053893dfd80c0208d2029bf61dadd3\transformed\firebase-messaging-25.0.0\AndroidManifest.xml:37:13-39:40
279                android:name="com.google.android.gms.cloudmessaging.FINISHED_AFTER_HANDLED"
279-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb053893dfd80c0208d2029bf61dadd3\transformed\firebase-messaging-25.0.0\AndroidManifest.xml:38:17-92
280                android:value="true" />
280-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb053893dfd80c0208d2029bf61dadd3\transformed\firebase-messaging-25.0.0\AndroidManifest.xml:39:17-37
281        </receiver>
282        <!--
283             FirebaseMessagingService performs security checks at runtime,
284             but set to not exported to explicitly avoid allowing another app to call it.
285        -->
286        <service
286-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb053893dfd80c0208d2029bf61dadd3\transformed\firebase-messaging-25.0.0\AndroidManifest.xml:46:9-53:19
287            android:name="com.google.firebase.messaging.FirebaseMessagingService"
287-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb053893dfd80c0208d2029bf61dadd3\transformed\firebase-messaging-25.0.0\AndroidManifest.xml:47:13-82
288            android:directBootAware="true"
288-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb053893dfd80c0208d2029bf61dadd3\transformed\firebase-messaging-25.0.0\AndroidManifest.xml:48:13-43
289            android:exported="false" >
289-->[com.google.firebase:firebase-messaging:25.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eb053893dfd80c0208d2029bf61dadd3\transformed\firebase-messaging-25.0.0\AndroidManifest.xml:49:13-37
290            <intent-filter android:priority="-500" >
290-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:14:13-16:29
290-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:14:28-49
291                <action android:name="com.google.firebase.MESSAGING_EVENT" />
291-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:15:17-78
291-->[host.exp.exponent:expo.modules.notifications:0.31.4] C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\AndroidManifest.xml:15:25-75
292            </intent-filter>
293        </service>
294        <service
294-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\621a99157695b6c3f36ffb3a28eb4931\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:26:9-32:19
295            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
295-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\621a99157695b6c3f36ffb3a28eb4931\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:27:13-103
296            android:exported="false" >
296-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\621a99157695b6c3f36ffb3a28eb4931\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:28:13-37
297            <meta-data
297-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\621a99157695b6c3f36ffb3a28eb4931\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:29:13-31:39
298                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
298-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\621a99157695b6c3f36ffb3a28eb4931\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:30:17-94
299                android:value="cct" />
299-->[com.google.android.datatransport:transport-backend-cct:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\621a99157695b6c3f36ffb3a28eb4931\transformed\transport-backend-cct-3.3.0\AndroidManifest.xml:31:17-36
300        </service>
301        <service
301-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\443563b72932f48155563e107642094d\transformed\room-runtime-2.6.1\AndroidManifest.xml:24:9-28:63
302            android:name="androidx.room.MultiInstanceInvalidationService"
302-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\443563b72932f48155563e107642094d\transformed\room-runtime-2.6.1\AndroidManifest.xml:25:13-74
303            android:directBootAware="true"
303-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\443563b72932f48155563e107642094d\transformed\room-runtime-2.6.1\AndroidManifest.xml:26:13-43
304            android:exported="false" />
304-->[androidx.room:room-runtime:2.6.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\443563b72932f48155563e107642094d\transformed\room-runtime-2.6.1\AndroidManifest.xml:27:13-37
305
306        <activity
306-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\94921a4d9a4028612659c771a37a3c23\transformed\firebase-auth-24.0.1\AndroidManifest.xml:29:9-46:20
307            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
307-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\94921a4d9a4028612659c771a37a3c23\transformed\firebase-auth-24.0.1\AndroidManifest.xml:30:13-80
308            android:excludeFromRecents="true"
308-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\94921a4d9a4028612659c771a37a3c23\transformed\firebase-auth-24.0.1\AndroidManifest.xml:31:13-46
309            android:exported="true"
309-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\94921a4d9a4028612659c771a37a3c23\transformed\firebase-auth-24.0.1\AndroidManifest.xml:32:13-36
310            android:launchMode="singleTask"
310-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\94921a4d9a4028612659c771a37a3c23\transformed\firebase-auth-24.0.1\AndroidManifest.xml:33:13-44
311            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
311-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\94921a4d9a4028612659c771a37a3c23\transformed\firebase-auth-24.0.1\AndroidManifest.xml:34:13-72
312            <intent-filter>
312-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\94921a4d9a4028612659c771a37a3c23\transformed\firebase-auth-24.0.1\AndroidManifest.xml:35:13-45:29
313                <action android:name="android.intent.action.VIEW" />
313-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:15:7-58
313-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:15:15-56
314
315                <category android:name="android.intent.category.DEFAULT" />
315-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:31:9-67
315-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:31:19-65
316                <category android:name="android.intent.category.BROWSABLE" />
316-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:16:7-67
316-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:16:17-65
317
318                <data
318-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:17:7-37
319                    android:host="firebase.auth"
320                    android:path="/"
321                    android:scheme="genericidp" />
321-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:17:13-35
322            </intent-filter>
323        </activity>
324        <activity
324-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\94921a4d9a4028612659c771a37a3c23\transformed\firebase-auth-24.0.1\AndroidManifest.xml:47:9-64:20
325            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
325-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\94921a4d9a4028612659c771a37a3c23\transformed\firebase-auth-24.0.1\AndroidManifest.xml:48:13-79
326            android:excludeFromRecents="true"
326-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\94921a4d9a4028612659c771a37a3c23\transformed\firebase-auth-24.0.1\AndroidManifest.xml:49:13-46
327            android:exported="true"
327-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\94921a4d9a4028612659c771a37a3c23\transformed\firebase-auth-24.0.1\AndroidManifest.xml:50:13-36
328            android:launchMode="singleTask"
328-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\94921a4d9a4028612659c771a37a3c23\transformed\firebase-auth-24.0.1\AndroidManifest.xml:51:13-44
329            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
329-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\94921a4d9a4028612659c771a37a3c23\transformed\firebase-auth-24.0.1\AndroidManifest.xml:52:13-72
330            <intent-filter>
330-->[com.google.firebase:firebase-auth:24.0.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\94921a4d9a4028612659c771a37a3c23\transformed\firebase-auth-24.0.1\AndroidManifest.xml:53:13-63:29
331                <action android:name="android.intent.action.VIEW" />
331-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:15:7-58
331-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:15:15-56
332
333                <category android:name="android.intent.category.DEFAULT" />
333-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:31:9-67
333-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:31:19-65
334                <category android:name="android.intent.category.BROWSABLE" />
334-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:16:7-67
334-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:16:17-65
335
336                <data
336-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:17:7-37
337                    android:host="firebase.auth"
338                    android:path="/"
339                    android:scheme="recaptcha" />
339-->D:\PROJECTS\ORDERS\driver\android\app\src\main\AndroidManifest.xml:17:13-35
340            </intent-filter>
341        </activity>
342
343        <service
343-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\3eb942687d01e04e9e71a24476541a9b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
344            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
344-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\3eb942687d01e04e9e71a24476541a9b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
345            android:enabled="true"
345-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\3eb942687d01e04e9e71a24476541a9b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
346            android:exported="false" >
346-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\3eb942687d01e04e9e71a24476541a9b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
347            <meta-data
347-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\3eb942687d01e04e9e71a24476541a9b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
348                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
348-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\3eb942687d01e04e9e71a24476541a9b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
349                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
349-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\3eb942687d01e04e9e71a24476541a9b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
350        </service>
351
352        <activity
352-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\3eb942687d01e04e9e71a24476541a9b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
353            android:name="androidx.credentials.playservices.HiddenActivity"
353-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\3eb942687d01e04e9e71a24476541a9b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
354            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
354-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\3eb942687d01e04e9e71a24476541a9b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
355            android:enabled="true"
355-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\3eb942687d01e04e9e71a24476541a9b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
356            android:exported="false"
356-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\3eb942687d01e04e9e71a24476541a9b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
357            android:fitsSystemWindows="true"
357-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\3eb942687d01e04e9e71a24476541a9b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
358            android:theme="@style/Theme.Hidden" >
358-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.13\transforms\3eb942687d01e04e9e71a24476541a9b\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
359        </activity>
360
361        <meta-data
361-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d239b6202d56858cacb9c0e3df74064e\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:11:9-13:43
362            android:name="com.bumptech.glide.integration.okhttp3.OkHttpGlideModule"
362-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d239b6202d56858cacb9c0e3df74064e\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:12:13-84
363            android:value="GlideModule" />
363-->[com.github.bumptech.glide:okhttp3-integration:4.11.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\d239b6202d56858cacb9c0e3df74064e\transformed\okhttp3-integration-4.11.0\AndroidManifest.xml:13:13-40
364
365        <provider
365-->[com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\83bcdcd1a4185502f68fcb8b6dd4be22\transformed\firebase-common-22.0.0\AndroidManifest.xml:23:9-28:39
366            android:name="com.google.firebase.provider.FirebaseInitProvider"
366-->[com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\83bcdcd1a4185502f68fcb8b6dd4be22\transformed\firebase-common-22.0.0\AndroidManifest.xml:24:13-77
367            android:authorities="co.auter.hcorp.firebaseinitprovider"
367-->[com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\83bcdcd1a4185502f68fcb8b6dd4be22\transformed\firebase-common-22.0.0\AndroidManifest.xml:25:13-72
368            android:directBootAware="true"
368-->[com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\83bcdcd1a4185502f68fcb8b6dd4be22\transformed\firebase-common-22.0.0\AndroidManifest.xml:26:13-43
369            android:exported="false"
369-->[com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\83bcdcd1a4185502f68fcb8b6dd4be22\transformed\firebase-common-22.0.0\AndroidManifest.xml:27:13-37
370            android:initOrder="100" />
370-->[com.google.firebase:firebase-common:22.0.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\83bcdcd1a4185502f68fcb8b6dd4be22\transformed\firebase-common-22.0.0\AndroidManifest.xml:28:13-36
371
372        <activity
372-->[com.google.android.gms:play-services-auth:21.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ecb085eabbca858f83719bb406d133e\transformed\play-services-auth-21.4.0\AndroidManifest.xml:23:9-27:75
373            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
373-->[com.google.android.gms:play-services-auth:21.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ecb085eabbca858f83719bb406d133e\transformed\play-services-auth-21.4.0\AndroidManifest.xml:24:13-93
374            android:excludeFromRecents="true"
374-->[com.google.android.gms:play-services-auth:21.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ecb085eabbca858f83719bb406d133e\transformed\play-services-auth-21.4.0\AndroidManifest.xml:25:13-46
375            android:exported="false"
375-->[com.google.android.gms:play-services-auth:21.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ecb085eabbca858f83719bb406d133e\transformed\play-services-auth-21.4.0\AndroidManifest.xml:26:13-37
376            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
376-->[com.google.android.gms:play-services-auth:21.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ecb085eabbca858f83719bb406d133e\transformed\play-services-auth-21.4.0\AndroidManifest.xml:27:13-72
377        <!--
378            Service handling Google Sign-In user revocation. For apps that do not integrate with
379            Google Sign-In, this service will never be started.
380        -->
381        <service
381-->[com.google.android.gms:play-services-auth:21.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ecb085eabbca858f83719bb406d133e\transformed\play-services-auth-21.4.0\AndroidManifest.xml:33:9-37:51
382            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
382-->[com.google.android.gms:play-services-auth:21.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ecb085eabbca858f83719bb406d133e\transformed\play-services-auth-21.4.0\AndroidManifest.xml:34:13-89
383            android:exported="true"
383-->[com.google.android.gms:play-services-auth:21.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ecb085eabbca858f83719bb406d133e\transformed\play-services-auth-21.4.0\AndroidManifest.xml:35:13-36
384            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
384-->[com.google.android.gms:play-services-auth:21.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ecb085eabbca858f83719bb406d133e\transformed\play-services-auth-21.4.0\AndroidManifest.xml:36:13-107
385            android:visibleToInstantApps="true" />
385-->[com.google.android.gms:play-services-auth:21.4.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\5ecb085eabbca858f83719bb406d133e\transformed\play-services-auth-21.4.0\AndroidManifest.xml:37:13-48
386
387        <activity
387-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6f32c56df1f09404e35eae2beaed3ad9\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
388            android:name="com.google.android.gms.common.api.GoogleApiActivity"
388-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6f32c56df1f09404e35eae2beaed3ad9\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
389            android:exported="false"
389-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6f32c56df1f09404e35eae2beaed3ad9\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
390            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
390-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6f32c56df1f09404e35eae2beaed3ad9\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
391
392        <provider
392-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eaf3da4e3df11c1fb7af91ceff1b438b\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
393            android:name="androidx.startup.InitializationProvider"
393-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eaf3da4e3df11c1fb7af91ceff1b438b\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
394            android:authorities="co.auter.hcorp.androidx-startup"
394-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eaf3da4e3df11c1fb7af91ceff1b438b\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
395            android:exported="false" >
395-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eaf3da4e3df11c1fb7af91ceff1b438b\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
396            <meta-data
396-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eaf3da4e3df11c1fb7af91ceff1b438b\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
397                android:name="androidx.emoji2.text.EmojiCompatInitializer"
397-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eaf3da4e3df11c1fb7af91ceff1b438b\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
398                android:value="androidx.startup" />
398-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\eaf3da4e3df11c1fb7af91ceff1b438b\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
399            <meta-data
399-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f81b77428bec6ca6b761e4004bb78112\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
400                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
400-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f81b77428bec6ca6b761e4004bb78112\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
401                android:value="androidx.startup" />
401-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.13\transforms\f81b77428bec6ca6b761e4004bb78112\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
402            <meta-data
402-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f056e4e5eaf6441194751899f29f1962\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
403                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
403-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f056e4e5eaf6441194751899f29f1962\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
404                android:value="androidx.startup" />
404-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f056e4e5eaf6441194751899f29f1962\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
405        </provider>
406
407        <meta-data
407-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c840b354751b833ba967f669749ddf08\transformed\play-services-basement-18.5.0\AndroidManifest.xml:21:9-23:69
408            android:name="com.google.android.gms.version"
408-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c840b354751b833ba967f669749ddf08\transformed\play-services-basement-18.5.0\AndroidManifest.xml:22:13-58
409            android:value="@integer/google_play_services_version" />
409-->[com.google.android.gms:play-services-basement:18.5.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\c840b354751b833ba967f669749ddf08\transformed\play-services-basement-18.5.0\AndroidManifest.xml:23:13-66
410
411        <receiver
411-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f056e4e5eaf6441194751899f29f1962\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
412            android:name="androidx.profileinstaller.ProfileInstallReceiver"
412-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f056e4e5eaf6441194751899f29f1962\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
413            android:directBootAware="false"
413-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f056e4e5eaf6441194751899f29f1962\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
414            android:enabled="true"
414-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f056e4e5eaf6441194751899f29f1962\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
415            android:exported="true"
415-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f056e4e5eaf6441194751899f29f1962\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
416            android:permission="android.permission.DUMP" >
416-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f056e4e5eaf6441194751899f29f1962\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
417            <intent-filter>
417-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f056e4e5eaf6441194751899f29f1962\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
418                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
418-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f056e4e5eaf6441194751899f29f1962\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
418-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f056e4e5eaf6441194751899f29f1962\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
419            </intent-filter>
420            <intent-filter>
420-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f056e4e5eaf6441194751899f29f1962\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
421                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
421-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f056e4e5eaf6441194751899f29f1962\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
421-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f056e4e5eaf6441194751899f29f1962\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
422            </intent-filter>
423            <intent-filter>
423-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f056e4e5eaf6441194751899f29f1962\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
424                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
424-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f056e4e5eaf6441194751899f29f1962\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
424-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f056e4e5eaf6441194751899f29f1962\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
425            </intent-filter>
426            <intent-filter>
426-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f056e4e5eaf6441194751899f29f1962\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
427                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
427-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f056e4e5eaf6441194751899f29f1962\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
427-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.13\transforms\f056e4e5eaf6441194751899f29f1962\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
428            </intent-filter>
429        </receiver>
430
431        <service
431-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c5865cf41c2e504fc59267a5e787347\transformed\transport-runtime-3.3.0\AndroidManifest.xml:24:9-28:19
432            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
432-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c5865cf41c2e504fc59267a5e787347\transformed\transport-runtime-3.3.0\AndroidManifest.xml:25:13-117
433            android:exported="false"
433-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c5865cf41c2e504fc59267a5e787347\transformed\transport-runtime-3.3.0\AndroidManifest.xml:26:13-37
434            android:permission="android.permission.BIND_JOB_SERVICE" >
434-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c5865cf41c2e504fc59267a5e787347\transformed\transport-runtime-3.3.0\AndroidManifest.xml:27:13-69
435        </service>
436
437        <receiver
437-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c5865cf41c2e504fc59267a5e787347\transformed\transport-runtime-3.3.0\AndroidManifest.xml:30:9-32:40
438            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
438-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c5865cf41c2e504fc59267a5e787347\transformed\transport-runtime-3.3.0\AndroidManifest.xml:31:13-132
439            android:exported="false" /> <!-- The activities will be merged into the manifest of the hosting app. -->
439-->[com.google.android.datatransport:transport-runtime:3.3.0] C:\Users\<USER>\.gradle\caches\8.13\transforms\6c5865cf41c2e504fc59267a5e787347\transformed\transport-runtime-3.3.0\AndroidManifest.xml:32:13-37
440        <activity
440-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0e8fdd275e211cd77bb2a75e22921bc\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
441            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
441-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0e8fdd275e211cd77bb2a75e22921bc\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
442            android:exported="false"
442-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0e8fdd275e211cd77bb2a75e22921bc\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
443            android:stateNotNeeded="true"
443-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0e8fdd275e211cd77bb2a75e22921bc\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
444            android:theme="@style/Theme.PlayCore.Transparent" />
444-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.13\transforms\f0e8fdd275e211cd77bb2a75e22921bc\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
445    </application>
446
447</manifest>

<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config=":expo-updates-interface" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PROJECTS\ORDERS\driver\node_modules\expo-updates-interface\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-structured-headers" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PROJECTS\ORDERS\driver\node_modules\expo-structured-headers\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-json-utils" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PROJECTS\ORDERS\driver\node_modules\expo-json-utils\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-manifests" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PROJECTS\ORDERS\driver\node_modules\expo-manifests\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-eas-client" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PROJECTS\ORDERS\driver\node_modules\expo-eas-client\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo-constants" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PROJECTS\ORDERS\driver\node_modules\expo-constants\android\build\intermediates\library_assets\debug\packageDebugAssets\out"><file name="app.config" path="D:\PROJECTS\ORDERS\driver\node_modules\expo-constants\android\build\intermediates\library_assets\debug\packageDebugAssets\out\app.config"/></source></dataSet><dataSet config=":expo-updates" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PROJECTS\ORDERS\driver\node_modules\expo-updates\android\build\intermediates\library_assets\debug\packageDebugAssets\out"><file name="expo-root.pem" path="D:\PROJECTS\ORDERS\driver\node_modules\expo-updates\android\build\intermediates\library_assets\debug\packageDebugAssets\out\expo-root.pem"/></source></dataSet><dataSet config=":expo-modules-core" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PROJECTS\ORDERS\driver\node_modules\expo-modules-core\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-reanimated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PROJECTS\ORDERS\driver\node_modules\react-native-reanimated\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":expo" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PROJECTS\ORDERS\driver\node_modules\expo\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-firebase_app" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PROJECTS\ORDERS\driver\node_modules\@react-native-firebase\app\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-firebase_crashlytics" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PROJECTS\ORDERS\driver\node_modules\@react-native-firebase\crashlytics\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-firebase_auth" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PROJECTS\ORDERS\driver\node_modules\@react-native-firebase\auth\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-async-storage_async-storage" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PROJECTS\ORDERS\driver\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-edge-to-edge" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PROJECTS\ORDERS\driver\node_modules\react-native-edge-to-edge\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-webview" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PROJECTS\ORDERS\driver\node_modules\react-native-webview\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-screens" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PROJECTS\ORDERS\driver\node_modules\react-native-screens\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-safe-area-context" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PROJECTS\ORDERS\driver\node_modules\react-native-safe-area-context\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config=":react-native-gesture-handler" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PROJECTS\ORDERS\driver\node_modules\react-native-gesture-handler\android\build\intermediates\library_assets\debug\packageDebugAssets\out"/></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PROJECTS\ORDERS\driver\android\app\src\main\assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PROJECTS\ORDERS\driver\android\app\src\debug\assets"/></dataSet><dataSet config="assets-createDebugUpdatesResources" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PROJECTS\ORDERS\driver\android\app\build\generated\assets\createDebugUpdatesResources"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\PROJECTS\ORDERS\driver\android\app\build\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>
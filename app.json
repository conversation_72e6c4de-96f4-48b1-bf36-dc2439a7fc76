{"expo": {"name": "driver", "slug": "driver", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "driver", "userInterfaceStyle": "automatic", "newArchEnabled": true, "ios": {"supportsTablet": true}, "android": {"googleServicesFile": "./google-services.json", "adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "edgeToEdgeEnabled": true, "package": "co.auter.hcorp", "permissions": ["ACCESS_COARSE_LOCATION", "ACCESS_FINE_LOCATION", "ACCESS_BACKGROUND_LOCATION", "FOREGROUND_SERVICE", "FOREGROUND_SERVICE_LOCATION", "INTERNET", "POST_NOTIFICATIONS", "android.permission.POST_NOTIFICATIONS", "android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION", "android.permission.ACCESS_BACKGROUND_LOCATION", "android.permission.FOREGROUND_SERVICE", "android.permission.FOREGROUND_SERVICE_LOCATION"]}, "web": {"bundler": "metro", "output": "static", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", ["expo-splash-screen", {"image": "./assets/images/splash-icon.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}], ["expo-location", {"isAndroidBackgroundLocationEnabled": true, "isAndroidForegroundServiceEnabled": true, "locationAlwaysAndWhenInUsePermission": "Allow $(PRODUCT_NAME) to use your location even in the background."}], "@react-native-firebase/app", "@react-native-firebase/auth", "@react-native-firebase/crashlytics", ["expo-build-properties", {"ios": {"useFrameworks": "static"}}]], "experiments": {"typedRoutes": true}}}
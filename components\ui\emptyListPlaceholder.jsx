import { MaterialCommunityIcons } from '@expo/vector-icons';
import { StyleSheet, View } from 'react-native';
import { Text } from 'react-native-paper';
import { theme } from '../../constants/theme';
export const EmptyListPlaceholder = ({
  title,
  message,
  icon,
  iconDisabled,
  iconSize,
  iconColor,
  style,
  textStyle,
  titleStyle,
  color,
  backgroundColor,
  containerStyle,
}) => {
  return (
    <View
      style={[
        styles.empty_list_placeholder_main,
        {...style, backgroundColor: backgroundColor},
        backgroundColor ? {backgroundColor: backgroundColor} : null,

        color ? {borderColor: color} : null,
      ]}>
      {!iconDisabled && (
        <MaterialCommunityIcons
          name={icon ? icon : 'alert-circle'}
          size={iconSize ? iconSize : 20}
          style={{
            color: iconColor ? iconColor : theme.colors.error,
          }}
        />
      )}
      {title ? (
        <View
          style={{
            flexDirection: 'column',
            justifyContent: 'center',
            alignItems: 'center',
            ...containerStyle,
          }}>
          <Text
            style={[
              styles.empty_list_placeholder_title,
              color ? {color: color} : null,
              {...titleStyle},
            ]}>
            {title}
          </Text>
          <Text
            style={[
              styles.empty_list_placeholder_text,
              color ? {color: color} : null,
              {...textStyle},
            ]}>
            {message}
          </Text>
        </View>
      ) : (
        <Text
          style={[
            styles.empty_list_placeholder_text,
            color ? {color: color} : null,
            {...textStyle},
          ]}>
          {message}
        </Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  empty_list_placeholder_main: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    width: '100%',
    height:'auto'
  },
  empty_list_placeholder_title: {

    flexWrap: 'wrap',
    color: theme.colors.secondary,
    fontFamily: 'Montserrat-Bold',
    fontSize: 12,
    maxWidth: '95%',
    marginBottom: 4,
  },
  empty_list_placeholder_text: {

    flexWrap: 'wrap',
    color: theme.colors.secondary,
    fontFamily: 'Montserrat-Medium',
    fontSize: 12,
    maxWidth: '95%',
  },
});

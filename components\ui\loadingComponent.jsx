import { theme } from '@/constants/theme';
import { ActivityIndicator, StyleSheet, View } from 'react-native';

export const LoadingComponent = () => (
    <View style={[styles.loading_main]}>
        <ActivityIndicator size="large" />
    </View>
);

const styles = StyleSheet.create({
    loading_main: {
        flex: 1,
        justifyContent: 'center',
        flexDirection: 'row',
        justifyContent: 'space-around',
        padding: 10,
        backgroundColor: theme.colors.surface,
    },
});

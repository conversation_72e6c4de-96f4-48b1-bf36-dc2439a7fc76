
export class Driver {
  constructor({
    driverID,
    firebaseID,
    firstName,
    lastName,
    email,
    phoneNumber,
    address1,
    address2,
    isAvailable,
  }) {
    this.driverID = driverID;
    this.firebaseID = firebaseID;
    this.firstName = firstName;
    this.lastName = lastName;
    this.email = email;
    this.phoneNumber = phoneNumber;
    this.address1 = address1;
    this.address2 = address2;
    this.isAvailable = isAvailable;
  }
  isProfileComplete = () => {
    if (this.firstName && this.lastName && this.address1) {
      return true;
    } else {
      return false;
    }
  };
}

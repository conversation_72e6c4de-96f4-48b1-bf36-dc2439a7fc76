<?xml version="1.0" encoding="utf-8"?>
<resources xmlns:ns1="http://schemas.android.com/tools">
    <color name="colorPrimary">#023c69</color>
    <color name="colorPrimaryDark">#ffffff</color>
    <color name="iconBackground">#ffffff</color>
    <color name="splashscreen_background">#ffffff</color>
    <integer name="react_native_dev_server_port">8081</integer>
    <string name="app_name">driver</string>
    <string name="com.google.firebase.crashlytics.mapping_file_id" ns1:ignore="UnusedResources,TypographyDashes" translatable="false">00000000000000000000000000000000</string>
    <string name="com.google.firebase.crashlytics.version_control_info">""</string>
    <string name="expo_splash_screen_resize_mode" translatable="false">contain</string>
    <string name="expo_splash_screen_status_bar_translucent" translatable="false">false</string>
    <string name="expo_system_ui_user_interface_style" translatable="false">automatic</string>
    <string name="gcm_defaultSenderId" translatable="false">691331548785</string>
    <string name="google_api_key" translatable="false">AIzaSyBuHgJY9fPM7dgJ8b2k2yUE_0UzP9c9Z2E</string>
    <string name="google_app_id" translatable="false">1:691331548785:android:a697c26637a1bd4c207dd0</string>
    <string name="google_crash_reporting_api_key" translatable="false">AIzaSyBuHgJY9fPM7dgJ8b2k2yUE_0UzP9c9Z2E</string>
    <string name="google_storage_bucket" translatable="false">hcorp-driver.firebasestorage.app</string>
    <string name="project_id" translatable="false">hcorp-driver</string>
    <style name="AppTheme" parent="Theme.EdgeToEdge">
    <item name="android:editTextBackground">@drawable/rn_edit_text_material</item>
    <item name="colorPrimary">@color/colorPrimary</item>
    <item name="android:statusBarColor">#ffffff</item>
  </style>
    <style name="Theme.App.SplashScreen" parent="Theme.SplashScreen">
    <item name="windowSplashScreenBackground">@color/splashscreen_background</item>
    <item name="windowSplashScreenAnimatedIcon">@drawable/splashscreen_logo</item>
    <item name="postSplashScreenTheme">@style/AppTheme</item>
  </style>
</resources>
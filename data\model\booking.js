export class Booking {
  constructor({
    bookingID,
    bookingStatus,
    driverID,
    vehicleID,
    userID,
    bookingStartLat,
    bookingStartLng,
    bookingStartAddress,
    bookingEndLat,
    bookingEndLng,
    bookingEndAddress,
    bookingType,
    createdAt,
    updatedAt,
    bookingReceiptID,
    bookingReviewID,
    isGuestBooking,
    tenantID,
    scheduledBookingDate,
    scheduledBookingStartTime,
    scheduledBookingEndTime,
    rentalBookingDate,
    rentalBookingStartTime,
    rentalBookingEndTime,
    rentalBookingDuration,
    recurringBookingStartTime,
    recurringBookingEndTime,
    recurringBookingFromDate,
    recurringBookingToDate,
    recurrentParentBookingID,
    recurringBookingDays,
    guestMeta,
    scheduledBookingSlot,
    rentalBookingSlot,
    recurringBookingSlot,
  }) {
    this.bookingID = bookingID;
    this.bookingStatus = bookingStatus;
    this.bookingStartAddress = bookingStartAddress;
    this.bookingEndAddress = bookingEndAddress;
    this.bookingStartLat = bookingStartLat;
    this.bookingStartLng = bookingStartLng;
    this.bookingEndLat = bookingEndLat;
    this.bookingEndLng = bookingEndLng;
    this.bookingType = bookingType;
    this.scheduledBookingDate = scheduledBookingDate;
    this.scheduledBookingStartTime = scheduledBookingStartTime;
    this.scheduledBookingEndTime = scheduledBookingEndTime;
    this.rentalBookingDate = rentalBookingDate;
    this.rentalBookingStartTime = rentalBookingStartTime;
    this.rentalBookingEndTime = rentalBookingEndTime;
    this.rentalBookingDuration = rentalBookingDuration;
    this.scheduledBookingSlot = scheduledBookingSlot;
    this.rentalBookingSlot = rentalBookingSlot;
  }
  toList = (data) => {
    if (data && Array.isArray(data)) {
      return data.map((item) => new Booking(item));
    } else {
      return [];
    }
  };
}

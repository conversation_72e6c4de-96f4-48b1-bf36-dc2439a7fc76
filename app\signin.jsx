import { Restart } from 'fiction-expo-restart';
import { useFormik } from 'formik';
import { Linking, StatusBar, StyleSheet, Text, View } from 'react-native';
import { Button, TextInput } from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { GLOBAL_STYLES } from '../constants/globalStyles';
import { theme } from '../constants/theme';
// import {hover_hero_logo} from '../../assets/hover_hero_logo';
import { SimpleTextAlert } from '../components/ui/alerts';
import { Timer } from '../components/ui/timer';
import { useAuthActions, useAuthState } from '../contexts/authContext';
import { extractError } from '../utils/extractError';

const PhoneNumberInputForm = () => {
  const {authState, signInState} = useAuthState();
  const {sendOTP} = useAuthActions();
  const phoneInputForm = useFormik({
    initialValues: {phoneNumber: ''},
    validate: values => {
      const errors = {};
      if (!values.phoneNumber) {
        errors.phoneNumber = 'Required';
      } else if (values.phoneNumber.length != 10) {
        errors.phoneNumber = 'Invalid phone';
      }
      return errors;
    },
    onSubmit: values => {
      _handleSendOTP({phoneNumber: values.phoneNumber});
    },
  });

  const _navigateToTandCScreen = () => {
      Linking.openURL('https://www.hover-mobility.com/terms');
    };

  const _handleSendOTP = ({phoneNumber}) => {
    sendOTP({phoneNumber});
  };
  return (
    <View
      style={[
        styles.phone_input_main,
        {backgroundColor: theme.colors.surface},
      ]}>
      <View
        style={[
          styles.phone_input_section_1,
          {backgroundColor: theme.colors.surface},
        ]}>
        {/* <SvgXml
          height={80}
          width={150}
          xml={hover_hero_logo}
          style={{borderRadius: 8, marginTop: 20}}
        /> */}
        <Text
          style={
            styles.phone_input_text_main
          }>{`Please enter your phone number to sign in.`}</Text>
        <TextInput
          value={phoneInputForm.values.phoneNumber}
          keyboardType="numeric"
          onBlur={phoneInputForm.handleBlur('phoneNumber')}
          placeholder="Phone number"
          onChangeText={phoneInputForm.handleChange('phoneNumber')}
          style={{
            width: '100%',
            marginTop: 20,
          }}
          mode="outlined"></TextInput>
        {phoneInputForm.errors?.phoneNumber && (
          <SimpleTextAlert
            message={phoneInputForm.errors.phoneNumber}
            style={{marginTop: 6}}
          />
        )}
        {signInState?.error && (
          <SimpleTextAlert
            message={extractError(signInState.error)}
            style={{marginTop: 6}}
          />
        )}
        <Text style={styles.phone_input_text_secondary}>
          By signing up, you confirm that you agree to our{' '}
          <Text
            style={styles.phone_input_text_secondary_link}
            onPress={_navigateToTandCScreen}>
            Terms of Use
          </Text>{' '}
          and have read and understood out{' '}
          <Text
            style={styles.phone_input_text_secondary_link}
            onPress={_navigateToTandCScreen}>
            Privacy Policy
          </Text>{' '}
          . You will receive an SMS to confirm your phone number, SMS fee may
          apply
        </Text>
      </View>

      <Button
        loading={signInState?.isLoading}
        disabled={signInState?.isLoading}
        onPress={phoneInputForm.submitForm}
        mode="contained"
        labelStyle={GLOBAL_STYLES.button_text}
        style={[GLOBAL_STYLES.primary_contained_button]}>
        Submit
      </Button>
    </View>
  );
};

const OTPInputForm = () => {
  const {sendOTP, confirmOTP, setResendOTPState} = useAuthActions();
  const {auth, resendOTPState, signInState} = useAuthState();
  const {} = useAuthActions();

  const otpInputForm = useFormik({
    initialValues: {otp: ''},
    validate: values => {
      const errors = {};
      if (!values.otp) {
        errors.otp = 'Required';
      } else if (values.otp.length != 6) {
        errors.otp = 'Invalid OTP';
      }
      return errors;
    },
    onSubmit: values => {
      _handleConfirmOTP(values.otp);
    },
  });

  const _handleConfirmOTP = OTP => {
    confirmOTP(OTP);
  };

  const _handleResendCode = () => {
    if (resendOTPState?.resendPhoneNumber) {
      sendOTP({phoneNumber: resendOTPState.resendPhoneNumber});
    }
  };

  const _handleResendDisablerTrigger = () => {
    setResendOTPState({...resendOTPState, isResendable: false});
  };
  const _handleResendEnableTrigger = () => {
    setResendOTPState({...resendOTPState, isResendable: true});
  };
  _handleChangePhoneNumber = () => {
    Restart();
  };
  return (
    <View
      style={[styles.otp_input_main, {backgroundColor: theme.colors.surface}]}>
      <View
        style={[
          styles.otp_input_section_1,
          {backgroundColor: theme.colors.surface},
        ]}>
        {/* <SvgXml
          height={80}
          width={150}
          xml={hover_hero_logo}
          style={{borderRadius: 8, marginTop: 20}}
        /> */}
        <Text
          style={
            styles.otp_input_text_main
          }>{`Please enter OTP which you recieved on registered mobile.`}</Text>
        <TextInput
          value={otpInputForm.values.otp}
          keyboardType="numeric"
          onBlur={otpInputForm.handleBlur('otp')}
          placeholder="OTP"
          onChangeText={otpInputForm.handleChange('otp')}
          style={{
            width: '100%',
            marginTop: 20,
          }}
          mode="outlined"></TextInput>
        {otpInputForm.errors?.otp && (
          <SimpleTextAlert
            message={otpInputForm.errors.otp}
            style={{marginTop: 6}}
          />
        )}
        {signInState?.error && (
          <SimpleTextAlert
            message={extractError(signInState.error)}
            style={{marginTop: 6}}
          />
        )}
        <View style={[styles.otp_input_action_container]}>
          <Button
            onPress={_handleChangePhoneNumber}
            disabled={signInState.isLoading}
            labelStyle={[{marginHorizontal: 0}, GLOBAL_STYLES.button_text]}
            mode="text"
            rippleColor={'#00000000'}
            className="font-medium !normal-case">
            Edit phone
          </Button>
          <View style={[styles.otp_input_action_container_section_2]}>
            <Button
              onPress={_handleResendCode}
              disabled={!resendOTPState?.isResendable || signInState.isLoading}
              loading={signInState.isLoading}
              mode="text"
              rippleColor={'#00000000'}
              labelStyle={[{marginRight: 0}, GLOBAL_STYLES.button_text]}>
              {signInState.isLoading
                ? 'Please wait...'
                : resendOTPState?.isResendable
                ? 'Resend code'
                : 'Resend code?'}
            </Button>
            {resendOTPState && !resendOTPState?.isResendable && (
              <Timer
                startTime={resendOTPState.prevSentAt}
                mode={'countdown'}
                countdown={60000}
                countdownEndTrigger={_handleResendEnableTrigger}
                countdownStartTrigger={_handleResendDisablerTrigger}
                startText={' '}
              />
            )}
          </View>
        </View>
      </View>

      <Button
        loading={signInState?.isLoading}
        disabled={signInState?.isLoading}
        onPress={otpInputForm.handleSubmit}
        mode="contained"
        labelStyle={GLOBAL_STYLES.button_text}
        style={[GLOBAL_STYLES.primary_contained_button]}>
        Submit
      </Button>
    </View>
  );
};

const SignInScreen = ({}) => {
  const {signInState} = useAuthState();
  return (
    <SafeAreaView style={styles.signin_main}>
      <StatusBar
        barStyle={'dark-content'}
        backgroundColor={theme.colors.surface}
      />

      {signInState.confirmationResult ? (
        <OTPInputForm />
      ) : (
        <PhoneNumberInputForm />
      )}
    </SafeAreaView>
  );
};


const styles = StyleSheet.create({
  signin_main: {
    flex: 1,
    flexDirection: 'column',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',

    backgroundColor: theme.colors.surface,
  },
  phone_input_main: {
    flexDirection: 'column',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 14,
    width: '100%',
    height: '100%',
    paddingVertical: 20,
  },
  phone_input_section_1: {
    flexDirection: 'column',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    width: '100%',
  },
  phone_input_text_main: {
    width: '100%',
    fontFamily: 'Montserrat-Medium',
    color: theme.colors.secondary,
  },

  phone_input_text_secondary: {
    marginTop: 25,
    width: '100%',
    fontFamily: 'Montserrat-Medium',
    color: theme.colors.secondary,
  },

  phone_input_text_secondary_link: {
    marginTop: 25,
    width: '100%',
    fontFamily: 'Montserrat-Medium',
    color: theme.colors.info,
  },

  otp_input_main: {
    flexDirection: 'column',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingHorizontal: 14,
    width: '100%',
    height: '100%',
    paddingVertical: 20,
  },
  otp_input_section_1: {
    flexDirection: 'column',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
    width: '100%',
  },
  otp_input_text_main: {
    width: '100%',
    fontFamily: 'Montserrat-Medium',
    color: theme.colors.secondary,
  },

  otp_input_text_secondary: {
    marginTop: 25,
    width: '100%',
    fontFamily: 'Montserrat-Medium',
    color: theme.colors.secondary,
  },

  otp_input_action_container: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    width: '100%',
  },

  otp_input_action_container_section_2: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'flex-end',
    alignItems: 'center',
  },
});
export default SignInScreen;


{"root": "D:\\PROJECTS\\ORDERS\\driver", "reactNativePath": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native", "dependencies": {"@react-native-async-storage/async-storage": {"root": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\@react-native-async-storage\\async-storage", "name": "@react-native-async-storage/async-storage", "platforms": {"android": {"sourceDir": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\@react-native-async-storage\\async-storage\\android", "packageImportPath": "import com.reactnativecommunity.asyncstorage.AsyncStoragePackage;", "packageInstance": "new AsyncStoragePackage()", "buildTypes": [], "libraryName": "rnasyncstorage", "componentDescriptors": [], "cmakeListsPath": "D:/PROJECTS/ORDERS/driver/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "@react-native-firebase/app": {"root": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\@react-native-firebase\\app", "name": "@react-native-firebase/app", "platforms": {"android": {"sourceDir": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\@react-native-firebase\\app\\android", "packageImportPath": "import io.invertase.firebase.app.ReactNativeFirebaseAppPackage;", "packageInstance": "new ReactNativeFirebaseAppPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "D:/PROJECTS/ORDERS/driver/node_modules/@react-native-firebase/app/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "@react-native-firebase/auth": {"root": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\@react-native-firebase\\auth", "name": "@react-native-firebase/auth", "platforms": {"android": {"sourceDir": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\@react-native-firebase\\auth\\android", "packageImportPath": "import io.invertase.firebase.auth.ReactNativeFirebaseAuthPackage;", "packageInstance": "new ReactNativeFirebaseAuthPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "D:/PROJECTS/ORDERS/driver/node_modules/@react-native-firebase/auth/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "@react-native-firebase/crashlytics": {"root": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\@react-native-firebase\\crashlytics", "name": "@react-native-firebase/crashlytics", "platforms": {"android": {"sourceDir": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\@react-native-firebase\\crashlytics\\android", "packageImportPath": "import io.invertase.firebase.crashlytics.ReactNativeFirebaseCrashlyticsPackage;", "packageInstance": "new ReactNativeFirebaseCrashlyticsPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "D:/PROJECTS/ORDERS/driver/node_modules/@react-native-firebase/crashlytics/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "expo": {"root": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\expo", "name": "expo", "platforms": {"android": {"sourceDir": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\expo\\android", "packageImportPath": "import expo.modules.ExpoModulesPackage;", "packageInstance": "new ExpoModulesPackage()", "buildTypes": [], "componentDescriptors": [], "cmakeListsPath": "D:/PROJECTS/ORDERS/driver/node_modules/expo/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-gesture-handler": {"root": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-gesture-handler", "name": "react-native-gesture-handler", "platforms": {"android": {"sourceDir": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-gesture-handler\\android", "packageImportPath": "import com.swmansion.gesturehandler.RNGestureHandlerPackage;", "packageInstance": "new RNGestureHandlerPackage()", "buildTypes": [], "libraryName": "rngesturehandler_codegen", "componentDescriptors": ["RNGestureHandlerRootViewComponentDescriptor", "RNGestureHandlerButtonComponentDescriptor"], "cmakeListsPath": "D:/PROJECTS/ORDERS/driver/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-reanimated": {"root": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-reanimated", "name": "react-native-reanimated", "platforms": {"android": {"sourceDir": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-reanimated\\android", "packageImportPath": "import com.swmansion.reanimated.ReanimatedPackage;", "packageInstance": "new ReanimatedPackage()", "buildTypes": [], "libraryName": "rnreanimated", "componentDescriptors": [], "cmakeListsPath": "D:/PROJECTS/ORDERS/driver/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-safe-area-context": {"root": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-safe-area-context", "name": "react-native-safe-area-context", "platforms": {"android": {"sourceDir": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-safe-area-context\\android", "packageImportPath": "import com.th3rdwave.safeareacontext.SafeAreaContextPackage;", "packageInstance": "new SafeAreaContextPackage()", "buildTypes": [], "libraryName": "safeareacontext", "componentDescriptors": ["RNCSafeAreaProviderComponentDescriptor", "RNCSafeAreaViewComponentDescriptor"], "cmakeListsPath": "D:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-screens": {"root": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-screens", "name": "react-native-screens", "platforms": {"android": {"sourceDir": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-screens\\android", "packageImportPath": "import com.swmansion.rnscreens.RNScreensPackage;", "packageInstance": "new RNScreensPackage()", "buildTypes": [], "libraryName": "rnscreens", "componentDescriptors": ["RNSFullWindowOverlayComponentDescriptor", "RNSScreenContainerComponentDescriptor", "RNSScreenNavigationContainerComponentDescriptor", "RNSScreenStackHeaderConfigComponentDescriptor", "RNSScreenStackHeaderSubviewComponentDescriptor", "RNSScreenStackComponentDescriptor", "RNSSearchBarComponentDescriptor", "RNSScreenComponentDescriptor", "RNSScreenFooterComponentDescriptor", "RNSScreenContentWrapperComponentDescriptor", "RNSModalScreenComponentDescriptor"], "cmakeListsPath": "D:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-webview": {"root": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-webview", "name": "react-native-webview", "platforms": {"android": {"sourceDir": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-webview\\android", "packageImportPath": "import com.reactnativecommunity.webview.RNCWebViewPackage;", "packageInstance": "new RNCWebViewPackage()", "buildTypes": [], "libraryName": "RNCWebViewSpec", "componentDescriptors": ["RNCWebViewComponentDescriptor"], "cmakeListsPath": "D:/PROJECTS/ORDERS/driver/node_modules/react-native-webview/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}, "react-native-edge-to-edge": {"root": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-edge-to-edge", "name": "react-native-edge-to-edge", "platforms": {"android": {"sourceDir": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-edge-to-edge\\android", "packageImportPath": "import com.zoontek.rnedgetoedge.EdgeToEdgePackage;", "packageInstance": "new EdgeToEdgePackage()", "buildTypes": [], "libraryName": "RNEdgeToEdge", "componentDescriptors": [], "cmakeListsPath": "D:/PROJECTS/ORDERS/driver/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/CMakeLists.txt", "cxxModuleCMakeListsModuleName": null, "cxxModuleCMakeListsPath": null, "cxxModuleHeaderName": null}}}}, "project": {"android": {"packageName": "co.auter.hcorp", "sourceDir": "D:\\PROJECTS\\ORDERS\\driver\\android"}}}
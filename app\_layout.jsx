import { useFonts } from "expo-font";
import { StatusBar } from "expo-status-bar";
import "react-native-reanimated";

import { MainRouter } from "@/components/router/mainRouter";
import { CONSTANTS } from "@/constants/constants";
import { AuthContextProvider } from "@/contexts/authContext";
import { LocationContextProvider } from "@/contexts/locationContext";
import { PermissionsContextProvider } from "@/contexts/permissionsContext";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import * as Notifications from "expo-notifications";
import { usePathname, useRouter } from "expo-router";
import { useEffect, useRef } from "react";
import { PaperProvider } from "react-native-paper";
import { theme } from "../constants/theme";
import { getRecentBookingAcceptanceRequest, saveBookingAcceptanceRequest } from "../utils/localStorageHelper";

const queryClient = new QueryClient();

Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: true,
    shouldSetBadge: false,
  }),
});

export default function RootLayout() {
  const router = useRouter();
  const pathname = usePathname(); // Get the current route path
  const currentRouteRef = useRef(pathname); // store latest route path
  const [loaded] = useFonts({
    SpaceMono: require("../assets/fonts/SpaceMono-Regular.ttf"),
  });

  useEffect(() => {
    currentRouteRef.current = pathname;
  }, [pathname]);

  

  useEffect(() => {

    // Foreground notifications
    const foregroundSub = Notifications.addNotificationReceivedListener(
      async(notification) => {
        const data = notification.request.content.data;
        console.log("New notification received in foreground:", data);
        if(data.type == CONSTANTS.DRIVER_NOTIFICATION_TYPES.NEW_BOOKING){
          console.log("New ride received in foreground:", data);
          const bookingIDs = await getRecentBookingAcceptanceRequest();
          console.log("bookingIDs", bookingIDs);
          await saveBookingAcceptanceRequest(data.bookingID);
          // if (currentRouteRef.current !== CONSTANTS.ROUTES.BOOKING_ACCEPTANCE.ROUTE) {
          //   router.push({
          //     pathname: CONSTANTS.ROUTES.BOOKING_ACCEPTANCE.ROUTE,
          //     params: data,
          //   });
          // } else {
          //   console.log("Already on booking acceptance page — not navigating again.");
          // }
        }
      }
    );

    // Background / killed notifications (user taps notification)
    const responseSub = Notifications.addNotificationResponseReceivedListener(
      (response) => {
        const data = response.notification.request.content.data;
        console.log("User tapped ride notification:", data);
      }
    );

    const tokenListener = Notifications.addPushTokenListener(async (token) => {
      console.log("New token:", token);
      await DriverRepository.updateFCMToken({ token: token.data });
    });

    return () => {
      foregroundSub.remove();
      responseSub.remove();
      tokenListener.remove();
    };
  }, []);

  if (!loaded) return null;

  return (
    <PaperProvider theme={theme}>
      <PermissionsContextProvider>
        <QueryClientProvider client={queryClient}>
          <LocationContextProvider>
            <AuthContextProvider>
                <MainRouter />
            </AuthContextProvider>
          </LocationContextProvider>
        </QueryClientProvider>
      </PermissionsContextProvider>
      <StatusBar style="auto" />
    </PaperProvider>
  );
}

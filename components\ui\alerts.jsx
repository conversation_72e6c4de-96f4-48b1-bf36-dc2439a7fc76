import { theme } from '@/constants/theme';
import { MaterialCommunityIcons } from '@expo/vector-icons';
import { StyleSheet, View } from 'react-native';
import { Text } from 'react-native-paper';

export const SimpleTextAlert = ({
  borderWidth,
  borderColor,
  title,
  message,
  icon,
  iconDisabled,
  iconColor,
  style,
  textStyle,
  titleStyle,
  color,
  backgroundColor,
  outlined,
  endIcon,
  endIconSize,
  endIconColor,
}) => {
  return (
    <View
      style={[
        styles.alert_main,
        {...style, backgroundColor: backgroundColor},
        backgroundColor ? {backgroundColor: backgroundColor} : null,
        outlined ? styles.alert_outlined : {alignItems: 'center'},
        color ? {borderColor: color} : null,
        borderWidth ? {borderWidth: borderWidth} : {},
        borderColor ? {borderColor: borderColor} : {},
      ]}>
      <View
        style={{
          flexDirection: 'row',
          justifyContent: 'flex-start',
          alignItems: 'flex-start',
        }}>
        {!iconDisabled && (
          <MaterialCommunityIcons
            name={icon ? icon : 'alert-circle'}
            size={20}
            style={{
              marginRight: 8,

              color: iconColor ? iconColor : color ? color : theme.colors.error,
            }}
          />
        )}
        {title ? (
          <View style={styles.alert_title_container}>
            <Text
              style={[
                styles.alert_title,
                color ? {color: color} : null,
                {...titleStyle},
              ]}>
              {title}
            </Text>
            <Text
              style={[
                styles.alert_text,
                color ? {color: color} : null,
                {...textStyle},
              ]}>
              {message}
            </Text>
          </View>
        ) : (
          <Text
            style={[
              styles.alert_text,
              color ? {color: color} : null,
              {...textStyle},
            ]}>
            {message}
          </Text>
        )}
      </View>

      <View style={{}}>
        {endIcon && (
          <MaterialCommunityIcons
            name={endIcon ? endIcon : 'alert-circle'}
            size={endIconSize ? endIconSize : 20}
            style={{
              color: endIconColor
                ? endIconColor
                : color
                ? color
                : theme.colors.error,
            }}
          />
        )}
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  alert_main: {
    display: 'flex',
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    width: '100%',
  },
  alert_outlined: {
    borderWidth: 1,
    borderRadius: 8,
    padding: 8,
  },
  alert_title_container: {
    flexDirection: 'column',
    justifyContent: 'flex-start',
    alignItems: 'flex-start',
  },
  alert_title: {
    flex: 1,
    flexWrap: 'wrap',
    color: theme.colors.secondary,
    fontSize: 12,
    flexGrow: 1,
    maxWidth: '95%',
    marginBottom: 4,
    fontFamily: 'Montserrat-Bold',
  },
  alert_text: {
    flex: 1,
    flexWrap: 'wrap',
    color: theme.colors.secondary,
    fontSize: 12,
    flexGrow: 1,
    maxWidth: '95%',
    fontFamily: 'Montserrat-Medium',
  },
});

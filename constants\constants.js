export const CONSTANTS = {
  // API_SERVER: __DEV__
  //   ? 'https://select-casual-civet.ngrok-free.app'
  //   : 'https://api.hovermobility.co.in',
  // SOCKET_SERVER: __DEV__
  //   ? 'https://select-casual-civet.ngrok-free.app'
  //   : 'https://api.hovermobility.co.in',

  API_SERVER: "https://select-casual-civet.ngrok-free.app",
  SOCKET_SERVER: "https://select-casual-civet.ngrok-free.app/driver",

  APP_VERSION: "1.2.12.2",

  DRIVER_AVAILABILITY_STATUS: {
    AVAILABLE: {
      value: "AVAILABLE",
      label: "Available",
      message: "You are currently available to receive ride requests",
    },
    UNAVAILABLE: {
      value: "UNAVAILABLE",
      label: "Unavailable",
      message: "You are currently unavailable to receive ride requests",
    },
  },

  SOCKET_RECEIVE_EVENTS: {},
  SOCKET_EMIT_EVENTS: {
    DRIVER_LOCATION_DATA: "driver_location_update",
  },
  STRINGS: {
    PERMISSIONS_SCREEN_TITLE: "Permissions",
    HOME_SCREEN_TITLE: "Home",
    PROFILE_SCREEN_TITLE: "Profile",
    PROFILE_SCREEN_SUBTITLE: "Update your profile details",
    PROFILE_SCREEN_ERROR_LOADING_PROFILE: "Error loading profile",
    PROFILE_SCREEN_FORM_SAVE_BUTTON: "Save",
    PROFILE_SCREEN_FORM_FIRST_NAME_LABEL: "First name",
    PROFILE_SCREEN_FORM_LAST_NAME_LABEL: "Last name",
    PROFILE_SCREEN_FORM_EMAIL_LABEL: "Email",
    PROFILE_SCREEN_FORM_ADDRESS_LABEL: "Address",
    APPBAR_RIDE_IN_PROGRESS: "Ride in Progress",
    RIDE_TIME_LABEL: "Ride time",
    QR_SCANNER_SCREEN_BUTTTON: "Enter code below QR",
    QR_SCANNER_SCREEN_INSTRUCTION: "Scan QR code on the vehicle",
    RESERVATION_CONFIRMATION_DIALOG_ALTERNATE_MESSAGE:
      "will be reserved for you for 10 min. Reach at vehicle location to start the vehicle",
    MANUAL_VEHICLE_NUMBER_SCREEN_TITLE: "Enter 6-digit code",
    MANUAL_VEHICLE_NUMBER_SCREEN_INSTRUCTION_PLACEHOLDER:
      "Enter 6-digit code below QR",
    MANUAL_VEHICLE_NUMBER_SCREEN_INSTRUCTION:
      "6-digit code is present below the QR code or on the Dashboard.",
    FAKE_GPS_MESSAGE:
      "We have noticed that you have a Fake GPS app running. Please turn it off the continue using Hover",
    SHOW_NEAREST_HUB: "Show nearest hub",
    BATTERY_SWAPPING_STATION: "Battery swapping station",
    FIND_NEAREST_HUB: "Find nearby parking hubs",
    ESTIMATED_OUTSTATION_CHARGE: "Estimated additional charge:",
    SET_DIFFERENT_LOCATION_BUTTON_TEXT: "Set Different Location",
    CONFIRM_SET_LOCATION_BUTTON_TEXT: "Confirm Location",
    PENDING_PAYMENTS_TITLE: "Pending payments",
    NAVIGATE_ON_MAPS: "Navigate to hub",
    SCAN_VEHICLE_BUTTON: "Scan direct",
    SCAN_VEHICLE_TO_RIDE_BUTTON: "Scan to Ride",
    LONG_TERM_PLANS_BUTTON_TEXT: `Long term plans`,
    BOOKING_CHARGES_CARD_TITLE: "Details",
    BOOKING_CHARGES_CARD_LOCKING_CHARGE_TITLE: "Locking charges",
    BOOKING_CHARGES_CARD_RIDE_CHARGE_TITLE: "Ride charges",
    ERROR_IN_RIDE: "Error in ride",
    NO_BOOKING_LIST_PLACEHOLDER: "No bookings",
    NO_RESERVATION_LIST_PLACEHOLDER: "No reservations",
    NO_DELIVERY_LIST_PLACEHOLDER: "No delivery booked",
    NO_PAYMENT_ORDER_LIST_PLACEHOLDER: "No payments",
    NO_PLANS_LIST_PLACEHOLDER: "No plans purchased",
    NO_SUBSCRIPTION_LIST_PLACEHOLDER: "No subscription purchased",
    NO_OFFERS_LIST_PLACEHOLDER: "No offers at this time",
    NO_STATION_LIST_PLACEHOLDER: "No stations",
    NO_ADVERTISE_LIST_PLACEHOLDER: "Oops!",
    NO_ADVERTISE_LIST_PLACEHOLDER_DESCRIPTION: "No ads right now",
    CONFIRMATION_DIALOG_CONFIRM_BUTTON: "Confirm",
    CONFIRMATION_DIALOG_CANCEL_BUTTON: "Cancel",
    NO_VEHICLE_CARD_TITLE: "No vehicles are available nearby!",
    NO_VEHICLE_CARD_DESCRIPTION: "But you can tell us to come to this area",
    NO_VEHICLE_CARD_VOTE_BUTTON: "Vote for area",
    OFFER_PURCHASE_CONFIRMATION_DIALOG_COST_TITLE: "Purchase item cost",
    OFFER_PURCHASE_CONFIRMATION_DIALOG_TOTAL_COST_TITLE:
      "Total amount to be paid",
    OFFER_PURCHASE_CONFIRMATION_DIALOG_CONFIRM_BUTTON: "Confirm",
    OFFER_PURCHASE_CONFIRMATION_DIALOG_CANCEL_BUTTON: "Cancel",

    OFFER_PURCHASE_RECHARGE_WALLET_CONFIRM_BUTTON: "Recharge wallet",
    OFFER_PURCHASE_PENDING_PAYMENT_RECOMMENDATION:
      "Please complete the pending payments to purchase plans or memberships",

    ONGOING_BOOKING_CARD_LOADING_RECOMMENDATION_TEXT:
      "We recommend not to close application",
    ONGOING_BOOKING_CARD_ENDING_RIDE_TEXT: "Ending your ride...",
    ONGOING_BOOKING_CARD_TIMER_TEXT: "Ride time",
    ONGOING_BOOKING_CARD_ESTIMATE_TEXT: "Estimated Fare",
    ONGOING_BOOKING_CARD_LOCK_BUTTON: "Lock",
    ONGOING_BOOKING_CARD_UNLOCK_BUTTON: "Unlock",
    ONGOING_BOOKING_CARD_SLIDE_TO_END_BUTTON: "Slide to End Ride",
    ONGOING_BOOKING_CARD_CANCEL_BOOKING_BUTTON: "Cancel booking",
    ONGOING_BOOKING_CARD_RECEIPT_BUTTON: "View receipt",
    VEHICLE_RANGE_TITLE: "Range",
    VEHICLE_BATTERY_TITLE: "Battery",
    RESERVATION_END_CONFIRMATION_TITLE: "Confirm end reservation?",
    RESERVATION_END_CONFIRMATION_DESCRIPTION:
      "Are you sure you want to end this reservation. Vehicle may geet assigned to someone else if not booked by you",

    RESERVATION_START_BUTTON: "Reserve",

    RESERVATION_START_CONFIRMATION_TITLE: "Reservation confirmation",
    RESERVATION_START_CONFIRMATION_DESCRIPTION:
      "Please select a plan for reservation. Vehicle is be reserved till the timer and amount will be deducted from wallet",

    ONGOING_RESERVATION_CARD_ENDING_RESERVATION_TEXT: "Ending reservation...",
    ONGOING_RESERVATION_CARD_TIMER_TEXT: "Reservation time left",
    ONGOING_RESERVATION_CARD_LOADING_RECOMMENDATION_TEXT:
      "We recommend not to close application",
    ONGOING_RESERVATION_CARD_END_RESERVATION_TEXT: "End reservation",
    ONGOING_RESERVATION_CARD_RESERVATION_TEXT: "Reserving vehicle for you...",
    ONGOING_RESERVATION_CARD_ENDING_RIDE_TEXT: "Ending reservation...",
    TRANSACTION_ERROR_TEXT: "Transaction error",
    DEBITED_FROM_TEXT: "Debited from",
    PAYMENT_ATTEMPTED_FROM_TEXT: "Payment attempted from",

    PLAN_PURCHASE_CONFIRMATION_TITLE: "Plan payment confirmation",
    PLAN_PURCHASE_CONFIRMATION_DESCRIPTION:
      "Are you sure you want to purchase this plan. You will be redirected to the payment gateway",
    PLAN_PURCHASE_BUTTON: "Buy now",
    STATION_CAPACITY_TEXT: "Capacity",
    STATION_PINCODE_TEXT: "Pincode",

    SUBSCRIPTION_PURCHASE_CONFIRMATION_TITLE: "Membership payment confirmation",
    SUBSCRIPTION_PURCHASE_CONFIRMATION_DESCRIPTION:
      "Are you sure you want to purchase this subscription. You will be redirected to the payment gateway",
    SUBSCRIPTION_PURCHASE_BUTTON: "Buy now",
    STARTED_RIDE_CANCELLATION_TEXT: "Cancel ride (1 min)",
    BOOKING_CANCEL_CONFIRMATION_TITLE: "Cancel booking?",
    BOOKING_CANCEL_CONFIRMATION_DESCRIPTION: `Your ongoing booking will be cancelled. You won't be charged any amount.`,

    OUTSTATION_CONFIRMATION_TITLE: "You are ending ride outside Hub",
    OUTSTATION_CONFIRMATION_DESCRIPTION: `Rides cannot be ended outside Hover Hubs. Please end your ride at the nearest Hover Hub.`,
    OUTSTATION_CONFIRMATION_DESCRIPTION_1: `You can end your ride outside Hover Hubs by giving additional charge of `,
    OUTSTATION_CONFIRMATION_DESCRIPTION_2: ` from the nearest Hub!`,

    PARKING_STATION_TITLE: "Parking station",

    BOOKING_RECEIPT_THANKYOU_1: `Thank you`,
    BOOKING_RECEIPT_THANKYOU_2: `for riding with us!`,
    BOOKING_RECEIPT_DETAILS_TITLE: `Booking details`,
    BOOKING_RECEIPT_BOOKING_CHARGE_TITLE: `Ride charge`,
    BOOKING_RECEIPT_DURATION_TITLE: `Duration`,
    BOOKING_RECEIPT_SHOW_MORE_SECTION_TITLE: `Show details`,
    BOOKING_RECEIPT_SHOW_LESS_SECTION_TITLE: `Show less`,
    BOOKING_RECEIPT_TOTAL_RIDE_TIME_TITLE: `Total ride time`,
    BOOKING_RECEIPT_TOTAL_PAUSE_TIME_TITLE: `Total pause time`,
    BOOKING_RECEIPT_START_TIME_TITLE: `Started at`,
    BOOKING_RECEIPT_END_TIME_TITLE: `Ended at`,
    BOOKING_RECEIPT_BILL_DETAILS_TITLE: `Bill details`,
    BOOKING_RECEIPT_UNLOCK_CHARGE_TITLE: `Unlock charge`,
    BOOKING_RECEIPT_RIDE_CHARGE_TITLE: `Ride time charge`,
    BOOKING_RECEIPT_PAUSE_CHARGE_TITLE: `Pause time charge`,
    BOOKING_RECEIPT_OUTSTATION_CHARGE_TITLE: `Out of station parking charge`,
    BOOKING_RECEIPT_TAX_CHARGE_TITLE: `Taxes`,
    BOOKING_RECEIPT_VEHICLE_MAINTENANCE_CHARGE_TITLE: `Vehicle maintenance charge`,
    BOOKING_RECEIPT_PAYMENT_GATEWAY_CHARGE_TITLE: `Payment gateway charge`,
    BOOKING_RECEIPT_MIN_CHARGE_TITLE: `Min base price`,
    BOOKING_RECEIPT_TOTAL_CHARGE_TITLE: `Total payable`,

    BOOKING_RECEIPT_ALERT_PAYMENT_FAILURE_TITLE: `Payment for ride not done`,
    BOOKING_RECEIPT_ALERT_PAYMENT_FAILURE_DESCRIPTION: `This may be due to various reasons like, the ride might have been terminated automatically due to low balance or the vehicle malfunctioned`,

    BOOKING_RECEIPT_ALERT_RECEIPT_GENERATION_FAILURE_TITLE: `Bill not generated`,
    BOOKING_RECEIPT_ALERT_RECEIPT_GENERATION_FAILURE_DESCRIPTION: `This may be due to various reasons like, the ride might have been terminated automatically due to low balance or the vehicle malfunctioned`,

    BOOKING_RECEIPT_ALERT_RIDE_CANCELLED_TITLE: `This ride was cancelled`,
    BOOKING_RECEIPT_ALERT_RIDE_CANCELLED_DESCRIPTION: `Rides cancelled within 1 min are not charged any money`,

    BOOKING_RECEIPT_GENERATE_RECEIPT_BUTTON: `Generate receipt`,

    ADD_MONEY_TO_WALLET_BOTTOM_BUTTON_1: `Add money to wallet`,
    ADD_MONEY_TO_WALLET_BOTTOM_BUTTON_2: `For seemless payments`,

    SUBSCRIPTION_PURCHASE_SUCCESS: "Membership purchased!",
    PLAN_PURCHASE_SUCCESS: "Plan purchased!",
    DELIVERY_BOOKING_REQUEST_SUCCESS: `Request added. Our delivery representative will shortly contact you`,
    SECURITY_DEPOSIT_REFUND_REQUEST_SUCCESS:
      "Submitted your request for security deposit refund",

    DELIVER_TO_ME_BOOK_BUTTON: "Slide to book",
    DELIVER_TO_ME_SELECTED_LOCATION_TITLE: `Selected location`,
    DELIVER_TO_ME_SELECT_LOCATION_TITLE: `Select delivery location`,
    DELIVER_TO_ME_SELECTED_DURATION_TITLE: `Selected duration`,
    DELIVER_TO_ME_SELECT_DURATION_TITLE: `Select duration`,
    DELIVER_TO_ME_SELECTED_VEHICLE_TITLE: `Selected scooter`,
    DELIVER_TO_ME_SELECT_VEHICLE_TITLE: `Select scooter`,
    DELIVER_TO_ME_SELECT_VEHICLE_DESCRIPTION: `Please select one scooter type to be delivered`,
    MY_DELIVERIES_TITLE: "My delivery orders",

    SIGNOUT_CONFIRMATION_TITLE: "Sign out confirmation",
    SIGNOUT_CONFIRMATION_DESCRIPTION: "Are you sure you want to sign out?",

    BOOKING_STARTING_LOADING_TITLE: "Starting your ride...",
    BOOKING_STARTING_LOADING_DESCRIPTION:
      "We recommend you to be in near vehicle while booking",

    BOOKING_STATUS_LOADING_TITLE: "Loading your ride...",
    BOOKING_STATUS_LOADING_DESCRIPTION:
      "We recommend you to be in near vehicle while booking",

    STARTED_RIDE_CANCELLATION_ALER_DESCRIPTION: `If vehicle is damaged, you can end ride in `,

    REPORT_DAMAGE_TITLE: `Report damage`,
    VEHICLE_PARKING_RECOMMENDATION: `We encourage you to End ride at Hover Hubs. You can end your ride outside Hover Hubs by paying additional charge of Rs 5/km from nearest Hub. Additional Vehicle Maintenance, Payment Gateway charges and GST applicable.`,
    SELECTED_PLAN_TITLE: "Selected plan",
    ACTIVE_SUBSCRIPTIONS_TITLE: `Active subscriptions`,

    NO_PLAN_SUBSCRIPTION_TEXT: `You are not subscribed to cheaper plans`,
    NO_PLAN_SUBSCRIPTION_ACTION_BUTTON: `Explore plans`,

    SECURITY_DEPOSIT_PAYMENT_BUTTON: `Pay security deposit`,
    SECURITY_DEPOSIT_PAYMENT_RECOMMENDATION: `This is a one time, refundable amount and will ensure the safety of bike as well as the rider.`,

    VEHICLE_BLUETOOTH_NOT_CONNECTED_WARNING: `Vehicle cannot be connected to bluetooth of your device. Please check you are within vehicle's range and bluetooth is turned on.`,
    RIDE_CONSENT_TITLE: `I accept the scooter for safe ride.`,
    RIDE_CONSENT_DESCRIPTION: `Riding triple seat will incur a Rs. 500 penalty. I ensure that there are no major damages. Report damage if any. If unsure, Call us`,
    SLIDE_TO_UNLOCK_BUTTON: "Slide to unlock",
    WALLET_SECURITY_DEPOSIT_CONFIRMATION_TITLE:
      "Security deposit payment confirmation",
    WALLET_SECURITY_DEPOSIT_REFUND_INSTRUCTION_1:
      "Security deposit is refundable till ",
    WALLET_SECURITY_DEPOSIT_REFUND_INSTRUCTION_2:
      "After this it will be not refundable.",
    WALLET_SECURITY_DEPOSIT_CONFIRMATION_DESCRIPTION:
      "Are you sure you want to pay the security deposit. You will be redirected to the payment gateway",
    WALLET_SECURITY_DEPOSIT_CONFIRMATION_DESCRIPTION_1:
      "Security deposit is refundable till next 6 months (",
    WALLET_SECURITY_DEPOSIT_CONFIRMATION_DESCRIPTION_2:
      "After this it will be not refundable.",
    WALLET_SECURITY_DEPOSIT_REFUND_CONFIRMATION_TITLE:
      "Security deposit refund confirmation",
    WALLET_SECURITY_DEPOSIT_REFUND_CONFIRMATION_DESCRIPTION:
      "Are you sure you want to request refund for the security deposit?",
    WALLET_RECHARGE_CONFIRMATION_TITLE: "Wallet recharge payment confirmation",
    WALLET_RECHARGE_CONFIRMATION_DESCRIPTION:
      "Are you sure you want to recharge wallet. You will be redirected to the payment gateway",
    WALLET_SECURITY_DEPOSIT_RECOMMENDATION: `This is a one time, refundable amount and will ensure the safety of bike as well as the rider.`,
    WALLET_NOT_ACTIVATED: `Wallet still not activated. Please activate the wallet first`,

    WALLET_NO_REFUND_TITLE: "Refund failed!",
    WALLET_NO_REFUND_DESCRIPTION:
      "Your wallet balance in negetive. Please clear the payment dues to get security deposit refund",
    BLUETOOTH_OFF:
      "The app requires bluetooth for working in order to connect vehicles. Please turn on bluetooth",
    NET_OFF:
      "The app requires internet for working. Please turn on internet connection.",
    BLE_ERROR_CARD_TITLE_VEHICLE_SCREEN: "Connection Failed",
    BLE_ERROR_CARD_TITLE: "Please wait. Connecting to Vehicle",
    BLE_ERROR_CARD_DESCRIPTION:
      "Make sure you are near vehicle and your bluetooth is turned ON",
    BLE_ERROR_CARD_VOTE_BUTTON: "Refresh",
    BLE_ERROR_CARD_RETRY_BUTTON: "Refresh and try again",
    BLE_ERROR_CARD_VOTE_CONNECTING_BUTTON: "Connecting...",
    ONGOING_BOOKING_VEHICLE_NUMBER_LABEL: "You are Riding",
    BATTERY_LOW_WARNING_TITLE: `Battery Low`,
    BATTERY_LOW_WARNING_DESCRIPTION: `Go to nearest Battery swap station or Parking Hub`,
    VEHICLE_BY_ID_KYC_RECOMMENDATION_TITLE: `Please complete your KYC`,
    VEHICLE_BY_ID_KYC_RECOMMENDATION_DESCRIPTION: `You need to complete your KYC through your Aadhar number`,
    COMPLETE_KYC_BOTTOM_BUTTON_1: `Complete your KYC`,
    VEHICLE_CONNECTED_TOAST_TITLE: `Vehicle connected`,
  },

  DRIVER_NOTIFICATION_TYPES: {
    NEW_BOOKING: "new_booking",
    BOOKING_UPDATE: "booking_update",
    BOOKING_CANCELLED: "booking_cancelled",
    CUSTOMER_REACHED: "customer_reached",
  },
  ROUTES: {
    PSUEDO_HOME: { ROUTE: "psuedo_home" },
    HOME: { ROUTE: "index" },
    BOOKING_ACCEPTANCE: { ROUTE: "booking-acceptance" },
    HISTORY: { ROUTE: "history" },
    PROFILE: { ROUTE: "profile" },
    PERMISSION: { ROUTE: "permissions" },
    SIGN_IN: { ROUTE: "signin" },
    ONGOING_BOOKING: { ROUTE: "ongoing_booking" },
    QR_CODE_VEHICLE_NUMBER: { ROUTE: "qr_code_vehicle_number" },
    MANUAL_VEHICLE_NUMBER: { ROUTE: "manual_vehicle_number" },
    SUPPORT: { ROUTE: "support" },
    STATIONS: {
      ROUTE: "stations",
    },
    DELIVER_TO_ME: {
      ROUTE: "deliver_to_me",
      NESTING: {
        VEHICLE_SELECT: { ROUTE: "vehicle_select" },
        LOCATION_SELECT: { ROUTE: "location_select" },
      },
    },
    DRAWER: {
      ROUTE: "drawer",
      NESTING: {
        ALL: { ROUTE: "all" },
        WALLET: { ROUTE: "wallet" },
        PROFILE_EDIT: { ROUTE: "profile_edit" },
        KYC: { ROUTE: "kyc" },
        PERMISSION: { ROUTE: "permissions" },
        OFFERS: {
          ROUTE: "offers",
          NESTING: {
            TYPES: { ROUTE: "types" },
            MY_OFFERS: {
              ROUTE: "my_offers",
              NESTING: {
                ALL: { ROUTE: "all" },
                BY_PLAN_ID: { ROUTE: "by_plan_id" },
                BY_SUBSCRIPTION_ID: { ROUTE: "by_subscription_id" },
              },
            },
          },
        },
        PAYMENT_ORDERS: {
          ROUTE: "payment_orders",
          NESTING: {
            ALL: { ROUTE: "all" },
            BY_ID: { ROUTE: "by_plan_id" },
          },
        },
        BOOKING_HISTORY: {
          ROUTE: "booking_history",
          NESTING: {
            ALL: { ROUTE: "all" },
            BY_ID: { ROUTE: "by_id" },
          },
        },
        DELIVERY_BOOKING_HISTORY: {
          ROUTE: "delivery_booking_history",
          NESTING: {
            ALL: { ROUTE: "all" },
            BY_ID: { ROUTE: "by_id" },
          },
        },
        RESERVATION_HISTORY: {
          ROUTE: "reservation_history",
          NESTING: {
            ALL: { ROUTE: "all" },
            BY_ID: { ROUTE: "by_id" },
          },
        },
      },
    },

    VEHICLES: {
      ROUTE: "vehicles",
      NESTING: {
        BY_ID: { ROUTE: "by_vehicle_id" },
      },
    },
  },

  DRAWER_ROUTES: {
    HOME: { route: "HOME", label: "Home" },
    WALLET: { route: "WALLET", label: "Wallet" },
    OFFERS: { route: "OFFERS", label: "Plans" },
    PAYMENT_ORDERS: { route: "PAYMENT_ORDERS", label: "Payment History" },
  },

  APIS: {
    DRIVER: {
      getSelf: () => "/api/driver/auth/v1/",
      updateProfile: () => "/api/driver/auth/v1/",
      updateAvailabilityStatus: () => "/api/driver/auth/v1/status",
      updateFCMToken: () => "/api/driver/auth/v1/fcmtoken",

      setGeolocation: () => "/api/users/geolocation",
      updatePushNotificationToken: () => "/api/users/push_notification_tokens",
      sendKYCOTP: () => "/api/users/kyc",
      verifyKYCOTP: () => "/api/users/kyc_verify",
    },
    BOOKING: {
      getBookingByID: (bookingID) => `/api/driver/bookings/v1/${bookingID}`,
      acceptBooking: () => "/api/driver/bookings/v1/accept",
      rejectBooking: () => "/api/driver/bookings/v1/reject",
      startBooking: () => "/api/driver/bookings/v1/start",
      endBooking: () => "/api/driver/bookings/v1/end",
      cancelBooking: () => "/api/driver/bookings/v1/cancel",
    },

    FEEDBACK: {
      fetchAllSupportTickets: ({ page }) =>
        page
          ? `/api/feedbacks/support_ticket?page=${page}`
          : `/api/feedbacks/support_ticket`,
      fetchSupportTicketMessages: ({ page, supportTicketID }) =>
        page
          ? `/api/feedbacks/support_ticket/${supportTicketID}?page=${page}`
          : `/api/feedbacks/support_ticket/${supportTicketID}`,
      raiseSupportTicket: () => `/api/feedbacks/support_ticket`,
      postSupportTicketMessage: ({ supportTicketID }) =>
        `/api/feedbacks/support_ticket/${supportTicketID}`,
      review: () => `/api/feedbacks/review`,
    },

    WALLET: {
      activate: () => "/api/wallets/activate",
      getWallet: () => "/api/wallets/",
      securityDeposit: () => "/api/wallets/security_deposit",
      walletRecharge: () => "/api/wallets/recharge",
      bookingFarePayment: () => `/api/wallets/booking_fare`,
      reservationFarePayment: () => `/api/wallets/reservation_fare`,
    },

    OFFER: {
      getReservationTypes: () => `/api/offers/reservationTypes`,
      getSubscriptionTypes: (vehicleTypeID) =>
        vehicleTypeID
          ? `/api/offers/subscriptionTypes?vehicle_type_id=${vehicleTypeID}`
          : "/api/offers/subscriptionTypes",
      getPlanTypes: (vehicleTypeID) =>
        vehicleTypeID
          ? `/api/offers/planTypes?vehicle_type_id=${vehicleTypeID}`
          : "/api/offers/planTypes",
      getSubscriptions: (vehicleTypeID) =>
        vehicleTypeID
          ? `/api/offers/subscriptions?vehicle_type_id=${vehicleTypeID}`
          : `/api/offers/subscriptions`,
      getPlans: (vehicleTypeID) =>
        vehicleTypeID
          ? `/api/offers/plans?vehicle_type_id=${vehicleTypeID}`
          : "/api/offers/plans",
      getCoupons: (vehicleTypeID) =>
        vehicleTypeID
          ? `/api/offers/coupons?vehicle_type_id=${vehicleTypeID}`
          : "/api/offers/coupons",
      getSubscriptionByID: (id) => `/api/offers/subscriptions/${id}`,
      getPlanByID: (id) => `/api/offers/plans/${id}`,
      purchaseSubscriptionType: () => "/api/offers/purchase_subscription_type",
      purchasePlanType: () => "/api/offers/purchase_plan_type",
    },

    PAYMENT_ORDER: {
      getPaymentOrders: (page) =>
        page ? `/api/payment_orders?page=${page}` : `/api/payment_orders/`,
      getPaymentOrderByID: (id) => `/api/payment_orders/${id}`,
      downloadPaymentOrderInvoiceByID: (id) =>
        `/api/payment_orders/invoice/${id}`,
    },

    FCM: {
      registerDeviceToken: () => "/api/users/register_device",
    },
    VEHICLES: {
      getNearby: ({ latitude, longitude }) =>
        `/api/vehicles/nearby?lat=${latitude}&lng=${longitude}`,
      getDeliverables: () => `/api/vehicles/deliverables`,
      getById: (vehicleId) => `/api/vehicles/${vehicleId}`,
      getByVehicleCode: (vehicleCode) => `/api/vehicles/code/${vehicleCode}`,
      ringVehicle: (vehicleId) => `/api/vehicles/ring/${vehicleId}`,
    },
    STATIONS: {
      getNearby: ({ latitude, longitude }) =>
        `/api/stations/nearby?lat=${latitude}&lng=${longitude}`,
      getById: (stationId) => `/api/stations/${stationId}`,
    },
    ADVERTISES: {
      getMyAdvertises: () => `/api/advertises/my`,
    },
    // BOOKING: {
    //   getCompletedBookings: (page) =>
    //     page ? `/api/bookings?page=${page}` : `/api/bookings/`,
    //   getBookingByID: (id) => `/api/bookings/${id}`,
    //   ongoing: () => `/api/bookings/ongoing`,
    //   reserve: () => `/api/bookings/reserve_ride`,
    //   cancelReservation: () => `/api/bookings/cancel_reservation`,
    //   start: () => `/api/bookings/start`,
    //   pause: () => `/api/bookings/pause`,
    //   resume: () => `/api/bookings/resume`,
    //   end: () => `/api/bookings/end`,
    //   checkOutstation: () => `/api/bookings/check_outstation_v2`,
    //   cancel: () => `/api/bookings/cancel`,
    //   retryEndRidePayment: () => `/api/bookings/retry_end_payment`,
    // },
    RESERVATION: {
      ongoing: () => `/api/reservations/ongoing`,
      reserve: () => `/api/reservations/reserve`,
      end: () => `/api/reservations/end`,
      getCompletedReservations: (page) =>
        page ? `/api/reservations?page=${page}` : `/api/reservations/`,
      getReservationByID: (id) => `/api/reservations/${id}`,
    },
    DELIVERY_BOOKING: {
      getDeliveryBookings: (page) =>
        page
          ? `/api/delivery_bookings?page=${page}`
          : `/api/delivery_bookings/`,
      getDeliveryBookingByID: (id) => `/api/delivery_bookings/${id}`,
      request: () => `/api/delivery_bookings/request`,
    },
    RECEIPT: {
      generateBookingReceipt: () => `/api/receipts/generate_booking_receipt`,
      generateReservationReceipt: () =>
        `/api/receipts/generate_reservation_receipt`,
    },
    PAYMENT: {
      addCard: () => `/api/payments/add_card`,
      getSetupIntent: () => `/api/payments/setup_intent`,
      getPaymentMethodById: (paymentMethodID) =>
        `/api/payments/payment_method/${paymentMethodID}`,
    },
  },

  COUPON_CATEGORIES: {
    FREE_RIDE_MINUTES: "FREE_RIDE_MINUTES",
    RIDE_TIME_FARE_PERCENTAGE_REDUCTION: "RIDE_TIME_FARE_PERCENTAGE_REDUCTION",
    RIDE_TIME_FARE_DIRECT_REDUCTION: "RIDE_TIME_FARE_DIRECT_REDUCTION",
    RIDE_TIME_CHARGE_REPLACEMENT: "RIDE_TIME_CHARGE_REPLACEMENT",

    FREE_PAUSE_MINUTES: "FREE_PAUSE_MINUTES",
    PAUSE_TIME_FARE_PERCENTAGE_REDUCTION:
      "PAUSE_TIME_FARE_PERCENTAGE_REDUCTION",
    PAUSE_TIME_FARE_DIRECT_REDUCTION: "PAUSE_TIME_FARE_DIRECT_REDUCTION",
    PAUSE_TIME_CHARGE_REPLACEMENT: "PAUSE_TIME_CHARGE_REPLACEMENT",

    FREE_RESERVATION_MINUTES: "FREE_RESERVATION_MINUTES",
    RESERVATION_TIME_FARE_PERCENTAGE_REDUCTION:
      "RESERVATION_TIME_FARE_PERCENTAGE_REDUCTION",
    RESERVATION_TIME_FARE_DIRECT_REDUCTION:
      "RESERVATION_TIME_FARE_DIRECT_REDUCTION",
    RESERVATION_TIME_CHARGE_REPLACEMENT: "RESERVATION_TIME_CHARGE_REPLACEMENT",

    FREE_UNLOCKS: "FREE_UNLOCKS",
    UNLOCK_FARE_PERCENTAGE_REDUCTION: "UNLOCK_FARE_PERCENTAGE_REDUCTION",
    UNLOCK_FARE_DIRECT_REDUCTION: "UNLOCK_FARE_DIRECT_REDUCTION",
    UNLOCK_CHARGE_REPLACEMENT: "UNLOCK_CHARGE_REPLACEMENT",
  },

  SOCKET_EVENTS: {
    ON_VEHICLES_UPDATE: "ON_VEHICLES_UPDATE",
    ON_STATION_UPDATE: "ON_STATION_UPDATE",
    ON_OFFER_TYPES_UPDATE: "ON_OFFER_TYPES_UPDATE",
    ON_CURRENT_VEHICLE_UPDATE: "ON_CURRENT_VEHICLE_UPDATE",
    ON_USER_UPDATE: "ON_USER_UPDATE",
    ON_WALLET_UPDATE: "ON_WALLET_UPDATE",
    ON_ONGOING_BOOKING_UPDATE: "ON_ONGOING_BOOKING_UPDATE",
    ON_ONGOING_RESERVATION_UPDATE: "ON_ONGOING_RESERVATION_UPDATE",
    ON_CURRENT_PAYMENT_ORDER_UPDATE: "ON_CURRENT_PAYMENT_ORDER_UPDATE",
    ON_PAYMENT_TRANSACTION_UPDATE: "ON_PAYMENT_TRANSACTION_UPDATE",
    ON_SERVER_SIDE_ERROR: "ON_SERVER_SIDE_ERROR",
  },

  // VEHICLE_COMMANDS: {
  //   lock: bleToken => `"${bleToken}","lock"`,
  //   unlock: bleToken => `"${bleToken}","unlock"`,
  // },

  VEHICLE_COMMANDS: {
    lock: (bleToken) => `{"cmd": "lock","code": "${bleToken}"}`,
    unlock: (bleToken) => `{"cmd": "unlock","code": "${bleToken}"}`,
  },

  PAYMENT_PURPOSE: {
    PLAN_PURCHASE: {
      label: "Plan purchase payment",
    },
    SECURITY_DEPOSIT: { label: "Security deposit payment" },
    WALLET_RECHARGE: {
      label: "Wallet recharge payment",
    },
    SUBSCRIPTION_PURCHASE: {
      label: "Subscription purchase payment",
    },
    UNKNOWN: { label: "Error getting payment information" },
  },

  PAYMENT_ORDER_STATUS_CODE: {
    CREATED: "created",
    ATTEMPTED: "attempted",
    PAID: "paid",
    FAILED: "failed",
  },

  PAYMENT_ORDER_STATUS: {
    created: {
      label: "Payment Initiated",
      bg: "#d6af13",
      color: "#ffffff",
      icon: "clock",
    },
    attempted: {
      label: "Payment pending",
      bg: "#d6af13",
      color: "#000000",
      icon: "clock",
    },
    paid: {
      label: "Payment success",
      bg: "#32920f",
      color: "#ffffff",
      icon: "check-circle",
    },
    failed: {
      label: "Payment failed",
      bg: "#ff6868",
      color: "#ffffff",
      icon: "close-circle",
    },
  },

  PAYMENT_TRANSACTION_STATUS: {
    created: { label: "Payment Initiated", bg: "#d6af13", color: "#ffffff" },
    authorized: { label: "Payment Initiated", bg: "#d6af13", color: "#ffffff" },
    captured: { label: "Payment success", bg: "#32920f", color: "#ffffff" },
    failed: { label: "Payment error", bg: "#ff6868", color: "#ffffff" },
    refunded: { label: "Payment success", bg: "#32920f", color: "#ffffff" },
  },

  INTER_WALLET_TRANSACTION_STATUS_CODE: {
    DEFAULT: "DEFAULT",
    PENDING: "PENDING",
    SUCCESS: "SUCCESS",
    FAILED: "FAILED",
    CANCELLED: "CANCELLED",
    REFUND: "REFUND",
  },

  INTER_WALLET_TRANSACTION_STATUS: {
    DEFAULT: {
      label: "Wallet payment initiated",
      bg: "#d6af13",
      color: "#ffffff",
      icon: "clock",
    },
    PENDING: {
      label: "Wallet payment initiated",
      bg: "#d6af13",
      color: "#ffffff",
      icon: "clock",
    },
    SUCCESS: {
      label: "Wallet payment success",
      bg: "#32920f",
      color: "#ffffff",
      icon: "check-circle",
    },
    FAILED: {
      label: "Wallet payment error",
      bg: "#ff6868",
      color: "#ffffff",
      icon: "close-circle",
    },
    CANCELLED: {
      label: "Wallet payment error",
      bg: "#ff6868",
      color: "#ffffff",
      icon: "close-circle",
    },
    REFUND: {
      label: "Wallet payment refunded",
      bg: "#ff6868",
      color: "#ffffff",
      icon: "credit-card-refund",
    },
  },

  PAYMENT_METHOD_CODE: {
    NETBANKING: "netbanking",
    CARD: "card",
    WALLET: "wallet",
    UPI: "upi",
  },

  PAYMENT_METHOD_DATA: {
    netbanking: { field: "paymentTransactionBank" },
    card: { field: "paymentTransactionCard" },
    wallet: { field: "paymentTransactionWallet" },
    upi: { field: "paymentTransactionVpa" },
  },

  PAYMENT_SOURCE: {
    WALLET: { value: "WALLET", label: "Wallet" },
    RAZORPAY: { value: "RAZORPAY", label: "Payment gateway" },
    WALLET_RAZORPAY: { value: "WALLET_RAZORPAY", label: "Use wallet balance" },
    SECURITY_DEPOSIT_BONUS: {
      value: "SECURITY_DEPOSIT_BONUS",
      label: "Security deposit bonus",
    },
    SIGNUP_BONUS: { value: "SIGNUP_BONUS", label: "Sign up bonus" },
  },

  ERROR_CODES: {
    APPLICATION_ERROR: {
      code: "APPLICATION_ERROR",
      message: "Internal Application error occured!",
    },
    VEHICLE_DID_NOT_RESPOND: {
      code: "VEHICLE_DID_NOT_RESPOND",
      message: "Vehicle did not respond. Please try again",
    },
    NO_RESPONSE_FROM_SERVER: {
      code: "NO_RESPONSE_FROM_SERVER",
      message: "No response from server",
    },
    SERVER_ERROR: {
      code: "SERVER_ERROR",
      message: "Server error",
    },
    USER_AUTH_TOKEN_NOT_FOUND: {
      code: "USER_AUTH_TOKEN_NOT_FOUND",
      message: "User auth token not found",
    },
    BLUETOOTH_OFF: {
      code: "BLUETOOTH_OFF",
      message: "Bluetooth is off",
    },
    BLE_NOT_FOUND: {
      code: "BLE_NOT_FOUND",
      message: "Vehicle not within range or vehicle is not operational",
    },
    VEHICLE_NOT_PARKED_NEAR_STATION: {
      code: "VEHICLE_NOT_PARKED_NEAR_STATION",
      message: "Vehicle not parked near station",
    },
  },

  BLE_STATE: {
    ON: "PoweredOn",
    OFF: "PoweredOff",
  },

  RESERVATION_STATUS: {
    RESERVED: {
      value: "RESERVED",
      label: "Riding",
      color: "#1398d6",
      bg: "#ddf4ff",
    },
    ENDED: {
      value: "ENDED",
      label: "Reservation completed",
      color: "#2e9616",
      bg: "#e4ffdb",
    },
    TERMINATED: {
      value: "TERMINATED",
      label: "Terminated",
      color: "#ff6868",
      bg: "#ffe4e4",
    },
    WAITING: {
      value: "WAITING",
      label: "Waiting...",
      color: "#ff6868",
      bg: "#ffe4e4",
    },
  },

  BOOKING_STATUS: {
    PENDING: "PENDING",
    DRIVER_ASSIGNED: "DRIVER_ASSIGNED",
    DRIVER_REACHED: "DRIVER_REACHED",
    RIDING: "RIDING",
    CANCELLED: "CANCELLED",
    COMPLETED: "COMPLETED",
  },

  // BOOKING_STATUS: {
  //   RIDING: {
  //     value: "RIDING",
  //     label: "Riding",
  //     actionButtonIcon: "lock-outline",
  //     color: "#1398d6",
  //     bg: "#ddf4ff",
  //   },
  //   PAUSED: {
  //     value: "PAUSED",
  //     label: "Locked",
  //     actionButtonIcon: "lock-open-variant-outline",
  //     color: "#000",
  //     bg: "#d6af13",
  //   },
  //   ENDED: {
  //     value: "ENDED",
  //     label: "Ride completed",
  //     actionButtonIcon: "",
  //     color: "#2e9616",
  //     bg: "#e4ffdb",
  //   },
  //   INITIATED_RIDE: {
  //     value: "INITIATED_RIDE",
  //     label: "Booking your ride...",
  //     actionButtonIcon: "",
  //     color: "#ae8c06",
  //     bg: "#fff7d6",
  //   },
  //   INITIATED_PAUSE: {
  //     value: "INITIATED_PAUSE",
  //     label: "Locking your ride...",
  //     actionButtonIcon: "",
  //     color: "#000",
  //     bg: "#d6af13",
  //   },
  //   INITIATED_RESUME: {
  //     value: "INITIATED_RESUME",
  //     label: "Unlocking your ride...",
  //     actionButtonIcon: "",
  //     color: "#000",
  //     bg: "#d6af13",
  //   },
  //   INITIATED_END: {
  //     value: "INITIATED_END",
  //     label: "Ending your ride...",
  //     actionButtonIcon: "",
  //     color: "#000",
  //     bg: "#d6af13",
  //   },
  //   TERMINATED: {
  //     value: "TERMINATED",
  //     label: "Ride terminated",
  //     actionButtonIcon: "",
  //     color: "#ff6868",
  //     bg: "#ffe4e4",
  //   },
  //   CANCELLED: {
  //     value: "CANCELLED",
  //     label: "Ride cancelled",
  //     actionButtonIcon: "",
  //     color: "#6E6E6E",
  //     bg: "#DFDFDF",
  //   },
  //   ERROR: {
  //     value: "ERROR",
  //     label: "Error in ride...",
  //     actionButtonIcon: "",
  //     color: "#ff6868",
  //     bg: "#ffe4e4",
  //   },
  // },

  DELIVERY_BOOKING_STATUS: {
    RIDING: {
      value: "RIDING",
      label: "Riding",
      actionButtonIcon: "lock-outline",
      color: "#1398d6",
      bg: "#ddf4ff",
    },
    REQUESTED: {
      value: "REQUESTED",
      label: "Assigning vehicle...",
      actionButtonIcon: "lock-open-variant-outline",
      color: "#000",
      bg: "#d6af13",
    },
    ENDED: {
      value: "ENDED",
      label: "Booking completed",
      actionButtonIcon: "",
      color: "#2e9616",
      bg: "#e4ffdb",
    },
    VEHICLE_ASSIGNED: {
      value: "VEHICLE_ASSIGNED",
      label: "Vehicle assigned",
      actionButtonIcon: "",
      color: "#74e44c",
      bg: "#daffdf",
    },
    ON_DELIVERY: {
      value: "ON_DELIVERY",
      label: "Out for delivery",
      actionButtonIcon: "",
      color: "#000",
      bg: "#d6af13",
    },

    TERMINATED: {
      value: "TERMINATED",
      label: "Booking terminated",
      actionButtonIcon: "",
      color: "#ff6868",
      bg: "#ffe4e4",
    },
    CANCELLED: {
      value: "CANCELLED",
      label: "Booking cancelled",
      actionButtonIcon: "",
      color: "#ff6868",
      bg: "#ffe4e4",
    },
  },

  VEHICLE_STATUS: {
    READY: { value: "READY", text1: "Off", text1Color: "#FF5245" },
    RIDING: { value: "RIDING", text1: "On", text1Color: "#27AE60" },
    PAUSED: { value: "PAUSED", text1: "Off", text1Color: "#FF5245" },
    MAINTAINANCE: {
      value: "MAINTAINANCE",
      text1: "Off",
      text1Color: "#FF5245",
    },
    INITIATED_UNLOCK: {
      value: "INITIATED_UNLOCK",
      text1: "Off",
      text1Color: "#FF5245",
    },
    INITIATED_LOCK_TEMP: {
      value: "INITIATED_LOCK_TEMP",
      text1: "On",
      text1Color: "#27AE60",
    },
    INITIATED_UNLOCK_TEMP: {
      value: "INITIATED_UNLOCK_TEMP",
      text1: "Off",
      text1Color: "#FF5245",
    },
    INITIATED_LOCK: {
      value: "INITIATED_LOCK",
      text1: "On",
      text1Color: "#27AE60",
    },
    RESERVED: { value: "RESERVED", text1: "Off", text1Color: "#FF5245" },
  },
  BOOKING_TYPE: {
    SCHEDULED: "SCHEDULED",
    RENTAL: "RENTAL",
  },
  REACT_QUERY_KEYS: {
    BOOKINGS: "BOOKINGS",
    ONGOING_BOOKING: "ONGOING_BOOKING",
    ONGOING_RESERVATION: "ONGOING_RESERVATION",
    DB_DRIVER: "DB_DRIVER",
    LOCATION_OFFERS_MAP: "LOCATION_OFFERS_MAP",
    SUBSCRIPTIONS: "SUBSCRIPTIONS",
    PLANS: "PLANS",
    SUBSCRIPTION_TYPES: "SUBSCRIPTION_TYPES",
    RESERVATION_TYPES: "RESERVATION_TYPES",
    PLAN_TYPES: "PLAN_TYPES",
    MACHINE_REMINDER: "MACHINE_REMINDER",
    BOOKING_HISTORY: "BOOKING_HISTORY",
    DELIVERY_BOOKING_HISTORY: "DELIVERY_BOOKING_HISTORY",
    RESERVATION_HISTORY: "RESERVATION_HISTORY",
    WALLET: "WALLET",
    PAYMENT_ORDER_HISTORY: "PAYMENT_ORDER_HISTORY",
    NEAREST_STATION: "NEAREST_STATION",
    VEHICLES: "VEHICLES",
    NEARBY_STATIONS: "NEARBY_STATIONS",
    OFFERS: "OFFERS",
    SUPPORT_TICKETS: "SUPPORT_TICKETS",
    STATIONS: "STATIONS",
    DELIVERABLE_VEHICLE_TYPES: "DELIVERABLE_VEHICLE_TYPES",
    ADVERTISES: "ADVERTISES",
  },

  // in milliseconds
  REACT_QUERY_REFETCH_INTERVALS: {
    VEHICLES: 30000,
  },

  DELIVERY_DURATIONS: {
    "1 day": { label: "1 day", duration: 86400000 },
    "1 week": { label: "1 week", duration: 604800000 },
    "1 month": { label: "1 month", duration: 2592000000 },
  },

  IOT_VALID_RESPONSE: {
    VEHICLE_START: ["DOUT1:1", "DOUT1:Already set to 1"],
    VEHICLE_STOP: ["DOUT1:0", "DOUT1:Already set to 0"],
  },

  SUPPORT_PHONE: "7378880223",
};

import {StyleSheet} from 'react-native';
import {theme} from './theme';

export const GLOBAL_STYLES = StyleSheet.create({
  primary_outlined_button: {
    borderWidth: 1,
    borderColor: theme.colors.onSurface,
    borderRadius: 10,
    backgroundColor: '#fff',
    color: theme.colors.onSurface,
    width: '100%',
  },
  primary_contained_button: {
    borderWidth: 1,
    borderRadius: 10,
    width: '100%',
  },
  font_family: {fontFamily: 'Montserrat-SemiBold'},
  button_text: {fontFamily: 'Montserrat-SemiBold'},
  elevation_style: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,

    elevation: 2,
  },
  elevation_style_5: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,

    elevation: 5,
  },
  mild_elevation_style: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 1,
    },
    shadowOpacity: 0.2,
    shadowRadius: 1.41,
    elevation: 2,
  },
});

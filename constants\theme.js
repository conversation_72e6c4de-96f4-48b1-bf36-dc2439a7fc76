import { MD3LightTheme as DefaultTheme } from 'react-native-paper';
export const theme = {
  ...DefaultTheme,
  animation: {
    scale: 1,
  },
  colors: {
    backdrop: "rgba(50, 47, 55, 0.4)",
    background: "rgba(255, 255, 255, 1)",
    directionButton: "#3273E9",
    elevation: {
      level0: "transparent",
      level1: "rgb(247, 243, 249)",
      level2: "rgb(243, 237, 246)",
      level3: "rgb(238, 232, 244)",
      level4: "rgb(236, 230, 243)",
      level5: "rgb(233, 227, 241)",
    },
    alertShade1: "#EA592B",
    error: "rgba(179, 38, 30, 1)",
    errorContainer: "rgba(249, 222, 220, 1)",
    warning: "#EA9D2B",
    warningContainer: "#FFF3E0",
    inverseOnSurface: "#F5F5F5FF",
    inversePrimary: "rgba(208, 188, 255, 1)",
    inverseSurface: "rgba(49, 48, 51, 1)",
    onBackground: "rgba(28, 27, 31, 1)",
    onError: "rgba(255, 255, 255, 1)",
    onErrorContainer: "rgba(65, 14, 11, 1)",
    onPrimary: "#CBDF75",
    onPrimaryLight: "#eeffa0",
    onPrimaryVariant: "#8CD3AC",
    onPrimaryContainer: "rgba(33, 0, 93, 1)",
    onSecondary: "rgba(255, 255, 255, 1)",
    onSecondaryContainer: "rgba(29, 25, 43, 1)",
    onSurface: "#1f1f1f",
    onSurfaceDisabled: "rgba(28, 27, 31, 0.38)",
    onSurfaceVariant: "rgba(166, 166, 166, 1)",
    onTertiary: "rgba(255, 255, 255, 1)",
    onTertiaryContainer: "rgba(49, 17, 29, 1)",
    outline: "rgba(121, 116, 126, 1)",
    outlineVariant: "rgba(202, 196, 208, 1)",
    primary: "#1f1f1f",
    primaryContainer: "rgba(234, 221, 255, 1)",
    scrim: "rgba(0, 0, 0, 1)",
    secondary: "rgba(120, 120, 120, 1)",
    secondaryContainer: "rgba(232, 222, 248, 1)",
    shadow: "rgba(0, 0, 0, 1)",
    surface: "rgba(255, 255, 255, 1)",
    surfaceDisabled: "rgba(28, 27, 31, 0.12)",
    surfaceVariant: "rgba(210, 210, 210, 1)",
    tertiary: "rgba(125, 82, 96, 1)",
    tertiaryContainer: "rgba(255, 216, 228, 1)",
    success: "#3dbd2a",

    consentBoxBorder: "#1B80DC",
    consentBoxBg: "#E0E9FF",
    consentBoxText: "#1B80DC",
    unlockToRideDisabledThumbBg: "#BDBDBD",
    unlockToRideDisabledRailBg: "#E0E0E0",
    unlockToRideEnabledThumbBg: "#CBDF75",
    unlockToRideEnabledRailBg: "#000",
    unlockToRideDisabledText: "#828282",
    unlockToRideEnabledText: "#fff",
    vehicleUnavailable: "#ffe53e",
    info: "#4fbcff",
    infoBackground: "rgba(45, 156, 219, 0.2)",
    invoiceBackground: "rgba(238, 238, 238, 1)",
  },
  dark: false,
  fonts: {
    bodyLarge: {
      fontFamily: "sans-serif",
      fontSize: 16,
      fontWeight: "400",
      letterSpacing: 0.15,
      lineHeight: 24,
    },
    bodyMedium: {
      fontFamily: "sans-serif",
      fontSize: 14,
      fontWeight: "400",
      letterSpacing: 0.25,
      lineHeight: 20,
    },
    bodySmall: {
      fontFamily: "sans-serif",
      fontSize: 12,
      fontWeight: "400",
      letterSpacing: 0.4,
      lineHeight: 16,
    },
    default: {
      fontFamily: "sans-serif",
      fontWeight: "400",
      letterSpacing: 0,
    },
    displayLarge: {
      fontFamily: "sans-serif",
      fontSize: 57,
      fontWeight: "400",
      letterSpacing: 0,
      lineHeight: 64,
    },
    displayMedium: {
      fontFamily: "sans-serif",
      fontSize: 45,
      fontWeight: "400",
      letterSpacing: 0,
      lineHeight: 52,
    },
    displaySmall: {
      fontFamily: "sans-serif",
      fontSize: 36,
      fontWeight: "400",
      letterSpacing: 0,
      lineHeight: 44,
    },
    headlineLarge: {
      fontFamily: "sans-serif",
      fontSize: 32,
      fontWeight: "400",
      letterSpacing: 0,
      lineHeight: 40,
    },
    headlineMedium: {
      fontFamily: "sans-serif",
      fontSize: 28,
      fontWeight: "400",
      letterSpacing: 0,
      lineHeight: 36,
    },
    headlineSmall: {
      fontFamily: "sans-serif",
      fontSize: 24,
      fontWeight: "400",
      letterSpacing: 0,
      lineHeight: 32,
    },
    labelLarge: {
      fontFamily: "sans-serif-medium",
      fontSize: 14,
      fontWeight: "500",
      letterSpacing: 0.1,
      lineHeight: 20,
    },
    labelMedium: {
      fontFamily: "sans-serif-medium",
      fontSize: 12,
      fontWeight: "500",
      letterSpacing: 0.5,
      lineHeight: 16,
    },
    labelSmall: {
      fontFamily: "sans-serif-medium",
      fontSize: 11,
      fontWeight: "500",
      letterSpacing: 0.5,
      lineHeight: 16,
    },
    titleLarge: {
      fontFamily: "sans-serif",
      fontSize: 22,
      fontWeight: "400",
      letterSpacing: 0,
      lineHeight: 28,
    },
    titleMedium: {
      fontFamily: "sans-serif-medium",
      fontSize: 16,
      fontWeight: "500",
      letterSpacing: 0.15,
      lineHeight: 24,
    },
    titleSmall: {
      fontFamily: "sans-serif-medium",
      fontSize: 14,
      fontWeight: "500",
      letterSpacing: 0.1,
      lineHeight: 20,
    },
  },
  isV3: true,
  roundness: 4,
  version: 3,
};

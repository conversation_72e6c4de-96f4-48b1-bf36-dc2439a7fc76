co.auter.hcorp:xml/rn_dev_preferences = 0x7f130003
co.auter.hcorp:styleable/ViewPager2 = 0x7f120098
co.auter.hcorp:styleable/ViewBackgroundHelper = 0x7f120097
co.auter.hcorp:styleable/View = 0x7f120096
co.auter.hcorp:styleable/Variant = 0x7f120095
co.auter.hcorp:styleable/Tooltip = 0x7f120092
co.auter.hcorp:styleable/ThemeEnforcement = 0x7f120090
co.auter.hcorp:styleable/TextAppearance = 0x7f12008d
co.auter.hcorp:styleable/TabItem = 0x7f12008b
co.auter.hcorp:styleable/SwitchMaterial = 0x7f12008a
co.auter.hcorp:styleable/SwipeRefreshLayout = 0x7f120088
co.auter.hcorp:styleable/StateSet = 0x7f120087
co.auter.hcorp:styleable/StateListDrawableItem = 0x7f120086
co.auter.hcorp:styleable/State = 0x7f120084
co.auter.hcorp:styleable/SnackbarLayout = 0x7f120082
co.auter.hcorp:styleable/SimpleDraweeView = 0x7f12007f
co.auter.hcorp:styleable/SignInButton = 0x7f12007e
co.auter.hcorp:styleable/SearchBar = 0x7f120079
co.auter.hcorp:styleable/ScrollingViewBehavior_Layout = 0x7f120078
co.auter.hcorp:styleable/ScrimInsetsFrameLayout = 0x7f120077
co.auter.hcorp:styleable/SVGImageView = 0x7f120076
co.auter.hcorp:styleable/RangeSlider = 0x7f120073
co.auter.hcorp:styleable/PopupWindow = 0x7f12006f
co.auter.hcorp:styleable/MotionTelltales = 0x7f120068
co.auter.hcorp:styleable/MotionScene = 0x7f120067
co.auter.hcorp:styleable/MotionLayout = 0x7f120066
co.auter.hcorp:styleable/MotionHelper = 0x7f120065
co.auter.hcorp:styleable/Motion = 0x7f120064
co.auter.hcorp:styleable/MockView = 0x7f120063
co.auter.hcorp:styleable/MaterialTextView = 0x7f12005d
co.auter.hcorp:styleable/MaterialTextAppearance = 0x7f12005c
co.auter.hcorp:styleable/MaterialRadioButton = 0x7f120059
co.auter.hcorp:styleable/MaterialDivider = 0x7f120058
co.auter.hcorp:styleable/MaterialCheckBoxStates = 0x7f120057
co.auter.hcorp:styleable/KeyTrigger = 0x7f120047
co.auter.hcorp:styleable/KeyFramesAcceleration = 0x7f120043
co.auter.hcorp:styleable/KeyFrame = 0x7f120042
co.auter.hcorp:styleable/Insets = 0x7f12003f
co.auter.hcorp:styleable/ImageFilterView = 0x7f12003e
co.auter.hcorp:styleable/GenericDraweeHierarchy = 0x7f12003b
co.auter.hcorp:styleable/FragmentContainerView = 0x7f12003a
co.auter.hcorp:styleable/FlowLayout = 0x7f120035
co.auter.hcorp:styleable/FloatingActionButton = 0x7f120033
co.auter.hcorp:styleable/ExtendedFloatingActionButton = 0x7f120031
co.auter.hcorp:styleable/DrawerArrowToggle = 0x7f12002f
co.auter.hcorp:styleable/ConstraintLayout_Layout = 0x7f120029
co.auter.hcorp:styleable/CollapsingToolbarLayout = 0x7f120024
co.auter.hcorp:styleable/ClockFaceView = 0x7f120022
co.auter.hcorp:styleable/CircularProgressIndicator = 0x7f120021
co.auter.hcorp:styleable/ChipGroup = 0x7f120020
co.auter.hcorp:styleable/Chip = 0x7f12001f
co.auter.hcorp:styleable/Carousel = 0x7f12001d
co.auter.hcorp:styleable/ButtonBarLayout = 0x7f12001a
co.auter.hcorp:styleable/BottomAppBar = 0x7f120017
co.auter.hcorp:styleable/Badge = 0x7f120014
co.auter.hcorp:styleable/AppCompatTheme = 0x7f120012
co.auter.hcorp:styleable/AppCompatTextHelper = 0x7f120010
co.auter.hcorp:styleable/AppCompatEmojiHelper = 0x7f12000d
co.auter.hcorp:styleable/AnimatedStateListDrawableTransition = 0x7f120009
co.auter.hcorp:styleable/AlertDialog = 0x7f120006
co.auter.hcorp:styleable/ActionMode = 0x7f120004
co.auter.hcorp:styleable/ActionMenuView = 0x7f120003
co.auter.hcorp:style/redboxButton = 0x7f1104a3
co.auter.hcorp:style/Widget.MaterialComponents.Tooltip = 0x7f1104a1
co.auter.hcorp:style/Widget.MaterialComponents.Toolbar.PrimarySurface = 0x7f11049f
co.auter.hcorp:style/Widget.MaterialComponents.TimePicker.ImageButton = 0x7f11049b
co.auter.hcorp:style/Widget.MaterialComponents.TimePicker.Display.HelperText = 0x7f110498
co.auter.hcorp:style/Widget.MaterialComponents.TimePicker.Button = 0x7f110494
co.auter.hcorp:style/Widget.MaterialComponents.TextView = 0x7f110492
co.auter.hcorp:style/Widget.MaterialComponents.TextInputLayout.FilledBox.ExposedDropdownMenu = 0x7f11048d
co.auter.hcorp:style/Widget.MaterialComponents.TextInputEditText.OutlinedBox = 0x7f110488
co.auter.hcorp:style/Widget.MaterialComponents.TextInputEditText.FilledBox = 0x7f110486
co.auter.hcorp:style/Widget.MaterialComponents.TabLayout.PrimarySurface = 0x7f110485
co.auter.hcorp:style/Widget.MaterialComponents.TabLayout.Colored = 0x7f110484
co.auter.hcorp:style/Widget.MaterialComponents.Snackbar.TextView = 0x7f110482
co.auter.hcorp:style/Widget.MaterialComponents.Snackbar.FullWidth = 0x7f110481
co.auter.hcorp:style/Widget.MaterialComponents.ProgressIndicator = 0x7f11047d
co.auter.hcorp:style/Widget.MaterialComponents.PopupMenu.ListPopupWindow = 0x7f11047b
co.auter.hcorp:style/Widget.MaterialComponents.PopupMenu = 0x7f110479
co.auter.hcorp:style/Widget.MaterialComponents.MaterialDivider = 0x7f110472
co.auter.hcorp:style/Widget.MaterialComponents.MaterialCalendar.Year.Selected = 0x7f11046f
co.auter.hcorp:style/Widget.MaterialComponents.MaterialCalendar.MonthNavigationButton = 0x7f11046c
co.auter.hcorp:style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection = 0x7f110467
co.auter.hcorp:style/Widget.MaterialComponents.MaterialCalendar.Fullscreen = 0x7f110461
co.auter.hcorp:style/Widget.MaterialComponents.MaterialCalendar.Day = 0x7f11045b
co.auter.hcorp:style/Widget.MaterialComponents.MaterialCalendar = 0x7f11045a
co.auter.hcorp:style/Widget.MaterialComponents.LinearProgressIndicator = 0x7f110458
co.auter.hcorp:style/Widget.MaterialComponents.ExtendedFloatingActionButton = 0x7f110454
co.auter.hcorp:style/Widget.MaterialComponents.CompoundButton.Switch = 0x7f110453
co.auter.hcorp:style/Widget.MaterialComponents.CompoundButton.CheckBox = 0x7f110451
co.auter.hcorp:style/Widget.MaterialComponents.CircularProgressIndicator.ExtraSmall = 0x7f11044d
co.auter.hcorp:style/Widget.MaterialComponents.ChipGroup = 0x7f11044b
co.auter.hcorp:style/Widget.MaterialComponents.CardView = 0x7f110445
co.auter.hcorp:style/Widget.MaterialComponents.Button.TextButton.Dialog.Icon = 0x7f110440
co.auter.hcorp:style/Widget.MaterialComponents.Button.TextButton.Dialog.Flush = 0x7f11043f
co.auter.hcorp:style/Widget.MaterialComponents.Button.Icon = 0x7f11043a
co.auter.hcorp:style/Widget.MaterialComponents.BottomSheet.Modal = 0x7f110438
co.auter.hcorp:style/Widget.MaterialComponents.BottomNavigationView.PrimarySurface = 0x7f110436
co.auter.hcorp:style/Widget.MaterialComponents.BottomSheet = 0x7f110437
co.auter.hcorp:style/Widget.MaterialComponents.BottomNavigationView = 0x7f110434
co.auter.hcorp:style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox = 0x7f11042e
co.auter.hcorp:style/Widget.MaterialComponents.ActionMode = 0x7f110428
co.auter.hcorp:style/Widget.MaterialComponents.ActionBar.Surface = 0x7f110427
co.auter.hcorp:style/Widget.MaterialComponents.ActionBar.Primary = 0x7f110424
co.auter.hcorp:style/Widget.Material3.Toolbar = 0x7f110420
co.auter.hcorp:style/Widget.Material3.TextInputLayout.OutlinedBox.ExposedDropdownMenu = 0x7f11041f
co.auter.hcorp:style/Widget.Material3.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu = 0x7f11041e
co.auter.hcorp:style/Widget.Material3.TextInputLayout.OutlinedBox.Dense = 0x7f11041d
co.auter.hcorp:style/Widget.Material3.TextInputLayout.OutlinedBox = 0x7f11041c
co.auter.hcorp:style/Widget.Material3.TextInputLayout.FilledBox.ExposedDropdownMenu = 0x7f11041b
co.auter.hcorp:style/Widget.Material3.TextInputLayout.FilledBox = 0x7f110418
co.auter.hcorp:style/Widget.Material3.TextInputEditText.FilledBox.Dense = 0x7f110415
co.auter.hcorp:style/Widget.Material3.TextInputEditText.FilledBox = 0x7f110414
co.auter.hcorp:style/Widget.Material3.TabLayout.OnSurface = 0x7f110412
co.auter.hcorp:style/Widget.Material3.Snackbar = 0x7f11040e
co.auter.hcorp:style/Widget.Material3.Slider.Legacy.Label = 0x7f11040d
co.auter.hcorp:style/Widget.Material3.Slider = 0x7f11040a
co.auter.hcorp:style/Widget.Material3.SearchView.Toolbar = 0x7f110405
co.auter.hcorp:style/Widget.Material3.SearchView.Prefix = 0x7f110404
co.auter.hcorp:style/Widget.Material3.SearchBar.Outlined = 0x7f110402
co.auter.hcorp:style/Widget.Material3.Search.Toolbar.Button.Navigation = 0x7f110400
co.auter.hcorp:style/Widget.Material3.PopupMenu.Overflow = 0x7f1103fe
co.auter.hcorp:style/Widget.Material3.NavigationView = 0x7f1103fa
co.auter.hcorp:style/Widget.Material3.NavigationRailView = 0x7f1103f7
co.auter.hcorp:style/Widget.Material3.MaterialTimePicker.Display.TextInputLayout = 0x7f1103f5
co.auter.hcorp:style/Widget.Material3.MaterialTimePicker.Display.TextInputEditText = 0x7f1103f4
co.auter.hcorp:style/Widget.Material3.MaterialTimePicker.Display.HelperText = 0x7f1103f3
co.auter.hcorp:style/Widget.Material3.MaterialTimePicker.Display = 0x7f1103f1
co.auter.hcorp:style/Widget.Material3.MaterialTimePicker.Button = 0x7f1103ef
co.auter.hcorp:style/Widget.Material3.MaterialCalendar.YearNavigationButton = 0x7f1103eb
co.auter.hcorp:style/Widget.Material3.MaterialCalendar.MonthTextView = 0x7f1103e7
co.auter.hcorp:style/Widget.Material3.MaterialCalendar.Item = 0x7f1103e5
co.auter.hcorp:style/Widget.Material3.MaterialCalendar.HeaderToggleButton = 0x7f1103e4
co.auter.hcorp:styleable/OnSwipe = 0x7f12006e
co.auter.hcorp:style/Widget.Material3.MaterialCalendar.HeaderTitle = 0x7f1103e3
co.auter.hcorp:style/Widget.Material3.MaterialCalendar.HeaderDivider = 0x7f1103de
co.auter.hcorp:style/Widget.Material3.MaterialCalendar.Day = 0x7f1103d6
co.auter.hcorp:style/Widget.MaterialComponents.PopupMenu.Overflow = 0x7f11047c
co.auter.hcorp:style/Widget.Material3.FloatingActionButton.Small.Surface = 0x7f1103cd
co.auter.hcorp:style/Widget.Material3.FloatingActionButton.Small.Secondary = 0x7f1103cc
co.auter.hcorp:style/Widget.Material3.FloatingActionButton.Small.Primary = 0x7f1103cb
co.auter.hcorp:style/Widget.Material3.FloatingActionButton.Primary = 0x7f1103c9
co.auter.hcorp:style/Widget.Material3.FloatingActionButton.Large.Surface = 0x7f1103c7
co.auter.hcorp:style/Widget.Material3.FloatingActionButton.Large.Secondary = 0x7f1103c6
co.auter.hcorp:style/Widget.Material3.FloatingActionButton.Large.Primary = 0x7f1103c5
co.auter.hcorp:style/Widget.Material3.ExtendedFloatingActionButton.Tertiary = 0x7f1103c4
co.auter.hcorp:styleable/MenuItem = 0x7f120061
co.auter.hcorp:style/Widget.Material3.ExtendedFloatingActionButton.Surface = 0x7f1103c3
co.auter.hcorp:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Surface = 0x7f1103bf
co.auter.hcorp:style/Widget.Material3.DrawerLayout = 0x7f1103bc
co.auter.hcorp:style/Widget.Material3.CompoundButton.Switch = 0x7f1103bb
co.auter.hcorp:style/Widget.Material3.CompoundButton.RadioButton = 0x7f1103ba
co.auter.hcorp:style/Widget.Material3.CompoundButton.MaterialSwitch = 0x7f1103b9
co.auter.hcorp:style/Widget.Material3.CollapsingToolbar.Medium = 0x7f1103b7
co.auter.hcorp:style/Widget.Material3.CollapsingToolbar.Large = 0x7f1103b6
co.auter.hcorp:style/Widget.Material3.CircularProgressIndicator.Legacy.Small = 0x7f1103b2
co.auter.hcorp:styleable/BaseProgressIndicator = 0x7f120015
co.auter.hcorp:style/Widget.Material3.CircularProgressIndicator.Legacy.Medium = 0x7f1103b1
co.auter.hcorp:style/Widget.Material3.CircularProgressIndicator.Legacy.ExtraSmall = 0x7f1103b0
co.auter.hcorp:style/Widget.Material3.MaterialCalendar.HeaderCancelButton = 0x7f1103dd
co.auter.hcorp:style/Widget.Material3.CircularProgressIndicator.Legacy = 0x7f1103af
co.auter.hcorp:style/Widget.Material3.CircularProgressIndicator = 0x7f1103ad
co.auter.hcorp:style/Widget.Material3.Chip.Suggestion.Elevated = 0x7f1103ab
co.auter.hcorp:style/Widget.Material3.Chip.Filter.Elevated = 0x7f1103a5
co.auter.hcorp:styleable/MaterialAutoCompleteTextView = 0x7f120050
co.auter.hcorp:style/Widget.Material3.CardView.Outlined = 0x7f1103a0
co.auter.hcorp:style/Widget.Material3.Button.TonalButton.Icon = 0x7f11039c
co.auter.hcorp:style/Widget.Material3.Button.TonalButton = 0x7f11039b
co.auter.hcorp:style/Widget.Material3.Button.TextButton.Icon = 0x7f110399
co.auter.hcorp:style/Widget.Material3.Button.TextButton.Dialog.Flush = 0x7f110397
co.auter.hcorp:style/Widget.Material3.Button.TextButton = 0x7f110395
co.auter.hcorp:style/Widget.Material3.Button.OutlinedButton.Icon = 0x7f110394
co.auter.hcorp:style/Widget.Material3.Button.OutlinedButton = 0x7f110393
co.auter.hcorp:style/Widget.Material3.Button.IconButton.Outlined = 0x7f110392
co.auter.hcorp:style/Widget.Material3.Button.IconButton = 0x7f11038f
co.auter.hcorp:style/Widget.Material3.Button.Icon = 0x7f11038e
co.auter.hcorp:styleable/ActionBar = 0x7f120000
co.auter.hcorp:style/Widget.Material3.Button.ElevatedButton.Icon = 0x7f11038d
co.auter.hcorp:style/Widget.Material3.Button = 0x7f11038b
co.auter.hcorp:style/Widget.Material3.BottomSheet.DragHandle = 0x7f110389
co.auter.hcorp:style/Widget.Material3.BottomSheet = 0x7f110388
co.auter.hcorp:style/Widget.Material3.BottomNavigationView = 0x7f110386
co.auter.hcorp:style/Widget.Material3.BottomAppBar.Button.Navigation = 0x7f110383
co.auter.hcorp:style/Widget.Material3.Badge.AdjustToBounds = 0x7f110381
co.auter.hcorp:style/Widget.Material3.AutoCompleteTextView.OutlinedBox.Dense = 0x7f11037f
co.auter.hcorp:style/Widget.Material3.ActionBar.Solid = 0x7f110379
co.auter.hcorp:style/Widget.Design.TextInputLayout = 0x7f110378
co.auter.hcorp:style/Widget.Design.TabLayout = 0x7f110376
co.auter.hcorp:style/Widget.Design.Snackbar = 0x7f110375
co.auter.hcorp:style/Widget.Design.ScrimInsetsFrameLayout = 0x7f110374
co.auter.hcorp:style/Widget.Design.FloatingActionButton = 0x7f110372
co.auter.hcorp:style/Widget.Design.AppBarLayout = 0x7f11036e
co.auter.hcorp:style/Widget.Compat.NotificationActionText = 0x7f11036d
co.auter.hcorp:style/Widget.Autofill.InlineSuggestionSubtitle = 0x7f11036a
co.auter.hcorp:style/Widget.Material3.TextInputEditText.OutlinedBox.Dense = 0x7f110417
co.auter.hcorp:style/Widget.AppCompat.TextView.SpinnerItem = 0x7f110363
co.auter.hcorp:style/Widget.AppCompat.TextView = 0x7f110362
co.auter.hcorp:style/Widget.AppCompat.Spinner.Underlined = 0x7f110361
co.auter.hcorp:style/Widget.AppCompat.Spinner.DropDown = 0x7f11035f
co.auter.hcorp:style/Widget.AppCompat.SeekBar = 0x7f11035c
co.auter.hcorp:style/Widget.AppCompat.SearchView.ActionBar = 0x7f11035b
co.auter.hcorp:style/Widget.AppCompat.SearchView = 0x7f11035a
co.auter.hcorp:style/Widget.AppCompat.RatingBar.Indicator = 0x7f110358
co.auter.hcorp:style/Widget.AppCompat.ProgressBar.Horizontal = 0x7f110356
co.auter.hcorp:style/Widget.AppCompat.ProgressBar = 0x7f110355
co.auter.hcorp:style/Widget.AppCompat.PopupWindow = 0x7f110354
co.auter.hcorp:style/Widget.AppCompat.ListView.DropDown = 0x7f110350
co.auter.hcorp:style/Widget.AppCompat.ListView = 0x7f11034f
co.auter.hcorp:style/Widget.AppCompat.ListPopupWindow = 0x7f11034e
co.auter.hcorp:style/Widget.AppCompat.Light.Spinner.DropDown.ActionBar = 0x7f11034c
co.auter.hcorp:style/Widget.AppCompat.Light.PopupMenu = 0x7f110349
co.auter.hcorp:style/Widget.AppCompat.Light.DropDownItem.Spinner = 0x7f110346
co.auter.hcorp:style/Widget.AppCompat.Light.ActionMode.Inverse = 0x7f110343
co.auter.hcorp:style/Widget.AppCompat.Light.ActionButton = 0x7f110340
co.auter.hcorp:style/Widget.AppCompat.Light.ActionBar.TabView.Inverse = 0x7f11033f
co.auter.hcorp:style/Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f11033d
co.auter.hcorp:style/Widget.AppCompat.Light.ActionBar.TabBar.Inverse = 0x7f11033b
co.auter.hcorp:style/Widget.AppCompat.Light.ActionBar.TabBar = 0x7f11033a
co.auter.hcorp:style/Widget.AppCompat.Light.ActionBar.Solid.Inverse = 0x7f110339
co.auter.hcorp:style/Widget.AppCompat.DropDownItem.Spinner = 0x7f110334
co.auter.hcorp:style/Widget.AppCompat.CompoundButton.RadioButton = 0x7f110331
co.auter.hcorp:style/Widget.AppCompat.ButtonBar = 0x7f11032e
co.auter.hcorp:style/Widget.AppCompat.Button.Colored = 0x7f11032c
co.auter.hcorp:style/Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f11032b
co.auter.hcorp:style/Widget.AppCompat.Button.Borderless.Colored = 0x7f11032a
co.auter.hcorp:style/Widget.AppCompat.Button = 0x7f110328
co.auter.hcorp:styleable/ShapeAppearance = 0x7f12007b
co.auter.hcorp:style/Widget.AppCompat.ActivityChooserView = 0x7f110326
co.auter.hcorp:style/Widget.AppCompat.ActionMode = 0x7f110325
co.auter.hcorp:style/Widget.AppCompat.ActionButton.Overflow = 0x7f110324
co.auter.hcorp:style/Widget.AppCompat.ActionButton = 0x7f110322
co.auter.hcorp:style/Widget.AppCompat.ActionBar = 0x7f11031d
co.auter.hcorp:style/Widget.MaterialComponents.CircularProgressIndicator.Medium = 0x7f11044e
co.auter.hcorp:style/ThemeOverlay.MaterialComponents.Toolbar.Surface = 0x7f11031c
co.auter.hcorp:style/ThemeOverlay.MaterialComponents.Toolbar.Primary = 0x7f11031b
co.auter.hcorp:style/ThemeOverlay.MaterialComponents.Toolbar.Popup.Primary = 0x7f11031a
co.auter.hcorp:style/ThemeOverlay.MaterialComponents.TextInputEditText = 0x7f110312
co.auter.hcorp:style/ThemeOverlay.MaterialComponents.MaterialCalendar.Fullscreen = 0x7f110311
co.auter.hcorp:style/ThemeOverlay.MaterialComponents.MaterialCalendar = 0x7f110310
co.auter.hcorp:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Spinner = 0x7f11030f
co.auter.hcorp:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text.Day = 0x7f11030e
co.auter.hcorp:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Header.Text = 0x7f11030d
co.auter.hcorp:style/ThemeOverlay.MaterialComponents.Dark.ActionBar = 0x7f110302
co.auter.hcorp:style/ThemeOverlay.MaterialComponents.Dark = 0x7f110301
co.auter.hcorp:style/ThemeOverlay.MaterialComponents.BottomAppBar.Primary = 0x7f1102fe
co.auter.hcorp:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox = 0x7f1102fa
co.auter.hcorp:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView = 0x7f1102f9
co.auter.hcorp:style/ThemeOverlay.MaterialComponents.ActionBar.Surface = 0x7f1102f8
co.auter.hcorp:style/ThemeOverlay.MaterialComponents.ActionBar.Primary = 0x7f1102f7
co.auter.hcorp:style/ThemeOverlay.MaterialAlertDialog.Material3.Title.Icon = 0x7f1102f4
co.auter.hcorp:style/ThemeOverlay.Material3.Toolbar.Surface = 0x7f1102f3
co.auter.hcorp:style/ThemeOverlay.Material3.TextInputEditText.FilledBox.Dense = 0x7f1102f0
co.auter.hcorp:style/ThemeOverlay.Material3.TextInputEditText = 0x7f1102ee
co.auter.hcorp:style/ThemeOverlay.Material3.Search = 0x7f1102ea
co.auter.hcorp:style/ThemeOverlay.Material3.NavigationRailView = 0x7f1102e7
co.auter.hcorp:style/ThemeOverlay.Material3.MaterialTimePicker = 0x7f1102e5
co.auter.hcorp:style/ThemeOverlay.Material3.MaterialCalendar.Fullscreen = 0x7f1102e3
co.auter.hcorp:style/ThemeOverlay.Material3.MaterialAlertDialog.Centered = 0x7f1102e1
co.auter.hcorp:style/ThemeOverlay.Material3.Light.Dialog.Alert.Framework = 0x7f1102df
co.auter.hcorp:style/ThemeOverlay.Material3.Light = 0x7f1102de
co.auter.hcorp:style/ThemeOverlay.Material3.HarmonizedColors.Empty = 0x7f1102dd
co.auter.hcorp:styleable/Slider = 0x7f120080
co.auter.hcorp:style/ThemeOverlay.Material3.FloatingActionButton.Secondary = 0x7f1102d9
co.auter.hcorp:style/Widget.Design.TextInputEditText = 0x7f110377
co.auter.hcorp:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Primary = 0x7f1102d4
co.auter.hcorp:style/Widget.AppCompat.DrawerArrowToggle = 0x7f110333
co.auter.hcorp:style/ThemeOverlay.Material3.Dark.ActionBar = 0x7f1102cb
co.auter.hcorp:style/ThemeOverlay.Material3.Chip = 0x7f1102c8
co.auter.hcorp:style/ThemeOverlay.Material3.Button.TonalButton = 0x7f1102c7
co.auter.hcorp:style/Widget.Material3.Button.TextButton.Dialog = 0x7f110396
co.auter.hcorp:style/ThemeOverlay.Material3.Button.TextButton = 0x7f1102c5
co.auter.hcorp:style/ThemeOverlay.Material3.Button.IconButton.Filled.Tonal = 0x7f1102c4
co.auter.hcorp:style/ThemeOverlay.Material3.Button = 0x7f1102c0
co.auter.hcorp:style/ThemeOverlay.Material3.BottomSheetDialog = 0x7f1102bf
co.auter.hcorp:style/ThemeOverlay.Material3.BottomAppBar.Legacy = 0x7f1102bd
co.auter.hcorp:style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox.Dense = 0x7f1102bb
co.auter.hcorp:style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox.Dense = 0x7f1102b9
co.auter.hcorp:style/ThemeOverlay.Material3.AutoCompleteTextView.FilledBox = 0x7f1102b8
co.auter.hcorp:style/ThemeOverlay.Material3.AutoCompleteTextView = 0x7f1102b7
co.auter.hcorp:style/ThemeOverlay.Material3.ActionBar = 0x7f1102b6
co.auter.hcorp:style/ThemeOverlay.Material3 = 0x7f1102b5
co.auter.hcorp:style/ThemeOverlay.Design.TextInputEditText = 0x7f1102b4
co.auter.hcorp:style/ThemeOverlay.AppCompat.Dialog = 0x7f1102b1
co.auter.hcorp:style/ThemeOverlay.AppCompat.DayNight.ActionBar = 0x7f1102b0
co.auter.hcorp:style/Widget.Material3.Snackbar.TextView = 0x7f110410
co.auter.hcorp:style/ThemeOverlay.AppCompat.Dark = 0x7f1102ad
co.auter.hcorp:style/ThemeOverlay.AppCompat.ActionBar = 0x7f1102ac
co.auter.hcorp:style/Theme.SplashScreen.Common = 0x7f1102a9
co.auter.hcorp:style/ThemeOverlay.AppCompat.Light = 0x7f1102b3
co.auter.hcorp:style/Theme.ReactNative.AppCompat.Light = 0x7f1102a5
co.auter.hcorp:style/Theme.MaterialComponents.Light.NoActionBar.Bridge = 0x7f1102a1
co.auter.hcorp:style/Theme.MaterialComponents.Light.Dialog.Alert = 0x7f110298
co.auter.hcorp:style/Theme.MaterialComponents.Light.DarkActionBar = 0x7f110295
co.auter.hcorp:style/Theme.MaterialComponents.Light.Bridge = 0x7f110294
co.auter.hcorp:style/Theme.MaterialComponents.Light.BottomSheetDialog = 0x7f110293
co.auter.hcorp:style/Theme.MaterialComponents.Light = 0x7f110292
co.auter.hcorp:style/Theme.MaterialComponents.DialogWhenLarge = 0x7f110291
co.auter.hcorp:style/Theme.MaterialComponents.Dialog.MinWidth.Bridge = 0x7f110290
co.auter.hcorp:style/Theme.MaterialComponents.Dialog.MinWidth = 0x7f11028f
co.auter.hcorp:style/Theme.MaterialComponents.Dialog.FixedSize.Bridge = 0x7f11028e
co.auter.hcorp:style/Theme.MaterialComponents.Dialog.Alert = 0x7f11028a
co.auter.hcorp:style/Theme.MaterialComponents.Dialog = 0x7f110289
co.auter.hcorp:style/Theme.MaterialComponents.DayNight.NoActionBar.Bridge = 0x7f110288
co.auter.hcorp:style/Theme.MaterialComponents.DayNight.NoActionBar = 0x7f110287
co.auter.hcorp:style/Theme.MaterialComponents.DayNight.DialogWhenLarge = 0x7f110286
co.auter.hcorp:style/Theme.MaterialComponents.DayNight.Dialog.MinWidth.Bridge = 0x7f110285
co.auter.hcorp:style/Theme.MaterialComponents.DayNight.Dialog.MinWidth = 0x7f110284
co.auter.hcorp:style/Theme.MaterialComponents.DayNight.Dialog.Bridge = 0x7f110281
co.auter.hcorp:style/Widget.MaterialComponents.TextInputLayout.FilledBox = 0x7f11048a
co.auter.hcorp:style/Theme.MaterialComponents.DayNight.Dialog.Alert.Bridge = 0x7f110280
co.auter.hcorp:style/Theme.MaterialComponents.DayNight.Dialog.Alert = 0x7f11027f
co.auter.hcorp:style/Theme.MaterialComponents.DayNight = 0x7f110279
co.auter.hcorp:style/Theme.MaterialComponents = 0x7f110275
co.auter.hcorp:style/Theme.Material3.Light.SideSheetDialog = 0x7f110274
co.auter.hcorp:style/Theme.Material3.Light.NoActionBar = 0x7f110273
co.auter.hcorp:style/Theme.Material3.Light.BottomSheetDialog = 0x7f11026e
co.auter.hcorp:style/Theme.Material3.Light = 0x7f11026d
co.auter.hcorp:style/Theme.Material3.DynamicColors.Light = 0x7f11026b
co.auter.hcorp:style/Theme.Material3.DynamicColors.DayNight.NoActionBar = 0x7f11026a
co.auter.hcorp:style/Theme.Material3.DynamicColors.DayNight = 0x7f110269
co.auter.hcorp:style/Theme.Material3.DynamicColors.Dark.NoActionBar = 0x7f110268
co.auter.hcorp:styleable/KeyPosition = 0x7f120045
co.auter.hcorp:style/Theme.Material3.DynamicColors.Dark = 0x7f110267
co.auter.hcorp:style/Theme.Material3.DayNight.NoActionBar = 0x7f110265
co.auter.hcorp:style/Theme.Material3.DayNight.Dialog.Alert = 0x7f110262
co.auter.hcorp:style/Theme.Material3.DayNight.BottomSheetDialog = 0x7f110260
co.auter.hcorp:style/Theme.Material3.DayNight = 0x7f11025f
co.auter.hcorp:style/ThemeOverlay.Material3.FloatingActionButton.Surface = 0x7f1102da
co.auter.hcorp:style/Theme.Material3.Dark.SideSheetDialog = 0x7f11025e
co.auter.hcorp:style/Theme.Material3.Dark.DialogWhenLarge = 0x7f11025c
co.auter.hcorp:style/Widget.AppCompat.Spinner = 0x7f11035e
co.auter.hcorp:style/Theme.Material3.Dark.Dialog = 0x7f110259
co.auter.hcorp:style/Theme.Hidden = 0x7f110256
co.auter.hcorp:style/Theme.FullScreenDialogAnimatedFade = 0x7f110254
co.auter.hcorp:style/Theme.FullScreenDialog = 0x7f110253
co.auter.hcorp:style/Theme.EdgeToEdge.Material3.Light = 0x7f110251
co.auter.hcorp:style/Theme.EdgeToEdge.Material3.DayNight.Common = 0x7f11024c
co.auter.hcorp:style/Theme.EdgeToEdge.Material2.Light.Common = 0x7f11024a
co.auter.hcorp:style/Theme.EdgeToEdge.Material2.DayNight.Common = 0x7f110248
co.auter.hcorp:style/Theme.EdgeToEdge.Light.Common = 0x7f110246
co.auter.hcorp:style/Theme.EdgeToEdge = 0x7f110243
co.auter.hcorp:style/Theme.Design.Light = 0x7f11023f
co.auter.hcorp:styleable/CoordinatorLayout_Layout = 0x7f12002d
co.auter.hcorp:style/Widget.Material3.Chip.Assist = 0x7f1103a2
co.auter.hcorp:style/Theme.Design.BottomSheetDialog = 0x7f11023e
co.auter.hcorp:styleable/BottomNavigationView = 0x7f120018
co.auter.hcorp:style/Theme.Catalyst.LogBox = 0x7f11023b
co.auter.hcorp:style/Theme.Catalyst = 0x7f11023a
co.auter.hcorp:style/Theme.AutofillInlineSuggestion = 0x7f110239
co.auter.hcorp:style/Theme.AppCompat.NoActionBar = 0x7f110238
co.auter.hcorp:style/Theme.AppCompat.Light.NoActionBar = 0x7f110237
co.auter.hcorp:style/Theme.AppCompat.Light.DialogWhenLarge = 0x7f110236
co.auter.hcorp:style/Theme.AppCompat.Light.Dialog.MinWidth = 0x7f110235
co.auter.hcorp:style/Theme.AppCompat.Light.Dialog.Alert = 0x7f110234
co.auter.hcorp:style/Theme.AppCompat.Light.DarkActionBar = 0x7f110232
co.auter.hcorp:style/Theme.AppCompat.Dialog.MinWidth = 0x7f11022e
co.auter.hcorp:style/Theme.AppCompat.Dialog.Alert = 0x7f11022d
co.auter.hcorp:style/Theme.AppCompat.Dialog = 0x7f11022c
co.auter.hcorp:style/Theme.AppCompat.DayNight.NoActionBar = 0x7f11022b
co.auter.hcorp:style/Theme.AppCompat.DayNight.DialogWhenLarge = 0x7f11022a
co.auter.hcorp:style/ThemeOverlay.Material3.NavigationView = 0x7f1102e8
co.auter.hcorp:style/Theme.AppCompat.DayNight.Dialog.MinWidth = 0x7f110229
co.auter.hcorp:style/Theme.App.SplashScreen = 0x7f110222
co.auter.hcorp:style/TextAppearance.MaterialComponents.TimePicker.Title = 0x7f11021c
co.auter.hcorp:styleable/Capability = 0x7f12001b
co.auter.hcorp:style/TextAppearance.MaterialComponents.Overline = 0x7f110219
co.auter.hcorp:style/TextAppearance.MaterialComponents.Headline5 = 0x7f110217
co.auter.hcorp:style/TextAppearance.MaterialComponents.Headline2 = 0x7f110214
co.auter.hcorp:style/TextAppearance.MaterialComponents.Headline1 = 0x7f110213
co.auter.hcorp:style/TextAppearance.MaterialComponents.Chip = 0x7f110212
co.auter.hcorp:style/TextAppearance.MaterialComponents.Caption = 0x7f110211
co.auter.hcorp:style/TextAppearance.MaterialComponents.Body2 = 0x7f11020f
co.auter.hcorp:style/TextAppearance.MaterialComponents.Badge = 0x7f11020d
co.auter.hcorp:style/TextAppearance.Material3.TitleSmall = 0x7f11020c
co.auter.hcorp:style/TextAppearance.Material3.TitleMedium = 0x7f11020b
co.auter.hcorp:style/TextAppearance.Material3.TitleLarge = 0x7f11020a
co.auter.hcorp:style/TextAppearance.Material3.LabelSmall = 0x7f110205
co.auter.hcorp:style/TextAppearance.Material3.LabelMedium = 0x7f110204
co.auter.hcorp:style/Widget.Material3.MaterialCalendar.Day.Invalid = 0x7f1103d7
co.auter.hcorp:style/TextAppearance.Material3.LabelLarge = 0x7f110203
co.auter.hcorp:style/TextAppearance.Material3.HeadlineLarge = 0x7f110200
co.auter.hcorp:style/Widget.Material3.NavigationRailView.Badge = 0x7f1103f9
co.auter.hcorp:style/TextAppearance.Material3.DisplaySmall = 0x7f1101ff
co.auter.hcorp:style/TextAppearance.Material3.DisplayMedium = 0x7f1101fe
co.auter.hcorp:style/TextAppearance.Material3.DisplayLarge = 0x7f1101fd
co.auter.hcorp:style/TextAppearance.Material3.BodyLarge = 0x7f1101fa
co.auter.hcorp:style/TextAppearance.Material3.ActionBar.Subtitle = 0x7f1101f8
co.auter.hcorp:style/TextAppearance.M3.Sys.Typescale.TitleSmall = 0x7f1101f7
co.auter.hcorp:styleable/MaterialShape = 0x7f12005a
co.auter.hcorp:style/TextAppearance.M3.Sys.Typescale.LabelSmall = 0x7f1101f4
co.auter.hcorp:style/TextAppearance.M3.Sys.Typescale.LabelMedium = 0x7f1101f3
co.auter.hcorp:style/TextAppearance.M3.Sys.Typescale.LabelLarge = 0x7f1101f2
co.auter.hcorp:style/TextAppearance.M3.Sys.Typescale.HeadlineMedium = 0x7f1101f0
co.auter.hcorp:style/TextAppearance.Design.Tab = 0x7f1101e8
co.auter.hcorp:style/TextAppearance.Design.Suffix = 0x7f1101e7
co.auter.hcorp:style/Widget.Material3.FloatingActionButton.Secondary = 0x7f1103ca
co.auter.hcorp:style/TextAppearance.Design.Prefix = 0x7f1101e5
co.auter.hcorp:style/TextAppearance.Design.Hint = 0x7f1101e3
co.auter.hcorp:style/TextAppearance.Design.HelperText = 0x7f1101e2
co.auter.hcorp:style/TextAppearance.Design.Counter = 0x7f1101df
co.auter.hcorp:style/TextAppearance.Compat.Notification.Title = 0x7f1101dc
co.auter.hcorp:style/TextAppearance.Compat.Notification.Line2.Media = 0x7f1101d8
co.auter.hcorp:style/TextAppearance.Compat.Notification.Line2 = 0x7f1101d7
co.auter.hcorp:style/TextAppearance.Compat.Notification.Info.Media = 0x7f1101d6
co.auter.hcorp:styleable/FontFamilyFont = 0x7f120037
co.auter.hcorp:style/TextAppearance.Compat.Notification.Info = 0x7f1101d5
co.auter.hcorp:style/TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f1101d0
co.auter.hcorp:style/TextAppearance.AppCompat.Widget.Button.Colored = 0x7f1101cc
co.auter.hcorp:style/TextAppearance.AppCompat.Widget.Button = 0x7f1101ca
co.auter.hcorp:style/TextAppearance.AppCompat.Widget.ActionMode.Title.Inverse = 0x7f1101c9
co.auter.hcorp:style/TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f1101c8
co.auter.hcorp:style/TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f1101c5
co.auter.hcorp:style/TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f1101c4
co.auter.hcorp:style/TextAppearance.AppCompat.Tooltip = 0x7f1101c0
co.auter.hcorp:style/TextAppearance.AppCompat.Title = 0x7f1101be
co.auter.hcorp:style/TextAppearance.AppCompat.Small = 0x7f1101ba
co.auter.hcorp:style/Widget.AppCompat.Toolbar.Button.Navigation = 0x7f110365
co.auter.hcorp:style/TextAppearance.AppCompat.SearchResult.Title = 0x7f1101b9
co.auter.hcorp:style/TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f1101b8
co.auter.hcorp:style/Theme.MaterialComponents.Light.Dialog.MinWidth.Bridge = 0x7f11029e
co.auter.hcorp:style/TextAppearance.AppCompat.Medium.Inverse = 0x7f1101b6
co.auter.hcorp:style/TextAppearance.AppCompat.Medium = 0x7f1101b5
co.auter.hcorp:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f1101b4
co.auter.hcorp:style/TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f1101b3
co.auter.hcorp:style/TextAppearance.AppCompat.Light.SearchResult.Title = 0x7f1101b2
co.auter.hcorp:style/TextAppearance.AppCompat.Large = 0x7f1101af
co.auter.hcorp:style/TextAppearance.AppCompat.Headline = 0x7f1101ad
co.auter.hcorp:style/TextAppearance.AppCompat.Display3 = 0x7f1101ab
co.auter.hcorp:style/TextAppearance.AppCompat.Display2 = 0x7f1101aa
co.auter.hcorp:style/TextAppearance.AppCompat.Caption = 0x7f1101a8
co.auter.hcorp:style/Widget.MaterialComponents.CompoundButton.RadioButton = 0x7f110452
co.auter.hcorp:style/TextAppearance.AppCompat.Button = 0x7f1101a7
co.auter.hcorp:style/TextAppearance.AppCompat.Body2 = 0x7f1101a6
co.auter.hcorp:style/Theme.MaterialComponents.Light.DialogWhenLarge = 0x7f11029f
co.auter.hcorp:style/TextAppearance.AppCompat.Body1 = 0x7f1101a5
co.auter.hcorp:style/TextAppearance.AppCompat = 0x7f1101a4
co.auter.hcorp:style/SpinnerDatePickerStyle = 0x7f1101a3
co.auter.hcorp:style/SpinnerDatePickerDialog = 0x7f1101a2
co.auter.hcorp:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Year = 0x7f1101a0
co.auter.hcorp:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Day = 0x7f11019e
co.auter.hcorp:style/ShapeAppearanceOverlay.MaterialComponents.ExtendedFloatingActionButton = 0x7f11019c
co.auter.hcorp:style/ShapeAppearanceOverlay.MaterialComponents.BottomSheet = 0x7f11019a
co.auter.hcorp:style/ShapeAppearanceOverlay.Material3.SearchView = 0x7f110198
co.auter.hcorp:style/ShapeAppearanceOverlay.Material3.NavigationView.Item = 0x7f110196
co.auter.hcorp:style/ShapeAppearanceOverlay.Material3.FloatingActionButton = 0x7f110195
co.auter.hcorp:style/ShapeAppearanceOverlay.Material3.Corner.Right = 0x7f110193
co.auter.hcorp:style/ShapeAppearanceOverlay.Material3.Corner.Left = 0x7f110192
co.auter.hcorp:style/ShapeAppearanceOverlay.Material3.Corner.Bottom = 0x7f110191
co.auter.hcorp:style/ShapeAppearanceOverlay.Material3.Chip = 0x7f110190
co.auter.hcorp:style/ShapeAppearanceOverlay.Material3.Button = 0x7f11018f
co.auter.hcorp:style/ShapeAppearance.MaterialComponents.SmallComponent = 0x7f11018d
co.auter.hcorp:style/ShapeAppearance.MaterialComponents.LargeComponent = 0x7f11018b
co.auter.hcorp:style/ShapeAppearance.Material3.SmallComponent = 0x7f110187
co.auter.hcorp:style/ShapeAppearance.Material3.MediumComponent = 0x7f110185
co.auter.hcorp:style/ShapeAppearance.Material3.Corner.Small = 0x7f110183
co.auter.hcorp:style/ShapeAppearance.Material3.Corner.Full = 0x7f11017f
co.auter.hcorp:style/ShapeAppearance.Material3.Corner.ExtraSmall = 0x7f11017e
co.auter.hcorp:style/ShapeAppearance.Material3.Corner.ExtraLarge = 0x7f11017d
co.auter.hcorp:style/ShapeAppearance.M3.Sys.Shape.Corner.Small = 0x7f11017c
co.auter.hcorp:style/ShapeAppearance.M3.Sys.Shape.Corner.Medium = 0x7f11017a
co.auter.hcorp:style/ShapeAppearance.M3.Sys.Shape.Corner.Large = 0x7f110179
co.auter.hcorp:style/Theme.MaterialComponents.DayNight.Bridge = 0x7f11027b
co.auter.hcorp:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraSmall = 0x7f110177
co.auter.hcorp:style/ShapeAppearance.M3.Comp.Switch.Handle.Shape = 0x7f110172
co.auter.hcorp:style/ShapeAppearance.M3.Comp.SearchBar.Avatar.Shape = 0x7f11016e
co.auter.hcorp:style/ShapeAppearance.M3.Comp.NavigationRail.ActiveIndicator.Shape = 0x7f11016c
co.auter.hcorp:style/ShapeAppearance.M3.Comp.NavigationBar.Container.Shape = 0x7f11016a
co.auter.hcorp:style/ShapeAppearance.M3.Comp.FilledButton.Container.Shape = 0x7f110168
co.auter.hcorp:style/Widget.Material3.TabLayout = 0x7f110411
co.auter.hcorp:style/ShapeAppearance.M3.Comp.DatePicker.Modal.Date.Container.Shape = 0x7f110167
co.auter.hcorp:style/ShapeAppearance.M3.Comp.Badge.Large.Shape = 0x7f110164
co.auter.hcorp:style/RtlUnderlay.Widget.AppCompat.ActionButton.Overflow = 0x7f110163
co.auter.hcorp:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Query = 0x7f11015f
co.auter.hcorp:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon2 = 0x7f11015e
co.auter.hcorp:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Icon1 = 0x7f11015d
co.auter.hcorp:style/RtlOverlay.Widget.AppCompat.Search.DropDown = 0x7f11015c
co.auter.hcorp:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Title = 0x7f11015b
co.auter.hcorp:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.SubmenuArrow = 0x7f110159
co.auter.hcorp:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.InternalGroup = 0x7f110157
co.auter.hcorp:style/RtlOverlay.Widget.AppCompat.ActionBar.TitleItem = 0x7f110154
co.auter.hcorp:xml/image_share_filepaths = 0x7f130002
co.auter.hcorp:style/Widget.Material3.Chip.Assist.Elevated = 0x7f1103a3
co.auter.hcorp:style/Platform.V21.AppCompat.Light = 0x7f11014f
co.auter.hcorp:style/Platform.V21.AppCompat = 0x7f11014e
co.auter.hcorp:style/Platform.MaterialComponents.Light = 0x7f110149
co.auter.hcorp:style/ShapeAppearance.MaterialComponents.MediumComponent = 0x7f11018c
co.auter.hcorp:style/Platform.MaterialComponents = 0x7f110147
co.auter.hcorp:style/Platform.AppCompat.Light = 0x7f110146
co.auter.hcorp:style/NoAnimationDialog = 0x7f110144
co.auter.hcorp:style/Widget.Compat.NotificationActionContainer = 0x7f11036c
co.auter.hcorp:style/MaterialAlertDialog.MaterialComponents.Title.Text.CenterStacked = 0x7f110143
co.auter.hcorp:style/MaterialAlertDialog.MaterialComponents.Title.Text = 0x7f110142
co.auter.hcorp:style/MaterialAlertDialog.MaterialComponents.Title.Icon.CenterStacked = 0x7f11013f
co.auter.hcorp:style/MaterialAlertDialog.MaterialComponents.Title.Icon = 0x7f11013e
co.auter.hcorp:style/MaterialAlertDialog.MaterialComponents.Picker.Date.Spinner = 0x7f11013d
co.auter.hcorp:style/MaterialAlertDialog.MaterialComponents.Picker.Date.Calendar = 0x7f11013c
co.auter.hcorp:style/MaterialAlertDialog.Material3.Title.Text.CenterStacked = 0x7f110139
co.auter.hcorp:style/MaterialAlertDialog.Material3.Body.Text.CenterStacked = 0x7f110133
co.auter.hcorp:style/MaterialAlertDialog.Material3.Animation = 0x7f110131
co.auter.hcorp:style/CardView.Light = 0x7f11012d
co.auter.hcorp:style/CardView.Dark = 0x7f11012c
co.auter.hcorp:style/CalendarDatePickerDialog = 0x7f110129
co.auter.hcorp:style/Base.v27.Theme.SplashScreen.Light = 0x7f110128
co.auter.hcorp:style/Base.v21.Theme.SplashScreen = 0x7f110125
co.auter.hcorp:style/Base.Widget.MaterialComponents.TextInputEditText = 0x7f110122
co.auter.hcorp:style/Base.Widget.MaterialComponents.Snackbar = 0x7f110121
co.auter.hcorp:style/Base.Widget.MaterialComponents.PopupMenu.ListPopupWindow = 0x7f11011e
co.auter.hcorp:style/Base.Widget.MaterialComponents.Chip = 0x7f110119
co.auter.hcorp:style/Base.Widget.MaterialComponents.AutoCompleteTextView = 0x7f110117
co.auter.hcorp:style/Base.Widget.Material3.TabLayout.Secondary = 0x7f110116
co.auter.hcorp:style/Base.Widget.Material3.MaterialCalendar.NavigationButton = 0x7f110112
co.auter.hcorp:style/Widget.MaterialComponents.Button.TextButton.Dialog = 0x7f11043e
co.auter.hcorp:style/Base.Widget.Material3.FloatingActionButton = 0x7f11010e
co.auter.hcorp:style/Base.Widget.Material3.ExtendedFloatingActionButton.Icon = 0x7f11010d
co.auter.hcorp:style/Base.Widget.Material3.ExtendedFloatingActionButton = 0x7f11010c
co.auter.hcorp:style/Base.Widget.Material3.CompoundButton.CheckBox = 0x7f110109
co.auter.hcorp:style/Base.Widget.Material3.CollapsingToolbar = 0x7f110108
co.auter.hcorp:style/Base.Widget.Material3.Chip = 0x7f110107
co.auter.hcorp:style/Base.Widget.Material3.CardView = 0x7f110106
co.auter.hcorp:styleable/AppBarLayout_Layout = 0x7f12000c
co.auter.hcorp:style/Base.Widget.Material3.BottomNavigationView = 0x7f110105
co.auter.hcorp:style/Base.Widget.Design.TabLayout = 0x7f110102
co.auter.hcorp:style/Base.Widget.AppCompat.Toolbar = 0x7f110100
co.auter.hcorp:style/Base.Widget.AppCompat.TextView.SpinnerItem = 0x7f1100ff
co.auter.hcorp:style/ThemeOverlay.Material3.BottomAppBar = 0x7f1102bc
co.auter.hcorp:style/Base.Widget.AppCompat.TextView = 0x7f1100fe
co.auter.hcorp:style/Base.Widget.AppCompat.SeekBar = 0x7f1100fa
co.auter.hcorp:style/Base.Widget.AppCompat.RatingBar.Small = 0x7f1100f7
co.auter.hcorp:style/Base.Widget.AppCompat.RatingBar.Indicator = 0x7f1100f6
co.auter.hcorp:style/Base.Widget.AppCompat.RatingBar = 0x7f1100f5
co.auter.hcorp:style/Base.Widget.AppCompat.ProgressBar.Horizontal = 0x7f1100f4
co.auter.hcorp:style/Base.Widget.AppCompat.ProgressBar = 0x7f1100f3
co.auter.hcorp:style/Base.Widget.AppCompat.PopupWindow = 0x7f1100f2
co.auter.hcorp:style/Base.Widget.AppCompat.Light.PopupMenu = 0x7f1100e9
co.auter.hcorp:style/Widget.MaterialComponents.Toolbar.Surface = 0x7f1104a0
co.auter.hcorp:style/Base.Widget.AppCompat.Light.ActionBar.Solid = 0x7f1100e4
co.auter.hcorp:style/Base.Widget.AppCompat.Light.ActionBar = 0x7f1100e3
co.auter.hcorp:style/Base.Widget.AppCompat.EditText = 0x7f1100e1
co.auter.hcorp:style/Base.Widget.AppCompat.DropDownItem.Spinner = 0x7f1100e0
co.auter.hcorp:style/Base.Widget.AppCompat.DrawerArrowToggle.Common = 0x7f1100df
co.auter.hcorp:styleable/MaterialCardView = 0x7f120055
co.auter.hcorp:style/Base.Widget.AppCompat.DrawerArrowToggle = 0x7f1100de
co.auter.hcorp:style/Base.Widget.AppCompat.CompoundButton.CheckBox = 0x7f1100db
co.auter.hcorp:style/Base.Widget.AppCompat.ButtonBar.AlertDialog = 0x7f1100da
co.auter.hcorp:style/Widget.Design.BottomSheet.Modal = 0x7f110370
co.auter.hcorp:style/Base.Widget.AppCompat.Button.ButtonBar.AlertDialog = 0x7f1100d6
co.auter.hcorp:style/Base.Widget.AppCompat.Button.Borderless.Colored = 0x7f1100d5
co.auter.hcorp:style/Base.Widget.AppCompat.Button.Borderless = 0x7f1100d4
co.auter.hcorp:style/Base.Widget.AppCompat.ActivityChooserView = 0x7f1100d1
co.auter.hcorp:style/Base.Widget.AppCompat.ActionBar.TabView = 0x7f1100cc
co.auter.hcorp:style/Base.Widget.AppCompat.ActionBar.TabText = 0x7f1100cb
co.auter.hcorp:style/Base.Widget.AppCompat.ActionBar.TabBar = 0x7f1100ca
co.auter.hcorp:style/Base.Widget.AppCompat.ActionBar.Solid = 0x7f1100c9
co.auter.hcorp:style/Base.V7.Widget.AppCompat.EditText = 0x7f1100c6
co.auter.hcorp:style/Base.V7.Widget.AppCompat.AutoCompleteTextView = 0x7f1100c5
co.auter.hcorp:style/Base.V7.Theme.AppCompat.Light.Dialog = 0x7f1100c3
co.auter.hcorp:style/Base.V7.Theme.AppCompat.Dialog = 0x7f1100c1
co.auter.hcorp:style/Base.V28.Theme.AppCompat.Light = 0x7f1100bf
co.auter.hcorp:style/Base.V28.Theme.AppCompat = 0x7f1100be
co.auter.hcorp:style/Base.V26.Widget.AppCompat.Toolbar = 0x7f1100bd
co.auter.hcorp:style/Widget.AppCompat.ActionBar.TabText = 0x7f110320
co.auter.hcorp:style/Base.V26.Theme.AppCompat.Light = 0x7f1100bc
co.auter.hcorp:style/Base.V24.Theme.Material3.Light.Dialog = 0x7f1100ba
co.auter.hcorp:style/Base.V24.Theme.Material3.Light = 0x7f1100b9
co.auter.hcorp:style/Base.V24.Theme.Material3.Dark.Dialog = 0x7f1100b8
co.auter.hcorp:style/Theme.Material3.DayNight.DialogWhenLarge = 0x7f110264
co.auter.hcorp:style/Base.V22.Theme.AppCompat.Light = 0x7f1100b4
co.auter.hcorp:style/Base.V21.ThemeOverlay.Material3.SideSheetDialog = 0x7f1100b1
co.auter.hcorp:style/Base.V21.ThemeOverlay.AppCompat.Dialog = 0x7f1100af
co.auter.hcorp:style/Base.V21.Theme.AppCompat.Light.Dialog = 0x7f1100aa
co.auter.hcorp:style/Base.V21.Theme.AppCompat = 0x7f1100a7
co.auter.hcorp:style/Base.V14.ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f1100a5
co.auter.hcorp:style/Base.V14.ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f1100a4
co.auter.hcorp:style/Base.V14.ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f1100a2
co.auter.hcorp:style/Base.V14.Theme.MaterialComponents.Light.Dialog = 0x7f11009e
co.auter.hcorp:style/Base.V14.Theme.MaterialComponents.Light.Bridge = 0x7f11009c
co.auter.hcorp:style/Base.V14.Theme.MaterialComponents.Dialog = 0x7f110099
co.auter.hcorp:style/Base.V14.Theme.Material3.Light.BottomSheetDialog = 0x7f110094
co.auter.hcorp:style/Base.V14.Theme.Material3.Dark.Dialog = 0x7f110091
co.auter.hcorp:style/TextAppearance.AppCompat.Widget.Switch = 0x7f1101d2
co.auter.hcorp:style/Base.V14.Theme.Material3.Dark = 0x7f11008f
co.auter.hcorp:style/Base.ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework = 0x7f11008d
co.auter.hcorp:style/ThemeOverlay.Material3.Dialog.Alert = 0x7f1102cf
co.auter.hcorp:style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert.Framework = 0x7f11008c
co.auter.hcorp:style/Base.ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f11008b
co.auter.hcorp:style/ThemeOverlay.Material3.DynamicColors.DayNight = 0x7f1102d2
co.auter.hcorp:style/Base.ThemeOverlay.MaterialComponents.Dialog = 0x7f11008a
co.auter.hcorp:style/Base.ThemeOverlay.Material3.TextInputEditText = 0x7f110089
co.auter.hcorp:style/Base.ThemeOverlay.Material3.Dialog = 0x7f110087
co.auter.hcorp:style/Base.ThemeOverlay.Material3.BottomSheetDialog = 0x7f110086
co.auter.hcorp:style/Base.ThemeOverlay.Material3.AutoCompleteTextView = 0x7f110085
co.auter.hcorp:style/Widget.Material3.MaterialCalendar.HeaderSelection = 0x7f1103e1
co.auter.hcorp:style/Base.ThemeOverlay.AppCompat.Dark = 0x7f110080
co.auter.hcorp:style/Base.ThemeOverlay.AppCompat.ActionBar = 0x7f11007f
co.auter.hcorp:style/Base.Theme.SplashScreen = 0x7f11007b
co.auter.hcorp:style/Widget.MaterialComponents.MaterialButtonToggleGroup = 0x7f110459
co.auter.hcorp:style/Base.Theme.MaterialComponents.Light.DialogWhenLarge = 0x7f11007a
co.auter.hcorp:style/Widget.MaterialComponents.BottomNavigationView.Colored = 0x7f110435
co.auter.hcorp:style/Base.Theme.MaterialComponents.Light.Dialog.FixedSize = 0x7f110078
co.auter.hcorp:style/Base.Theme.MaterialComponents.Light.Dialog = 0x7f110075
co.auter.hcorp:style/Base.Theme.MaterialComponents.Light.Bridge = 0x7f110072
co.auter.hcorp:style/Base.Theme.MaterialComponents.Light = 0x7f110071
co.auter.hcorp:style/Base.Theme.MaterialComponents.DialogWhenLarge = 0x7f110070
co.auter.hcorp:style/Base.Theme.MaterialComponents.CompactMenu = 0x7f11006a
co.auter.hcorp:style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox.Dense = 0x7f11042d
co.auter.hcorp:style/Base.Theme.Material3.Light.Dialog.FixedSize = 0x7f110065
co.auter.hcorp:style/Base.Theme.Material3.Light = 0x7f110062
co.auter.hcorp:style/Base.Theme.Material3.Dark.SideSheetDialog = 0x7f110061
co.auter.hcorp:style/Base.Theme.Material3.Dark.Dialog = 0x7f11005e
co.auter.hcorp:style/Widget.Material3.Light.ActionBar.Solid = 0x7f1103d1
co.auter.hcorp:style/Base.Theme.AppCompat.Light.Dialog.MinWidth = 0x7f11005a
co.auter.hcorp:style/Base.V14.Theme.Material3.Light = 0x7f110093
co.auter.hcorp:style/Base.Theme.AppCompat.Light.Dialog.FixedSize = 0x7f110059
co.auter.hcorp:style/Theme.EdgeToEdge.Material2 = 0x7f110247
co.auter.hcorp:style/Base.Theme.AppCompat.Light.Dialog = 0x7f110057
co.auter.hcorp:style/Widget.MaterialComponents.MaterialCalendar.HeaderDivider = 0x7f110464
co.auter.hcorp:style/Base.Theme.AppCompat.Dialog.FixedSize = 0x7f110052
co.auter.hcorp:style/Widget.MaterialComponents.NavigationRailView = 0x7f110473
co.auter.hcorp:style/Base.Theme.AppCompat.Dialog.Alert = 0x7f110051
co.auter.hcorp:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f11004c
co.auter.hcorp:style/Base.TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f11004b
co.auter.hcorp:style/Base.TextAppearance.MaterialComponents.Badge = 0x7f110047
co.auter.hcorp:style/Base.TextAppearance.Material3.Search = 0x7f110046
co.auter.hcorp:style/Base.TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f110045
co.auter.hcorp:style/Widget.MaterialComponents.Button.UnelevatedButton = 0x7f110443
co.auter.hcorp:style/Base.TextAppearance.AppCompat.Widget.Switch = 0x7f110044
co.auter.hcorp:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Large = 0x7f110042
co.auter.hcorp:styleable/ListPopupWindow = 0x7f12004c
co.auter.hcorp:style/Base.TextAppearance.AppCompat.Widget.DropDownItem = 0x7f110040
co.auter.hcorp:style/Base.V14.Theme.Material3.Light.Dialog = 0x7f110095
co.auter.hcorp:style/Base.TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f11003f
co.auter.hcorp:style/Base.TextAppearance.AppCompat.Widget.Button.Colored = 0x7f11003e
co.auter.hcorp:style/Base.TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f11003d
co.auter.hcorp:style/ThemeOverlay.Material3.Button.ElevatedButton = 0x7f1102c1
co.auter.hcorp:style/Base.TextAppearance.AppCompat.Widget.Button = 0x7f11003c
co.auter.hcorp:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f11003a
co.auter.hcorp:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title.Inverse = 0x7f110039
co.auter.hcorp:style/TextAppearance.AppCompat.Subhead = 0x7f1101bc
co.auter.hcorp:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f110036
co.auter.hcorp:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f110035
co.auter.hcorp:style/Base.TextAppearance.AppCompat.Tooltip = 0x7f110034
co.auter.hcorp:style/Base.TextAppearance.AppCompat.Title.Inverse = 0x7f110033
co.auter.hcorp:style/Base.TextAppearance.AppCompat.Subhead.Inverse = 0x7f110031
co.auter.hcorp:style/Widget.MaterialComponents.MaterialCalendar.Year.Today = 0x7f110470
co.auter.hcorp:style/Widget.AppCompat.ListView.Menu = 0x7f110351
co.auter.hcorp:style/Base.TextAppearance.AppCompat.Small.Inverse = 0x7f11002f
co.auter.hcorp:style/Base.TextAppearance.AppCompat.Small = 0x7f11002e
co.auter.hcorp:style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox = 0x7f1102f1
co.auter.hcorp:style/Base.Widget.AppCompat.ActionMode = 0x7f1100d0
co.auter.hcorp:style/Base.Widget.AppCompat.ActionBar = 0x7f1100c8
co.auter.hcorp:style/Base.TextAppearance.AppCompat.Medium.Inverse = 0x7f110029
co.auter.hcorp:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Small = 0x7f110027
co.auter.hcorp:style/Base.TextAppearance.AppCompat.Inverse = 0x7f110023
co.auter.hcorp:style/Base.TextAppearance.AppCompat.Display3 = 0x7f110020
co.auter.hcorp:style/ThemeOverlay.Material3.Button.IconButton.Filled = 0x7f1102c3
co.auter.hcorp:style/Base.TextAppearance.AppCompat.Button = 0x7f11001c
co.auter.hcorp:style/Base.TextAppearance.AppCompat.Body2 = 0x7f11001b
co.auter.hcorp:styleable/Spinner = 0x7f120083
co.auter.hcorp:style/Base.TextAppearance.AppCompat.Body1 = 0x7f11001a
co.auter.hcorp:style/Base.TextAppearance.AppCompat = 0x7f110019
co.auter.hcorp:style/Base.MaterialAlertDialog.MaterialComponents.Title.Panel = 0x7f110017
co.auter.hcorp:style/Base.CardView = 0x7f110013
co.auter.hcorp:style/Base.Animation.AppCompat.Tooltip = 0x7f110012
co.auter.hcorp:style/Base.Animation.AppCompat.DropDownUp = 0x7f110011
co.auter.hcorp:style/Base.AlertDialog.AppCompat.Light = 0x7f11000f
co.auter.hcorp:style/Animation.Material3.SideSheetDialog.Left = 0x7f11000a
co.auter.hcorp:style/Theme.EdgeToEdge.Material3.Light.Common = 0x7f110252
co.auter.hcorp:style/Animation.Catalyst.RedBox = 0x7f110006
co.auter.hcorp:style/Animation.Catalyst.LogBox = 0x7f110005
co.auter.hcorp:style/Animation.AppCompat.Tooltip = 0x7f110004
co.auter.hcorp:style/Animation.AppCompat.DropDownUp = 0x7f110003
co.auter.hcorp:string/timer_description = 0x7f1000fd
co.auter.hcorp:string/tablist_description = 0x7f1000fc
co.auter.hcorp:string/status_bar_notification_info_overflow = 0x7f1000fa
co.auter.hcorp:string/state_off_description = 0x7f1000f7
co.auter.hcorp:string/state_mixed_description = 0x7f1000f6
co.auter.hcorp:string/state_expanded_description = 0x7f1000f5
co.auter.hcorp:string/side_sheet_behavior = 0x7f1000f1
co.auter.hcorp:string/side_sheet_accessibility_pane_title = 0x7f1000f0
co.auter.hcorp:style/Base.Widget.MaterialComponents.PopupMenu = 0x7f11011c
co.auter.hcorp:string/searchview_navigation_content_description = 0x7f1000ef
co.auter.hcorp:string/searchbar_scrolling_view_behavior = 0x7f1000ed
co.auter.hcorp:style/Widget.Material3.FloatingActionButton.Surface = 0x7f1103cf
co.auter.hcorp:string/search_menu_title = 0x7f1000ec
co.auter.hcorp:style/Widget.Material3.CardView.Filled = 0x7f11039f
co.auter.hcorp:string/scrollbar_description = 0x7f1000eb
co.auter.hcorp:string/radiogroup_description = 0x7f1000e9
co.auter.hcorp:string/path_password_strike_through = 0x7f1000e6
co.auter.hcorp:string/path_password_eye_mask_visible = 0x7f1000e5
co.auter.hcorp:string/path_password_eye_mask_strike_through = 0x7f1000e4
co.auter.hcorp:string/mtrl_timepicker_confirm = 0x7f1000e1
co.auter.hcorp:string/mtrl_switch_track_path = 0x7f1000df
co.auter.hcorp:string/mtrl_switch_thumb_path_unchecked = 0x7f1000dd
co.auter.hcorp:string/mtrl_switch_thumb_path_pressed = 0x7f1000dc
co.auter.hcorp:string/mtrl_switch_thumb_path_name = 0x7f1000db
co.auter.hcorp:string/mtrl_switch_thumb_path_checked = 0x7f1000d9
co.auter.hcorp:string/mtrl_switch_thumb_group_name = 0x7f1000d8
co.auter.hcorp:style/Widget.MaterialComponents.TextInputEditText.OutlinedBox.Dense = 0x7f110489
co.auter.hcorp:style/TextAppearance.AppCompat.Light.SearchResult.Subtitle = 0x7f1101b1
co.auter.hcorp:string/mtrl_picker_toggle_to_year_selection = 0x7f1000d7
co.auter.hcorp:string/mtrl_picker_toggle_to_text_input_mode = 0x7f1000d6
co.auter.hcorp:string/mtrl_picker_toggle_to_day_selection = 0x7f1000d5
co.auter.hcorp:string/mtrl_picker_toggle_to_calendar_input_mode = 0x7f1000d4
co.auter.hcorp:string/mtrl_picker_text_input_date_range_start_hint = 0x7f1000cf
co.auter.hcorp:string/mtrl_picker_text_input_date_range_end_hint = 0x7f1000ce
co.auter.hcorp:string/mtrl_picker_range_header_unselected = 0x7f1000ca
co.auter.hcorp:string/mtrl_picker_range_header_title = 0x7f1000c9
co.auter.hcorp:string/mtrl_picker_range_header_selected = 0x7f1000c8
co.auter.hcorp:style/Widget.MaterialComponents.Chip.Filter = 0x7f11044a
co.auter.hcorp:style/Widget.Material3.FloatingActionButton.Small.Tertiary = 0x7f1103ce
co.auter.hcorp:string/mtrl_picker_out_of_range = 0x7f1000c5
co.auter.hcorp:style/Widget.MaterialComponents.MaterialCalendar.DayOfWeekLabel = 0x7f11045f
co.auter.hcorp:string/mtrl_picker_navigate_to_year_description = 0x7f1000c4
co.auter.hcorp:string/mtrl_picker_navigate_to_current_year_description = 0x7f1000c3
co.auter.hcorp:string/mtrl_picker_invalid_format_use = 0x7f1000c1
co.auter.hcorp:string/mtrl_picker_day_of_week_column_header = 0x7f1000bd
co.auter.hcorp:string/mtrl_picker_date_header_title = 0x7f1000bb
co.auter.hcorp:string/mtrl_picker_date_header_selected = 0x7f1000ba
co.auter.hcorp:string/mtrl_picker_confirm = 0x7f1000b9
co.auter.hcorp:style/Widget.MaterialComponents.Button = 0x7f110439
co.auter.hcorp:string/mtrl_picker_cancel = 0x7f1000b8
co.auter.hcorp:string/mtrl_picker_announce_current_selection_none = 0x7f1000b7
co.auter.hcorp:style/Widget.Material3.Chip.Input.Icon = 0x7f1103a8
co.auter.hcorp:string/mtrl_picker_a11y_prev_month = 0x7f1000b4
co.auter.hcorp:style/Widget.Material3.CircularProgressIndicator.ExtraSmall = 0x7f1103ae
co.auter.hcorp:string/mtrl_picker_a11y_next_month = 0x7f1000b3
co.auter.hcorp:string/mtrl_checkbox_state_description_unchecked = 0x7f1000af
co.auter.hcorp:string/mtrl_checkbox_state_description_checked = 0x7f1000ad
co.auter.hcorp:string/mtrl_checkbox_button_path_unchecked = 0x7f1000ac
co.auter.hcorp:string/mtrl_checkbox_button_path_name = 0x7f1000ab
co.auter.hcorp:string/mtrl_checkbox_button_path_checked = 0x7f1000a9
co.auter.hcorp:string/mtrl_checkbox_button_icon_path_name = 0x7f1000a8
co.auter.hcorp:string/mtrl_checkbox_button_icon_path_group_name = 0x7f1000a6
co.auter.hcorp:style/Theme.AppCompat.CompactMenu = 0x7f110224
co.auter.hcorp:string/menuitem_description = 0x7f1000a3
co.auter.hcorp:string/menu_description = 0x7f1000a1
co.auter.hcorp:string/material_timepicker_select_time = 0x7f10009f
co.auter.hcorp:string/material_timepicker_minute = 0x7f10009d
co.auter.hcorp:string/material_timepicker_hour = 0x7f10009c
co.auter.hcorp:string/material_timepicker_am = 0x7f10009a
co.auter.hcorp:string/material_slider_value = 0x7f100099
co.auter.hcorp:string/material_slider_range_end = 0x7f100097
co.auter.hcorp:style/TextAppearance.Design.CollapsingToolbar.Expanded = 0x7f1101de
co.auter.hcorp:string/material_motion_easing_standard = 0x7f100096
co.auter.hcorp:string/material_motion_easing_linear = 0x7f100095
co.auter.hcorp:string/material_motion_easing_decelerated = 0x7f100093
co.auter.hcorp:string/material_hour_suffix = 0x7f10008f
co.auter.hcorp:string/material_clock_display_divider = 0x7f10008b
co.auter.hcorp:string/m3_sys_motion_easing_standard_accelerate = 0x7f100089
co.auter.hcorp:style/Widget.Design.NavigationView = 0x7f110373
co.auter.hcorp:string/m3_sys_motion_easing_legacy_decelerate = 0x7f100086
co.auter.hcorp:string/m3_sys_motion_easing_emphasized_decelerate = 0x7f100082
co.auter.hcorp:string/m3_sys_motion_easing_emphasized = 0x7f100080
co.auter.hcorp:string/m3_ref_typeface_plain_regular = 0x7f10007f
co.auter.hcorp:string/m3_ref_typeface_brand_regular = 0x7f10007d
co.auter.hcorp:string/m3_ref_typeface_brand_medium = 0x7f10007c
co.auter.hcorp:style/Theme.MaterialComponents.Light.Dialog.FixedSize.Bridge = 0x7f11029c
co.auter.hcorp:string/imagebutton_description = 0x7f100078
co.auter.hcorp:style/Base.Widget.MaterialComponents.PopupMenu.Overflow = 0x7f11011f
co.auter.hcorp:string/image_description = 0x7f100077
co.auter.hcorp:style/Base.Theme.AppCompat.Light.Dialog.Alert = 0x7f110058
co.auter.hcorp:string/icon_content_description = 0x7f100076
co.auter.hcorp:string/header_description = 0x7f100074
co.auter.hcorp:string/google_crash_reporting_api_key = 0x7f100072
co.auter.hcorp:style/Theme.AppCompat.DayNight.Dialog.Alert = 0x7f110228
co.auter.hcorp:string/google_api_key = 0x7f100070
co.auter.hcorp:style/ThemeOverlay.MaterialComponents.TimePicker.Display = 0x7f110318
co.auter.hcorp:string/gcm_defaultSenderId = 0x7f10006f
co.auter.hcorp:string/fcm_fallback_notification_channel_label = 0x7f10006e
co.auter.hcorp:string/fallback_menu_item_open_in_browser = 0x7f10006c
co.auter.hcorp:string/fab_transformation_scrim_behavior = 0x7f100069
co.auter.hcorp:string/exposed_dropdown_menu_content_description = 0x7f100068
co.auter.hcorp:string/expo_system_ui_user_interface_style = 0x7f100067
co.auter.hcorp:string/expo_splash_screen_resize_mode = 0x7f100065
co.auter.hcorp:string/error_icon_content_description = 0x7f100063
co.auter.hcorp:string/error_a11y_label = 0x7f100062
co.auter.hcorp:string/copy_toast_msg = 0x7f100061
co.auter.hcorp:string/common_signin_button_text_long = 0x7f100060
co.auter.hcorp:style/TextAppearance.AppCompat.Widget.TextView.SpinnerItem = 0x7f1101d3
co.auter.hcorp:string/common_google_play_services_updating_text = 0x7f10005c
co.auter.hcorp:string/common_google_play_services_update_title = 0x7f10005b
co.auter.hcorp:string/common_google_play_services_update_text = 0x7f10005a
co.auter.hcorp:string/common_google_play_services_update_button = 0x7f100059
co.auter.hcorp:string/common_google_play_services_unsupported_text = 0x7f100058
co.auter.hcorp:string/common_google_play_services_install_title = 0x7f100054
co.auter.hcorp:string/common_google_play_services_install_text = 0x7f100053
co.auter.hcorp:style/Widget.Material3.MaterialCalendar.DayTextView = 0x7f1103db
co.auter.hcorp:string/common_google_play_services_install_button = 0x7f100052
co.auter.hcorp:string/common_google_play_services_enable_button = 0x7f10004f
co.auter.hcorp:styleable/ColorStateListItem = 0x7f120026
co.auter.hcorp:string/com.google.firebase.crashlytics.version_control_info = 0x7f10004d
co.auter.hcorp:string/com.google.firebase.crashlytics.mapping_file_id = 0x7f10004c
co.auter.hcorp:string/character_counter_pattern = 0x7f10004a
co.auter.hcorp:style/Widget.Material3.BottomAppBar.Legacy = 0x7f110384
co.auter.hcorp:string/character_counter_content_description = 0x7f100048
co.auter.hcorp:styleable/KeyTimeCycle = 0x7f120046
co.auter.hcorp:string/catalyst_settings = 0x7f100046
co.auter.hcorp:style/MaterialAlertDialog.Material3.Title.Icon = 0x7f110134
co.auter.hcorp:string/catalyst_sample_profiler_toggle = 0x7f100045
co.auter.hcorp:string/catalyst_reload = 0x7f100041
co.auter.hcorp:string/catalyst_perf_monitor_stop = 0x7f100040
co.auter.hcorp:string/catalyst_perf_monitor = 0x7f10003f
co.auter.hcorp:style/Widget.Material3.MaterialCalendar.DayOfWeekLabel = 0x7f1103da
co.auter.hcorp:style/Platform.Widget.AppCompat.Spinner = 0x7f110152
co.auter.hcorp:string/catalyst_open_debugger_error = 0x7f10003e
co.auter.hcorp:string/catalyst_loading_from_url = 0x7f10003d
co.auter.hcorp:string/catalyst_hot_reloading_stop = 0x7f10003b
co.auter.hcorp:style/Widget.Material3.BottomNavigationView.ActiveIndicator = 0x7f110387
co.auter.hcorp:string/catalyst_hot_reloading_auto_enable = 0x7f10003a
co.auter.hcorp:styleable/NavigationBarActiveIndicator = 0x7f120069
co.auter.hcorp:string/catalyst_hot_reloading = 0x7f100038
co.auter.hcorp:string/catalyst_dev_menu_sub_header = 0x7f100035
co.auter.hcorp:style/TextAppearance.M3.Sys.Typescale.DisplayLarge = 0x7f1101ec
co.auter.hcorp:string/catalyst_debug_error = 0x7f100031
co.auter.hcorp:string/catalyst_copy_button = 0x7f10002f
co.auter.hcorp:string/catalyst_change_bundle_location = 0x7f10002e
co.auter.hcorp:string/call_notification_incoming_text = 0x7f10002b
co.auter.hcorp:string/call_notification_answer_video_action = 0x7f100028
co.auter.hcorp:string/call_notification_answer_action = 0x7f100027
co.auter.hcorp:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Text = 0x7f11015a
co.auter.hcorp:style/Base.ThemeOverlay.AppCompat = 0x7f11007e
co.auter.hcorp:string/bottomsheet_action_expand = 0x7f100023
co.auter.hcorp:string/bottomsheet_action_collapse = 0x7f100022
co.auter.hcorp:style/Base.Theme.SplashScreen.Light = 0x7f11007d
co.auter.hcorp:string/bottom_sheet_behavior = 0x7f100021
co.auter.hcorp:string/app_name = 0x7f10001f
co.auter.hcorp:string/android.credentials.TYPE_PASSWORD_CREDENTIAL = 0x7f10001c
co.auter.hcorp:string/alert_description = 0x7f10001b
co.auter.hcorp:style/ThemeOverlay.MaterialComponents.Light = 0x7f110307
co.auter.hcorp:string/abc_shareactionprovider_share_with_application = 0x7f100019
co.auter.hcorp:string/abc_shareactionprovider_share_with = 0x7f100018
co.auter.hcorp:string/abc_searchview_description_voice = 0x7f100017
co.auter.hcorp:style/ThemeOverlay.MaterialComponents.TimePicker.Display.TextInputEditText = 0x7f110319
co.auter.hcorp:string/abc_searchview_description_search = 0x7f100015
co.auter.hcorp:string/abc_menu_sym_shortcut_label = 0x7f100010
co.auter.hcorp:styleable/ConstraintLayout_placeholder = 0x7f12002a
co.auter.hcorp:string/abc_menu_space_shortcut_label = 0x7f10000f
co.auter.hcorp:string/abc_menu_shift_shortcut_label = 0x7f10000e
co.auter.hcorp:string/abc_menu_meta_shortcut_label = 0x7f10000d
co.auter.hcorp:string/abc_menu_function_shortcut_label = 0x7f10000c
co.auter.hcorp:string/abc_menu_alt_shortcut_label = 0x7f100008
co.auter.hcorp:style/Widget.Autofill.InlineSuggestionTitle = 0x7f11036b
co.auter.hcorp:string/abc_capital_off = 0x7f100006
co.auter.hcorp:string/abc_activity_chooser_view_see_all = 0x7f100004
co.auter.hcorp:string/abc_action_bar_home_description = 0x7f100000
co.auter.hcorp:raw/firebase_crashlytics_keep = 0x7f0f0001
co.auter.hcorp:mipmap/ic_launcher_round = 0x7f0d0002
co.auter.hcorp:macro/m3_sys_color_light_surface_tint = 0x7f0c0176
co.auter.hcorp:macro/m3_comp_top_app_bar_small_trailing_icon_color = 0x7f0c0174
co.auter.hcorp:macro/m3_comp_top_app_bar_small_on_scroll_container_color = 0x7f0c0173
co.auter.hcorp:macro/m3_comp_top_app_bar_small_leading_icon_color = 0x7f0c0172
co.auter.hcorp:macro/m3_comp_top_app_bar_small_container_color = 0x7f0c016f
co.auter.hcorp:style/Base.V21.Theme.MaterialComponents.Light.Dialog = 0x7f1100ae
co.auter.hcorp:macro/m3_comp_top_app_bar_medium_headline_type = 0x7f0c016e
co.auter.hcorp:macro/m3_comp_top_app_bar_large_headline_type = 0x7f0c016c
co.auter.hcorp:macro/m3_comp_top_app_bar_large_headline_color = 0x7f0c016b
co.auter.hcorp:macro/m3_comp_time_picker_time_selector_unselected_label_text_color = 0x7f0c016a
co.auter.hcorp:macro/m3_comp_time_picker_time_selector_unselected_container_color = 0x7f0c0167
co.auter.hcorp:macro/m3_comp_time_picker_time_selector_separator_type = 0x7f0c0166
co.auter.hcorp:macro/m3_comp_time_picker_time_selector_selected_pressed_state_layer_color = 0x7f0c0164
co.auter.hcorp:style/TextAppearance.M3.Sys.Typescale.BodyMedium = 0x7f1101ea
co.auter.hcorp:macro/m3_comp_time_picker_time_selector_selected_hover_state_layer_color = 0x7f0c0162
co.auter.hcorp:style/ShapeAppearanceOverlay.Material3.SearchBar = 0x7f110197
co.auter.hcorp:macro/m3_comp_time_picker_time_selector_selected_focus_state_layer_color = 0x7f0c0161
co.auter.hcorp:macro/m3_comp_time_picker_period_selector_unselected_pressed_state_layer_color = 0x7f0c015d
co.auter.hcorp:style/Widget.AppCompat.Light.ActionBar.TabText = 0x7f11033c
co.auter.hcorp:macro/m3_comp_time_picker_period_selector_unselected_label_text_color = 0x7f0c015c
co.auter.hcorp:styleable/LinearProgressIndicator = 0x7f12004b
co.auter.hcorp:macro/m3_comp_time_picker_period_selector_selected_label_text_color = 0x7f0c0158
co.auter.hcorp:macro/m3_comp_time_picker_period_selector_selected_hover_state_layer_color = 0x7f0c0157
co.auter.hcorp:style/Base.V14.Theme.MaterialComponents.Dialog.Bridge = 0x7f11009a
co.auter.hcorp:macro/m3_comp_time_picker_period_selector_selected_container_color = 0x7f0c0155
co.auter.hcorp:style/Base.V14.Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f11009d
co.auter.hcorp:macro/m3_comp_time_picker_period_selector_label_text_type = 0x7f0c0153
co.auter.hcorp:macro/m3_comp_time_picker_headline_color = 0x7f0c0150
co.auter.hcorp:macro/m3_comp_time_picker_container_shape = 0x7f0c014f
co.auter.hcorp:macro/m3_comp_time_picker_clock_dial_selector_handle_container_color = 0x7f0c014d
co.auter.hcorp:string/catalyst_heap_capture = 0x7f100037
co.auter.hcorp:macro/m3_comp_time_picker_clock_dial_color = 0x7f0c014c
co.auter.hcorp:string/mtrl_picker_range_header_only_end_selected = 0x7f1000c6
co.auter.hcorp:macro/m3_comp_time_input_time_input_field_supporting_text_type = 0x7f0c014b
co.auter.hcorp:macro/m3_comp_time_input_time_input_field_focus_outline_color = 0x7f0c0148
co.auter.hcorp:macro/m3_comp_text_button_pressed_state_layer_color = 0x7f0c0146
co.auter.hcorp:style/Base.MaterialAlertDialog.MaterialComponents.Title.Icon = 0x7f110016
co.auter.hcorp:macro/m3_comp_text_button_label_text_color = 0x7f0c0144
co.auter.hcorp:macro/m3_comp_text_button_hover_state_layer_color = 0x7f0c0143
co.auter.hcorp:macro/m3_comp_switch_unselected_track_outline_color = 0x7f0c0141
co.auter.hcorp:macro/m3_comp_switch_unselected_pressed_state_layer_color = 0x7f0c013d
co.auter.hcorp:macro/m3_comp_switch_unselected_pressed_handle_color = 0x7f0c013b
co.auter.hcorp:string/material_clock_toggle_content_description = 0x7f10008c
co.auter.hcorp:macro/m3_comp_switch_unselected_icon_color = 0x7f0c013a
co.auter.hcorp:macro/m3_comp_switch_unselected_hover_track_outline_color = 0x7f0c0139
co.auter.hcorp:macro/m3_comp_switch_unselected_hover_track_color = 0x7f0c0138
co.auter.hcorp:macro/m3_comp_switch_unselected_hover_icon_color = 0x7f0c0136
co.auter.hcorp:macro/m3_comp_switch_unselected_hover_handle_color = 0x7f0c0135
co.auter.hcorp:macro/m3_comp_switch_unselected_focus_track_outline_color = 0x7f0c0133
co.auter.hcorp:style/Theme.MaterialComponents.CompactMenu = 0x7f110278
co.auter.hcorp:macro/m3_comp_switch_unselected_focus_handle_color = 0x7f0c012f
co.auter.hcorp:macro/m3_comp_switch_selected_pressed_icon_color = 0x7f0c012b
co.auter.hcorp:macro/m3_comp_switch_selected_pressed_handle_color = 0x7f0c012a
co.auter.hcorp:macro/m3_comp_switch_selected_hover_track_color = 0x7f0c0128
co.auter.hcorp:macro/m3_comp_switch_selected_hover_icon_color = 0x7f0c0126
co.auter.hcorp:macro/m3_comp_switch_selected_hover_handle_color = 0x7f0c0125
co.auter.hcorp:macro/m3_comp_switch_selected_focus_track_color = 0x7f0c0123
co.auter.hcorp:macro/m3_comp_switch_selected_focus_icon_color = 0x7f0c0121
co.auter.hcorp:string/bottomsheet_drag_handle_content_description = 0x7f100026
co.auter.hcorp:macro/m3_comp_switch_selected_focus_handle_color = 0x7f0c0120
co.auter.hcorp:string/mtrl_checkbox_button_icon_path_indeterminate = 0x7f1000a7
co.auter.hcorp:macro/m3_comp_switch_disabled_unselected_icon_color = 0x7f0c011d
co.auter.hcorp:macro/m3_comp_switch_disabled_selected_track_color = 0x7f0c011b
co.auter.hcorp:macro/m3_comp_suggestion_chip_label_text_type = 0x7f0c0118
co.auter.hcorp:style/TextAppearance.Compat.Notification = 0x7f1101d4
co.auter.hcorp:macro/m3_comp_suggestion_chip_container_shape = 0x7f0c0117
co.auter.hcorp:macro/m3_comp_snackbar_supporting_text_type = 0x7f0c0116
co.auter.hcorp:macro/m3_comp_time_picker_time_selector_unselected_focus_state_layer_color = 0x7f0c0168
co.auter.hcorp:macro/m3_comp_snackbar_container_shape = 0x7f0c0114
co.auter.hcorp:macro/m3_comp_slider_label_container_color = 0x7f0c0111
co.auter.hcorp:macro/m3_comp_slider_handle_color = 0x7f0c010f
co.auter.hcorp:macro/m3_comp_slider_disabled_handle_color = 0x7f0c010d
co.auter.hcorp:macro/m3_comp_sheet_side_docked_modal_container_color = 0x7f0c0108
co.auter.hcorp:macro/m3_comp_secondary_navigation_tab_inactive_label_text_color = 0x7f0c00ff
co.auter.hcorp:macro/m3_comp_secondary_navigation_tab_focus_state_layer_color = 0x7f0c00fd
co.auter.hcorp:style/ThemeOverlay.MaterialComponents = 0x7f1102f5
co.auter.hcorp:macro/m3_comp_secondary_navigation_tab_active_indicator_color = 0x7f0c00fa
co.auter.hcorp:macro/m3_comp_search_view_header_trailing_icon_color = 0x7f0c00f9
co.auter.hcorp:macro/m3_comp_search_view_header_leading_icon_color = 0x7f0c00f6
co.auter.hcorp:styleable/MaterialButton = 0x7f120051
co.auter.hcorp:macro/m3_comp_search_view_header_input_text_type = 0x7f0c00f5
co.auter.hcorp:macro/m3_comp_search_view_header_input_text_color = 0x7f0c00f4
co.auter.hcorp:macro/m3_comp_search_view_docked_container_shape = 0x7f0c00f3
co.auter.hcorp:macro/m3_comp_search_view_divider_color = 0x7f0c00f2
co.auter.hcorp:macro/m3_comp_search_view_container_color = 0x7f0c00f1
co.auter.hcorp:macro/m3_comp_search_bar_supporting_text_type = 0x7f0c00ef
co.auter.hcorp:macro/m3_comp_search_bar_supporting_text_color = 0x7f0c00ee
co.auter.hcorp:macro/m3_comp_switch_unselected_track_color = 0x7f0c0140
co.auter.hcorp:macro/m3_comp_search_bar_pressed_state_layer_color = 0x7f0c00ec
co.auter.hcorp:macro/m3_comp_search_bar_leading_icon_color = 0x7f0c00eb
co.auter.hcorp:macro/m3_comp_search_bar_input_text_color = 0x7f0c00e9
co.auter.hcorp:macro/m3_comp_search_bar_hover_supporting_text_color = 0x7f0c00e8
co.auter.hcorp:string/mtrl_picker_today_description = 0x7f1000d3
co.auter.hcorp:macro/m3_comp_radio_button_unselected_pressed_icon_color = 0x7f0c00e4
co.auter.hcorp:macro/m3_comp_radio_button_unselected_hover_icon_color = 0x7f0c00e1
co.auter.hcorp:style/Widget.Material3.Chip.Input.Elevated = 0x7f1103a7
co.auter.hcorp:macro/m3_comp_radio_button_unselected_focus_state_layer_color = 0x7f0c00e0
co.auter.hcorp:macro/m3_comp_radio_button_unselected_focus_icon_color = 0x7f0c00df
co.auter.hcorp:macro/m3_comp_radio_button_selected_icon_color = 0x7f0c00dc
co.auter.hcorp:macro/m3_comp_radio_button_selected_focus_state_layer_color = 0x7f0c00d9
co.auter.hcorp:macro/m3_comp_progress_indicator_track_color = 0x7f0c00d5
co.auter.hcorp:macro/m3_comp_progress_indicator_active_indicator_color = 0x7f0c00d4
co.auter.hcorp:macro/m3_comp_primary_navigation_tab_with_label_text_label_text_type = 0x7f0c00d3
co.auter.hcorp:macro/m3_comp_primary_navigation_tab_with_label_text_inactive_label_text_color = 0x7f0c00d2
co.auter.hcorp:style/ThemeOverlay.Material3.MaterialCalendar.HeaderCancelButton = 0x7f1102e4
co.auter.hcorp:macro/m3_comp_primary_navigation_tab_with_label_text_active_label_text_color = 0x7f0c00d1
co.auter.hcorp:macro/m3_comp_primary_navigation_tab_inactive_pressed_state_layer_color = 0x7f0c00ce
co.auter.hcorp:macro/m3_comp_primary_navigation_tab_container_color = 0x7f0c00cb
co.auter.hcorp:macro/m3_comp_primary_navigation_tab_active_focus_state_layer_color = 0x7f0c00c7
co.auter.hcorp:macro/m3_comp_outlined_text_field_supporting_text_type = 0x7f0c00c5
co.auter.hcorp:macro/m3_comp_outlined_text_field_supporting_text_color = 0x7f0c00c4
co.auter.hcorp:macro/m3_comp_outlined_text_field_hover_supporting_text_color = 0x7f0c00bf
co.auter.hcorp:macro/m3_comp_search_view_header_supporting_text_color = 0x7f0c00f7
co.auter.hcorp:macro/m3_comp_outlined_text_field_hover_outline_color = 0x7f0c00be
co.auter.hcorp:style/Theme.Material3.Dark.BottomSheetDialog = 0x7f110258
co.auter.hcorp:macro/m3_comp_outlined_text_field_focus_supporting_text_color = 0x7f0c00bc
co.auter.hcorp:macro/m3_comp_outlined_text_field_focus_input_text_color = 0x7f0c00b9
co.auter.hcorp:macro/m3_comp_outlined_text_field_error_outline_color = 0x7f0c00b6
co.auter.hcorp:macro/m3_comp_outlined_text_field_disabled_supporting_text_color = 0x7f0c00b5
co.auter.hcorp:macro/m3_comp_outlined_text_field_disabled_outline_color = 0x7f0c00b4
co.auter.hcorp:macro/m3_comp_outlined_text_field_disabled_label_text_color = 0x7f0c00b3
co.auter.hcorp:macro/m3_comp_switch_selected_focus_state_layer_color = 0x7f0c0122
co.auter.hcorp:macro/m3_comp_outlined_text_field_disabled_input_text_color = 0x7f0c00b2
co.auter.hcorp:macro/m3_comp_outlined_text_field_container_shape = 0x7f0c00b1
co.auter.hcorp:macro/m3_comp_outlined_text_field_caret_color = 0x7f0c00b0
co.auter.hcorp:style/ShapeAppearance.MaterialComponents.Badge = 0x7f11018a
co.auter.hcorp:macro/m3_comp_outlined_card_focus_outline_color = 0x7f0c00ac
co.auter.hcorp:macro/m3_comp_outlined_card_dragged_outline_color = 0x7f0c00ab
co.auter.hcorp:macro/m3_comp_outlined_card_disabled_outline_color = 0x7f0c00aa
co.auter.hcorp:macro/m3_comp_outlined_autocomplete_text_field_input_text_type = 0x7f0c00a2
co.auter.hcorp:style/Widget.MaterialComponents.AppBarLayout.Surface = 0x7f11042b
co.auter.hcorp:string/mtrl_picker_range_header_only_start_selected = 0x7f1000c7
co.auter.hcorp:macro/m3_comp_outlined_autocomplete_menu_container_color = 0x7f0c00a0
co.auter.hcorp:macro/m3_comp_navigation_rail_inactive_hover_state_layer_color = 0x7f0c009b
co.auter.hcorp:macro/m3_comp_navigation_rail_active_pressed_state_layer_color = 0x7f0c0098
co.auter.hcorp:macro/m3_comp_navigation_rail_active_label_text_color = 0x7f0c0097
co.auter.hcorp:macro/m3_comp_navigation_rail_active_icon_color = 0x7f0c0095
co.auter.hcorp:style/Widget.MaterialComponents.Slider = 0x7f11047f
co.auter.hcorp:macro/m3_comp_navigation_rail_active_hover_state_layer_color = 0x7f0c0094
co.auter.hcorp:macro/m3_comp_navigation_drawer_inactive_pressed_state_layer_color = 0x7f0c0090
co.auter.hcorp:macro/m3_comp_navigation_drawer_inactive_pressed_label_text_color = 0x7f0c008f
co.auter.hcorp:macro/m3_comp_navigation_drawer_inactive_pressed_icon_color = 0x7f0c008e
co.auter.hcorp:macro/m3_comp_navigation_drawer_inactive_hover_state_layer_color = 0x7f0c008b
co.auter.hcorp:macro/m3_comp_navigation_drawer_inactive_focus_state_layer_color = 0x7f0c0088
co.auter.hcorp:style/ThemeOverlay.AppCompat.DayNight = 0x7f1102af
co.auter.hcorp:macro/m3_comp_navigation_drawer_inactive_focus_label_text_color = 0x7f0c0087
co.auter.hcorp:style/Theme.Design.NoActionBar = 0x7f110242
co.auter.hcorp:macro/m3_comp_navigation_drawer_inactive_focus_icon_color = 0x7f0c0086
co.auter.hcorp:macro/m3_comp_navigation_drawer_headline_color = 0x7f0c0084
co.auter.hcorp:macro/m3_comp_navigation_drawer_active_pressed_state_layer_color = 0x7f0c0083
co.auter.hcorp:macro/m3_comp_navigation_drawer_active_pressed_label_text_color = 0x7f0c0082
co.auter.hcorp:macro/m3_comp_navigation_drawer_active_pressed_icon_color = 0x7f0c0081
co.auter.hcorp:macro/m3_comp_navigation_drawer_active_indicator_color = 0x7f0c007f
co.auter.hcorp:macro/m3_comp_navigation_drawer_active_hover_icon_color = 0x7f0c007b
co.auter.hcorp:macro/m3_comp_navigation_drawer_active_focus_state_layer_color = 0x7f0c007a
co.auter.hcorp:macro/m3_comp_navigation_drawer_active_focus_label_text_color = 0x7f0c0079
co.auter.hcorp:macro/m3_comp_navigation_bar_label_text_type = 0x7f0c0077
co.auter.hcorp:macro/m3_comp_navigation_bar_inactive_pressed_state_layer_color = 0x7f0c0076
co.auter.hcorp:macro/m3_comp_navigation_bar_inactive_pressed_label_text_color = 0x7f0c0075
co.auter.hcorp:macro/m3_comp_navigation_bar_inactive_hover_icon_color = 0x7f0c006f
co.auter.hcorp:macro/m3_comp_navigation_bar_inactive_focus_state_layer_color = 0x7f0c006e
co.auter.hcorp:macro/m3_comp_navigation_bar_inactive_focus_label_text_color = 0x7f0c006d
co.auter.hcorp:macro/m3_comp_navigation_bar_inactive_focus_icon_color = 0x7f0c006c
co.auter.hcorp:macro/m3_comp_navigation_bar_container_color = 0x7f0c006b
co.auter.hcorp:style/Base.V7.Widget.AppCompat.Toolbar = 0x7f1100c7
co.auter.hcorp:macro/m3_comp_navigation_bar_active_pressed_icon_color = 0x7f0c0068
co.auter.hcorp:macro/m3_comp_navigation_bar_active_label_text_color = 0x7f0c0067
co.auter.hcorp:style/Widget.MaterialComponents.CollapsingToolbar = 0x7f110450
co.auter.hcorp:macro/m3_comp_navigation_bar_active_indicator_color = 0x7f0c0066
co.auter.hcorp:macro/m3_comp_navigation_bar_active_icon_color = 0x7f0c0065
co.auter.hcorp:macro/m3_comp_navigation_bar_active_hover_state_layer_color = 0x7f0c0064
co.auter.hcorp:macro/m3_comp_navigation_bar_active_hover_icon_color = 0x7f0c0062
co.auter.hcorp:macro/m3_comp_navigation_bar_active_focus_label_text_color = 0x7f0c0060
co.auter.hcorp:macro/m3_comp_navigation_bar_active_focus_icon_color = 0x7f0c005f
co.auter.hcorp:macro/m3_comp_menu_list_item_selected_container_color = 0x7f0c005e
co.auter.hcorp:macro/m3_comp_input_chip_label_text_type = 0x7f0c005c
co.auter.hcorp:styleable/KeyFramesVelocity = 0x7f120044
co.auter.hcorp:macro/m3_comp_icon_button_selected_icon_color = 0x7f0c0059
co.auter.hcorp:macro/m3_comp_filter_chip_container_shape = 0x7f0c0057
co.auter.hcorp:macro/m3_comp_filled_tonal_icon_button_toggle_unselected_icon_color = 0x7f0c0056
co.auter.hcorp:macro/m3_comp_filled_tonal_icon_button_toggle_selected_icon_color = 0x7f0c0055
co.auter.hcorp:macro/m3_comp_filled_tonal_icon_button_container_color = 0x7f0c0054
co.auter.hcorp:style/Widget.AppCompat.Light.ActionBar = 0x7f110337
co.auter.hcorp:macro/m3_comp_filled_tonal_button_container_color = 0x7f0c0052
co.auter.hcorp:macro/m3_comp_filled_text_field_supporting_text_type = 0x7f0c0051
co.auter.hcorp:style/ThemeOverlay.Material3.BottomNavigationView = 0x7f1102be
co.auter.hcorp:macro/m3_comp_filled_text_field_error_supporting_text_color = 0x7f0c004e
co.auter.hcorp:style/Widget.Material3.CompoundButton.CheckBox = 0x7f1103b8
co.auter.hcorp:macro/m3_comp_filled_text_field_error_active_indicator_color = 0x7f0c004d
co.auter.hcorp:macro/m3_comp_filled_text_field_container_shape = 0x7f0c004c
co.auter.hcorp:style/Widget.Material3.PopupMenu.ContextMenu = 0x7f1103fc
co.auter.hcorp:macro/m3_comp_filled_text_field_container_color = 0x7f0c004b
co.auter.hcorp:styleable/CollapsingToolbarLayout_Layout = 0x7f120025
co.auter.hcorp:macro/m3_comp_icon_button_unselected_icon_color = 0x7f0c005a
co.auter.hcorp:macro/m3_comp_filled_icon_button_toggle_unselected_icon_color = 0x7f0c004a
co.auter.hcorp:macro/m3_comp_filled_icon_button_container_color = 0x7f0c0048
co.auter.hcorp:macro/m3_comp_filled_card_container_shape = 0x7f0c0047
co.auter.hcorp:macro/m3_comp_filled_card_container_color = 0x7f0c0046
co.auter.hcorp:string/catalyst_inspector_toggle = 0x7f10003c
co.auter.hcorp:macro/m3_comp_filled_button_label_text_type = 0x7f0c0045
co.auter.hcorp:macro/m3_comp_filled_button_label_text_color = 0x7f0c0044
co.auter.hcorp:macro/m3_comp_filled_button_container_color = 0x7f0c0043
co.auter.hcorp:macro/m3_comp_filled_autocomplete_text_field_input_text_type = 0x7f0c0042
co.auter.hcorp:macro/m3_comp_filled_autocomplete_menu_container_color = 0x7f0c0041
co.auter.hcorp:macro/m3_comp_fab_surface_icon_color = 0x7f0c003e
co.auter.hcorp:macro/m3_comp_fab_surface_container_color = 0x7f0c003d
co.auter.hcorp:macro/m3_comp_fab_secondary_container_color = 0x7f0c003b
co.auter.hcorp:macro/m3_comp_fab_primary_small_container_shape = 0x7f0c003a
co.auter.hcorp:macro/m3_comp_fab_primary_large_container_shape = 0x7f0c0039
co.auter.hcorp:macro/m3_comp_fab_primary_icon_color = 0x7f0c0038
co.auter.hcorp:macro/m3_comp_fab_primary_container_shape = 0x7f0c0037
co.auter.hcorp:macro/m3_comp_fab_primary_container_color = 0x7f0c0036
co.auter.hcorp:macro/m3_comp_extended_fab_surface_container_color = 0x7f0c0032
co.auter.hcorp:macro/m3_comp_extended_fab_secondary_icon_color = 0x7f0c0031
co.auter.hcorp:style/MaterialAlertDialog.MaterialComponents.Title.Panel.CenterStacked = 0x7f110141
co.auter.hcorp:macro/m3_comp_extended_fab_secondary_container_color = 0x7f0c0030
co.auter.hcorp:style/Widget.Material3.Chip.Input = 0x7f1103a6
co.auter.hcorp:macro/m3_comp_extended_fab_primary_label_text_type = 0x7f0c002f
co.auter.hcorp:macro/m3_comp_extended_fab_primary_container_shape = 0x7f0c002d
co.auter.hcorp:style/Base.Widget.Material3.ActionMode = 0x7f110104
co.auter.hcorp:macro/m3_comp_extended_fab_primary_container_color = 0x7f0c002c
co.auter.hcorp:style/ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f110300
co.auter.hcorp:style/Base.V21.ThemeOverlay.MaterialComponents.BottomSheetDialog = 0x7f1100b2
co.auter.hcorp:macro/m3_comp_dialog_supporting_text_type = 0x7f0c0027
co.auter.hcorp:macro/m3_comp_dialog_container_color = 0x7f0c0022
co.auter.hcorp:macro/m3_comp_date_picker_modal_year_selection_year_selected_container_color = 0x7f0c001f
co.auter.hcorp:macro/m3_comp_date_picker_modal_weekdays_label_text_type = 0x7f0c001e
co.auter.hcorp:string/catalyst_dev_menu_header = 0x7f100034
co.auter.hcorp:macro/m3_comp_date_picker_modal_range_selection_month_subhead_type = 0x7f0c001c
co.auter.hcorp:macro/m3_comp_date_picker_modal_range_selection_header_headline_type = 0x7f0c001a
co.auter.hcorp:macro/m3_comp_date_picker_modal_header_supporting_text_type = 0x7f0c0018
co.auter.hcorp:macro/m3_comp_date_picker_modal_header_headline_color = 0x7f0c0015
co.auter.hcorp:macro/m3_comp_date_picker_modal_date_unselected_label_text_color = 0x7f0c0014
co.auter.hcorp:macro/m3_comp_date_picker_modal_date_today_label_text_color = 0x7f0c0013
co.auter.hcorp:macro/m3_comp_date_picker_modal_date_today_container_outline_color = 0x7f0c0012
co.auter.hcorp:macro/m3_comp_date_picker_modal_date_selected_label_text_color = 0x7f0c0011
co.auter.hcorp:macro/m3_comp_date_picker_modal_date_label_text_type = 0x7f0c000f
co.auter.hcorp:macro/m3_comp_date_picker_modal_container_color = 0x7f0c000d
co.auter.hcorp:style/Base.V23.Theme.AppCompat.Light = 0x7f1100b6
co.auter.hcorp:macro/m3_comp_primary_navigation_tab_inactive_focus_state_layer_color = 0x7f0c00cc
co.auter.hcorp:macro/m3_comp_checkbox_unselected_outline_color = 0x7f0c000c
co.auter.hcorp:macro/m3_comp_checkbox_selected_container_color = 0x7f0c0006
co.auter.hcorp:macro/m3_comp_badge_large_label_text_color = 0x7f0c0003
co.auter.hcorp:macro/m3_comp_dialog_headline_color = 0x7f0c0024
co.auter.hcorp:macro/m3_comp_badge_color = 0x7f0c0002
co.auter.hcorp:macro/m3_comp_assist_chip_container_shape = 0x7f0c0000
co.auter.hcorp:layout/support_simple_spinner_dropdown_item = 0x7f0b007e
co.auter.hcorp:layout/splash_screen_view = 0x7f0b007d
co.auter.hcorp:style/Base.Widget.AppCompat.PopupMenu = 0x7f1100f0
co.auter.hcorp:layout/select_dialog_singlechoice_material = 0x7f0b007c
co.auter.hcorp:layout/select_dialog_multichoice_material = 0x7f0b007b
co.auter.hcorp:layout/select_dialog_item_material = 0x7f0b007a
co.auter.hcorp:layout/redbox_item_title = 0x7f0b0078
co.auter.hcorp:layout/redbox_item_frame = 0x7f0b0077
co.auter.hcorp:layout/notification_template_part_chronometer = 0x7f0b0074
co.auter.hcorp:layout/notification_template_media_custom = 0x7f0b0073
co.auter.hcorp:style/Base.Widget.Material3.CompoundButton.RadioButton = 0x7f11010a
co.auter.hcorp:layout/notification_template_icon_group = 0x7f0b0070
co.auter.hcorp:layout/notification_template_custom_big = 0x7f0b006f
co.auter.hcorp:layout/notification_template_big_media_custom = 0x7f0b006c
co.auter.hcorp:layout/notification_template_big_media = 0x7f0b006b
co.auter.hcorp:layout/notification_media_cancel_action = 0x7f0b006a
co.auter.hcorp:layout/notification_media_action = 0x7f0b0069
co.auter.hcorp:layout/mtrl_search_view = 0x7f0b0066
co.auter.hcorp:string/abc_action_menu_overflow_description = 0x7f100002
co.auter.hcorp:macro/m3_comp_slider_label_label_text_color = 0x7f0c0112
co.auter.hcorp:layout/mtrl_search_bar = 0x7f0b0065
co.auter.hcorp:layout/mtrl_picker_header_title_text = 0x7f0b0061
co.auter.hcorp:layout/mtrl_picker_header_fullscreen = 0x7f0b005f
co.auter.hcorp:layout/mtrl_picker_fullscreen = 0x7f0b005d
co.auter.hcorp:layout/mtrl_calendar_year = 0x7f0b0057
co.auter.hcorp:styleable/StateListDrawable = 0x7f120085
co.auter.hcorp:style/TextAppearance.Compat.Notification.Time.Media = 0x7f1101db
co.auter.hcorp:layout/mtrl_calendar_days_of_week = 0x7f0b0050
co.auter.hcorp:layout/mtrl_auto_complete_simple_item = 0x7f0b004d
co.auter.hcorp:string/material_minute_selection = 0x7f100090
co.auter.hcorp:layout/mtrl_alert_select_dialog_singlechoice = 0x7f0b004c
co.auter.hcorp:style/Widget.MaterialComponents.TimePicker.Clock = 0x7f110495
co.auter.hcorp:layout/mtrl_alert_select_dialog_multichoice = 0x7f0b004b
co.auter.hcorp:layout/mtrl_alert_select_dialog_item = 0x7f0b004a
co.auter.hcorp:layout/mtrl_alert_dialog_actions = 0x7f0b0048
co.auter.hcorp:style/Base.V26.Theme.AppCompat = 0x7f1100bb
co.auter.hcorp:layout/material_timepicker = 0x7f0b0044
co.auter.hcorp:layout/material_time_chip = 0x7f0b0042
co.auter.hcorp:layout/material_textinput_timepicker = 0x7f0b0041
co.auter.hcorp:layout/material_radial_view_group = 0x7f0b0040
co.auter.hcorp:layout/material_clockface_textview = 0x7f0b003e
co.auter.hcorp:style/Theme.Material3.Dark.Dialog.MinWidth = 0x7f11025b
co.auter.hcorp:layout/material_clock_period_toggle_land = 0x7f0b003d
co.auter.hcorp:layout/material_clock_display_divider = 0x7f0b003b
co.auter.hcorp:layout/material_chip_input_combo = 0x7f0b0039
co.auter.hcorp:layout/m3_side_sheet_dialog = 0x7f0b0038
co.auter.hcorp:layout/m3_alert_dialog_title = 0x7f0b0036
co.auter.hcorp:layout/m3_alert_dialog_actions = 0x7f0b0035
co.auter.hcorp:macro/m3_comp_outlined_autocomplete_text_field_caret_color = 0x7f0c00a1
co.auter.hcorp:layout/ime_secondary_split_test_activity = 0x7f0b0033
co.auter.hcorp:layout/fps_view = 0x7f0b0031
co.auter.hcorp:style/Widget.Design.BottomNavigationView = 0x7f11036f
co.auter.hcorp:layout/dev_loading_view = 0x7f0b0030
co.auter.hcorp:layout/design_text_input_start_icon = 0x7f0b002f
co.auter.hcorp:layout/design_text_input_end_icon = 0x7f0b002e
co.auter.hcorp:layout/design_navigation_menu_item = 0x7f0b002d
co.auter.hcorp:layout/design_navigation_menu = 0x7f0b002c
co.auter.hcorp:layout/design_navigation_item_subheader = 0x7f0b002b
co.auter.hcorp:style/Base.Theme.AppCompat.Dialog = 0x7f110050
co.auter.hcorp:layout/design_navigation_item_header = 0x7f0b0029
co.auter.hcorp:layout/design_menu_item_action_area = 0x7f0b0027
co.auter.hcorp:layout/design_layout_tab_text = 0x7f0b0026
co.auter.hcorp:layout/design_layout_snackbar = 0x7f0b0023
co.auter.hcorp:layout/design_bottom_sheet_dialog = 0x7f0b0022
co.auter.hcorp:layout/design_bottom_navigation_item = 0x7f0b0021
co.auter.hcorp:style/Widget.AppCompat.Light.ActivityChooserView = 0x7f110344
co.auter.hcorp:layout/browser_actions_context_menu_page = 0x7f0b001e
co.auter.hcorp:layout/alert_title_layout = 0x7f0b001c
co.auter.hcorp:layout/abc_tooltip = 0x7f0b001b
co.auter.hcorp:macro/m3_comp_checkbox_selected_error_container_color = 0x7f0c0009
co.auter.hcorp:layout/abc_select_dialog_material = 0x7f0b001a
co.auter.hcorp:layout/abc_screen_simple_overlay_action_mode = 0x7f0b0016
co.auter.hcorp:layout/abc_screen_simple = 0x7f0b0015
co.auter.hcorp:layout/abc_popup_menu_item_layout = 0x7f0b0013
co.auter.hcorp:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.ExposedDropdownMenu = 0x7f110491
co.auter.hcorp:style/TextAppearance.Compat.Notification.Time = 0x7f1101da
co.auter.hcorp:layout/abc_popup_menu_header_item_layout = 0x7f0b0012
co.auter.hcorp:layout/abc_list_menu_item_icon = 0x7f0b000f
co.auter.hcorp:layout/abc_alert_dialog_material = 0x7f0b0009
co.auter.hcorp:layout/abc_alert_dialog_button_bar_material = 0x7f0b0008
co.auter.hcorp:layout/abc_action_mode_bar = 0x7f0b0004
co.auter.hcorp:layout/abc_action_menu_item_layout = 0x7f0b0002
co.auter.hcorp:interpolator/mtrl_linear_out_slow_in = 0x7f0a0011
co.auter.hcorp:styleable/TextInputLayout = 0x7f12008f
co.auter.hcorp:style/Theme.ReactNative.AppCompat.Light.NoActionBar.FullScreen = 0x7f1102a6
co.auter.hcorp:interpolator/mtrl_linear = 0x7f0a0010
co.auter.hcorp:interpolator/mtrl_fast_out_slow_in = 0x7f0a000f
co.auter.hcorp:interpolator/m3_sys_motion_easing_standard = 0x7f0a000b
co.auter.hcorp:style/Animation.Material3.SideSheetDialog = 0x7f110009
co.auter.hcorp:layout/abc_screen_toolbar = 0x7f0b0017
co.auter.hcorp:interpolator/m3_sys_motion_easing_linear = 0x7f0a000a
co.auter.hcorp:style/ThemeOverlay.AppCompat = 0x7f1102ab
co.auter.hcorp:style/ShapeAppearance.M3.Comp.NavigationBar.ActiveIndicator.Shape = 0x7f110169
co.auter.hcorp:string/abc_menu_ctrl_shortcut_label = 0x7f100009
co.auter.hcorp:interpolator/btn_radio_to_on_mtrl_animation_interpolator_0 = 0x7f0a0005
co.auter.hcorp:style/Base.Widget.Material3.ActionBar.Solid = 0x7f110103
co.auter.hcorp:layout/abc_list_menu_item_radio = 0x7f0b0011
co.auter.hcorp:interpolator/btn_radio_to_off_mtrl_animation_interpolator_0 = 0x7f0a0004
co.auter.hcorp:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_1 = 0x7f0a0003
co.auter.hcorp:interpolator/btn_checkbox_unchecked_mtrl_animation_interpolator_0 = 0x7f0a0002
co.auter.hcorp:integer/react_native_dev_server_port = 0x7f090044
co.auter.hcorp:style/TextAppearance.M3.Sys.Typescale.BodySmall = 0x7f1101eb
co.auter.hcorp:integer/mtrl_view_visible = 0x7f090043
co.auter.hcorp:integer/mtrl_view_invisible = 0x7f090042
co.auter.hcorp:integer/mtrl_view_gone = 0x7f090041
co.auter.hcorp:integer/mtrl_tab_indicator_anim_duration_ms = 0x7f090040
co.auter.hcorp:integer/mtrl_switch_track_viewport_width = 0x7f09003f
co.auter.hcorp:integer/mtrl_switch_track_viewport_height = 0x7f09003e
co.auter.hcorp:integer/mtrl_switch_thumb_viewport_size = 0x7f09003d
co.auter.hcorp:integer/mtrl_switch_thumb_viewport_center_coordinate = 0x7f09003c
co.auter.hcorp:integer/mtrl_switch_thumb_pressed_duration = 0x7f09003b
co.auter.hcorp:style/Widget.Material3.Search.ActionButton.Overflow = 0x7f1103ff
co.auter.hcorp:integer/mtrl_switch_thumb_post_morphing_duration = 0x7f090039
co.auter.hcorp:integer/mtrl_switch_thumb_motion_duration = 0x7f090038
co.auter.hcorp:integer/mtrl_chip_anim_duration = 0x7f090037
co.auter.hcorp:integer/mtrl_calendar_header_orientation = 0x7f090032
co.auter.hcorp:style/ThemeOverlay.MaterialComponents.TimePicker = 0x7f110317
co.auter.hcorp:integer/mtrl_badge_max_character_count = 0x7f09002f
co.auter.hcorp:integer/material_motion_path = 0x7f09002e
co.auter.hcorp:integer/material_motion_duration_short_1 = 0x7f09002c
co.auter.hcorp:macro/m3_comp_assist_chip_label_text_type = 0x7f0c0001
co.auter.hcorp:integer/material_motion_duration_medium_2 = 0x7f09002b
co.auter.hcorp:integer/material_motion_duration_long_2 = 0x7f090029
co.auter.hcorp:integer/m3_sys_shape_corner_medium_corner_family = 0x7f090026
co.auter.hcorp:integer/m3_sys_shape_corner_large_corner_family = 0x7f090025
co.auter.hcorp:integer/m3_sys_shape_corner_full_corner_family = 0x7f090024
co.auter.hcorp:integer/m3_sys_motion_path = 0x7f090021
co.auter.hcorp:integer/m3_sys_motion_duration_short2 = 0x7f09001e
co.auter.hcorp:integer/m3_sys_motion_duration_medium4 = 0x7f09001c
co.auter.hcorp:style/Widget.MaterialComponents.TextInputEditText.FilledBox.Dense = 0x7f110487
co.auter.hcorp:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Tertiary = 0x7f1102d7
co.auter.hcorp:integer/m3_sys_motion_duration_medium3 = 0x7f09001b
co.auter.hcorp:style/Platform.ThemeOverlay.AppCompat = 0x7f11014b
co.auter.hcorp:integer/m3_sys_motion_duration_medium1 = 0x7f090019
co.auter.hcorp:integer/m3_sys_motion_duration_long4 = 0x7f090018
co.auter.hcorp:integer/m3_sys_motion_duration_extra_long4 = 0x7f090014
co.auter.hcorp:integer/m3_sys_motion_duration_extra_long1 = 0x7f090011
co.auter.hcorp:integer/m3_card_anim_delay_ms = 0x7f09000e
co.auter.hcorp:string/m3_sys_motion_easing_standard = 0x7f100088
co.auter.hcorp:integer/m3_btn_anim_duration_ms = 0x7f09000d
co.auter.hcorp:integer/m3_badge_max_number = 0x7f09000b
co.auter.hcorp:integer/default_icon_animation_duration = 0x7f090006
co.auter.hcorp:integer/bottom_sheet_slide_duration = 0x7f090003
co.auter.hcorp:integer/abc_config_activityDefaultDur = 0x7f090000
co.auter.hcorp:id/wrap_content = 0x7f080201
co.auter.hcorp:id/withText = 0x7f0801fd
co.auter.hcorp:id/wide = 0x7f0801fc
co.auter.hcorp:layout/mtrl_layout_snackbar_include = 0x7f0b0059
co.auter.hcorp:layout/abc_action_menu_layout = 0x7f0b0003
co.auter.hcorp:id/view_tree_on_back_pressed_dispatcher_owner = 0x7f0801f7
co.auter.hcorp:id/view_tree_lifecycle_owner = 0x7f0801f6
co.auter.hcorp:id/use_hardware_layer = 0x7f0801f1
co.auter.hcorp:interpolator/m3_sys_motion_easing_standard_decelerate = 0x7f0a000d
co.auter.hcorp:id/useLogo = 0x7f0801f0
co.auter.hcorp:id/up = 0x7f0801ef
co.auter.hcorp:id/unlabeled = 0x7f0801ee
co.auter.hcorp:styleable/CheckedTextView = 0x7f12001e
co.auter.hcorp:id/unchecked = 0x7f0801ec
co.auter.hcorp:id/triangle = 0x7f0801eb
co.auter.hcorp:id/transition_scene_layoutid_cache = 0x7f0801e9
co.auter.hcorp:id/transition_layout_save = 0x7f0801e6
co.auter.hcorp:string/mtrl_chip_close_icon_content_description = 0x7f1000b0
co.auter.hcorp:id/transition_current_scene = 0x7f0801e4
co.auter.hcorp:id/transition_clip = 0x7f0801e3
co.auter.hcorp:id/transitionToStart = 0x7f0801e2
co.auter.hcorp:id/transitionToEnd = 0x7f0801e1
co.auter.hcorp:id/transform_origin = 0x7f0801e0
co.auter.hcorp:id/top = 0x7f0801dc
co.auter.hcorp:macro/m3_comp_outlined_card_outline_color = 0x7f0c00ae
co.auter.hcorp:id/toggle = 0x7f0801db
co.auter.hcorp:id/title_template = 0x7f0801da
co.auter.hcorp:id/titleDividerNoCustom = 0x7f0801d9
co.auter.hcorp:id/time = 0x7f0801d7
co.auter.hcorp:id/textinput_suffix_text = 0x7f0801d6
co.auter.hcorp:id/textinput_prefix_text = 0x7f0801d5
co.auter.hcorp:id/textinput_helper_text = 0x7f0801d3
co.auter.hcorp:id/textinput_error = 0x7f0801d2
co.auter.hcorp:id/textinput_counter = 0x7f0801d1
co.auter.hcorp:id/text_input_error_icon = 0x7f0801cf
co.auter.hcorp:id/text_input_end_icon = 0x7f0801ce
co.auter.hcorp:id/textStart = 0x7f0801cc
co.auter.hcorp:id/textEnd = 0x7f0801c9
co.auter.hcorp:id/text2 = 0x7f0801c8
co.auter.hcorp:id/text = 0x7f0801c7
co.auter.hcorp:layout/design_layout_snackbar_include = 0x7f0b0024
co.auter.hcorp:id/tag_window_insets_animation_callback = 0x7f0801c6
co.auter.hcorp:id/tag_unhandled_key_listeners = 0x7f0801c5
co.auter.hcorp:macro/m3_comp_sheet_bottom_docked_drag_handle_color = 0x7f0c0106
co.auter.hcorp:id/tag_unhandled_key_event_manager = 0x7f0801c4
co.auter.hcorp:id/tag_transition_group = 0x7f0801c3
co.auter.hcorp:id/tag_state_description = 0x7f0801c2
co.auter.hcorp:id/tag_on_receive_content_mime_types = 0x7f0801c0
co.auter.hcorp:style/Widget.Material3.MaterialTimePicker = 0x7f1103ee
co.auter.hcorp:id/tag_on_apply_window_listener = 0x7f0801be
co.auter.hcorp:style/Base.Animation.AppCompat.Dialog = 0x7f110010
co.auter.hcorp:id/tag_accessibility_pane_title = 0x7f0801bd
co.auter.hcorp:macro/m3_comp_time_picker_time_selector_selected_container_color = 0x7f0c0160
co.auter.hcorp:macro/m3_comp_secondary_navigation_tab_hover_state_layer_color = 0x7f0c00fe
co.auter.hcorp:id/tag_accessibility_heading = 0x7f0801bc
co.auter.hcorp:id/tag_accessibility_clickable_spans = 0x7f0801bb
co.auter.hcorp:id/tag_accessibility_actions = 0x7f0801ba
co.auter.hcorp:id/submenuarrow = 0x7f0801b7
co.auter.hcorp:style/Base.Widget.AppCompat.ImageButton = 0x7f1100e2
co.auter.hcorp:id/view_offset_helper = 0x7f0801f3
co.auter.hcorp:id/stretch = 0x7f0801b6
co.auter.hcorp:id/stop = 0x7f0801b5
co.auter.hcorp:styleable/MaterialCheckBox = 0x7f120056
co.auter.hcorp:style/ThemeOverlay.MaterialComponents.DayNight.BottomSheetDialog = 0x7f110303
co.auter.hcorp:id/status_bar_latest_event_content = 0x7f0801b4
co.auter.hcorp:style/Base.V14.Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f11009f
co.auter.hcorp:id/staticLayout = 0x7f0801b2
co.auter.hcorp:id/startHorizontal = 0x7f0801af
co.auter.hcorp:string/state_unselected_description = 0x7f1000f9
co.auter.hcorp:id/standard = 0x7f0801ad
co.auter.hcorp:style/Base.Widget.Material3.CompoundButton.Switch = 0x7f11010b
co.auter.hcorp:macro/m3_sys_color_dark_surface_tint = 0x7f0c0175
co.auter.hcorp:id/src_over = 0x7f0801ac
co.auter.hcorp:id/src_in = 0x7f0801ab
co.auter.hcorp:style/Base.V14.ThemeOverlay.Material3.BottomSheetDialog = 0x7f1100a0
co.auter.hcorp:id/square = 0x7f0801a9
co.auter.hcorp:id/spread_inside = 0x7f0801a8
co.auter.hcorp:style/Widget.Support.CoordinatorLayout = 0x7f1104a2
co.auter.hcorp:style/Widget.Material3.Chip.Input.Icon.Elevated = 0x7f1103a9
co.auter.hcorp:id/spread = 0x7f0801a7
co.auter.hcorp:id/spline = 0x7f0801a5
co.auter.hcorp:id/splashscreen_icon_view = 0x7f0801a4
co.auter.hcorp:id/special_effects_controller_view_tag = 0x7f0801a3
co.auter.hcorp:id/snapMargins = 0x7f0801a1
co.auter.hcorp:id/snap = 0x7f0801a0
co.auter.hcorp:id/snackbar_action = 0x7f08019e
co.auter.hcorp:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Secondary = 0x7f1103be
co.auter.hcorp:id/showTitle = 0x7f08019a
co.auter.hcorp:id/shortcut = 0x7f080197
co.auter.hcorp:id/select_dialog_listview = 0x7f080194
co.auter.hcorp:id/search_voice_btn = 0x7f080193
co.auter.hcorp:macro/m3_comp_extended_fab_surface_icon_color = 0x7f0c0033
co.auter.hcorp:id/search_plate = 0x7f080191
co.auter.hcorp:style/Base.DialogWindowTitle.AppCompat = 0x7f110014
co.auter.hcorp:id/search_mag_icon = 0x7f080190
co.auter.hcorp:string/mtrl_picker_date_header_unselected = 0x7f1000bc
co.auter.hcorp:id/uniform = 0x7f0801ed
co.auter.hcorp:id/search_go_btn = 0x7f08018f
co.auter.hcorp:id/search_bar = 0x7f08018b
co.auter.hcorp:id/search_badge = 0x7f08018a
co.auter.hcorp:style/Theme.AppCompat.DayNight.Dialog = 0x7f110227
co.auter.hcorp:id/scrollable = 0x7f080189
co.auter.hcorp:style/TextAppearance.Material3.HeadlineMedium = 0x7f110201
co.auter.hcorp:id/scrollView = 0x7f080188
co.auter.hcorp:id/scrollIndicatorUp = 0x7f080187
co.auter.hcorp:id/scrollIndicatorDown = 0x7f080186
co.auter.hcorp:id/scroll = 0x7f080185
co.auter.hcorp:id/screen = 0x7f080184
co.auter.hcorp:macro/m3_comp_secondary_navigation_tab_with_icon_active_icon_color = 0x7f0c0102
co.auter.hcorp:id/save_overlay_view = 0x7f080181
co.auter.hcorp:macro/m3_comp_navigation_rail_inactive_pressed_state_layer_color = 0x7f0c009e
co.auter.hcorp:id/save_non_transition_alpha = 0x7f080180
co.auter.hcorp:id/row_index_key = 0x7f08017f
co.auter.hcorp:id/selection_type = 0x7f080196
co.auter.hcorp:id/rounded = 0x7f08017e
co.auter.hcorp:id/rn_redbox_report_button = 0x7f08017a
co.auter.hcorp:id/rn_redbox_reload_button = 0x7f080179
co.auter.hcorp:id/rn_redbox_loading_indicator = 0x7f080178
co.auter.hcorp:id/rn_redbox_line_separator = 0x7f080177
co.auter.hcorp:id/rn_frame_method = 0x7f080175
co.auter.hcorp:id/rn_frame_file = 0x7f080174
co.auter.hcorp:id/right_side = 0x7f080173
co.auter.hcorp:id/right_icon = 0x7f080172
co.auter.hcorp:id/right = 0x7f080170
co.auter.hcorp:id/reverseSawtooth = 0x7f08016f
co.auter.hcorp:id/rectangles = 0x7f08016d
co.auter.hcorp:styleable/PropertySet = 0x7f120071
co.auter.hcorp:string/abc_capital_on = 0x7f100007
co.auter.hcorp:id/react_test_id = 0x7f08016c
co.auter.hcorp:id/ratio = 0x7f08016b
co.auter.hcorp:id/pressed = 0x7f080167
co.auter.hcorp:string/mtrl_picker_start_date_description = 0x7f1000cc
co.auter.hcorp:id/position = 0x7f080165
co.auter.hcorp:id/percent = 0x7f080162
co.auter.hcorp:id/password_toggle = 0x7f08015e
co.auter.hcorp:style/Theme.MaterialComponents.BottomSheetDialog = 0x7f110276
co.auter.hcorp:id/parent_matrix = 0x7f08015d
co.auter.hcorp:id/parentRelative = 0x7f08015c
co.auter.hcorp:id/parentPanel = 0x7f08015b
co.auter.hcorp:style/Widget.Material3.ExtendedFloatingActionButton.Secondary = 0x7f1103c2
co.auter.hcorp:id/parallax = 0x7f080159
co.auter.hcorp:id/packed = 0x7f080158
co.auter.hcorp:id/open_search_view_toolbar_container = 0x7f080155
co.auter.hcorp:style/Theme.Design.Light.NoActionBar = 0x7f110241
co.auter.hcorp:id/open_search_view_toolbar = 0x7f080154
co.auter.hcorp:id/open_search_view_status_bar_spacer = 0x7f080153
co.auter.hcorp:string/call_notification_ongoing_text = 0x7f10002c
co.auter.hcorp:macro/m3_comp_top_app_bar_small_headline_color = 0x7f0c0170
co.auter.hcorp:id/open_search_view_header_container = 0x7f08014f
co.auter.hcorp:layout/design_navigation_item = 0x7f0b0028
co.auter.hcorp:id/open_search_view_edit_text = 0x7f08014e
co.auter.hcorp:id/open_search_view_dummy_toolbar = 0x7f08014d
co.auter.hcorp:style/Theme.SplashScreen.IconBackground = 0x7f1102aa
co.auter.hcorp:integer/m3_sys_motion_duration_long1 = 0x7f090015
co.auter.hcorp:id/open_search_view_divider = 0x7f08014c
co.auter.hcorp:id/open_search_view_clear_button = 0x7f08014a
co.auter.hcorp:id/open_search_view_background = 0x7f080149
co.auter.hcorp:macro/m3_comp_filled_text_field_input_text_type = 0x7f0c0050
co.auter.hcorp:id/off = 0x7f080146
co.auter.hcorp:id/notification_main_column_container = 0x7f080145
co.auter.hcorp:id/none = 0x7f080141
co.auter.hcorp:style/Widget.Material3.FloatingActionButton.Large.Tertiary = 0x7f1103c8
co.auter.hcorp:id/noScroll = 0x7f080140
co.auter.hcorp:id/never = 0x7f08013f
co.auter.hcorp:id/navigation_bar_item_small_label_view = 0x7f08013d
co.auter.hcorp:macro/m3_comp_date_picker_modal_weekdays_label_text_color = 0x7f0c001d
co.auter.hcorp:id/navigation_bar_item_large_label_view = 0x7f08013c
co.auter.hcorp:id/navigation_bar_item_icon_container = 0x7f080139
co.auter.hcorp:id/navigation_bar_item_active_indicator_view = 0x7f080138
co.auter.hcorp:styleable/AppBarLayout = 0x7f12000a
co.auter.hcorp:id/multiply = 0x7f080137
co.auter.hcorp:id/mtrl_picker_text_input_date = 0x7f080132
co.auter.hcorp:id/mtrl_picker_header_selection_text = 0x7f08012f
co.auter.hcorp:id/mtrl_picker_header = 0x7f08012e
co.auter.hcorp:macro/m3_comp_switch_unselected_hover_state_layer_color = 0x7f0c0137
co.auter.hcorp:id/mtrl_motion_snapshot_view = 0x7f08012c
co.auter.hcorp:id/mtrl_card_checked_layer_id = 0x7f080129
co.auter.hcorp:id/mtrl_calendar_text_input_frame = 0x7f080127
co.auter.hcorp:id/mtrl_calendar_selection_frame = 0x7f080126
co.auter.hcorp:id/mtrl_calendar_months = 0x7f080125
co.auter.hcorp:id/mtrl_calendar_main_pane = 0x7f080124
co.auter.hcorp:id/mtrl_calendar_days_of_week = 0x7f080122
co.auter.hcorp:string/catalyst_report_button = 0x7f100044
co.auter.hcorp:id/mtrl_calendar_day_selector_frame = 0x7f080121
co.auter.hcorp:id/mtrl_anchor_parent = 0x7f080120
co.auter.hcorp:style/TextAppearance.AppCompat.Menu = 0x7f1101b7
co.auter.hcorp:id/motion_base = 0x7f08011f
co.auter.hcorp:id/month_title = 0x7f08011e
co.auter.hcorp:id/month_navigation_previous = 0x7f08011d
co.auter.hcorp:style/TextAppearance.AppCompat.Display1 = 0x7f1101a9
co.auter.hcorp:id/month_navigation_fragment_toggle = 0x7f08011b
co.auter.hcorp:id/mix_blend_mode = 0x7f080118
co.auter.hcorp:id/message = 0x7f080115
co.auter.hcorp:id/media_actions = 0x7f080114
co.auter.hcorp:id/material_value_index = 0x7f080112
co.auter.hcorp:id/material_timepicker_ok_button = 0x7f080110
co.auter.hcorp:style/Widget.Material3.Toolbar.OnSurface = 0x7f110421
co.auter.hcorp:integer/mtrl_btn_anim_duration_ms = 0x7f090031
co.auter.hcorp:id/rightToLeft = 0x7f080171
co.auter.hcorp:id/material_timepicker_mode_button = 0x7f08010f
co.auter.hcorp:style/Widget.AppCompat.Light.SearchView = 0x7f11034b
co.auter.hcorp:id/material_timepicker_container = 0x7f08010e
co.auter.hcorp:id/material_timepicker_cancel_button = 0x7f08010d
co.auter.hcorp:id/material_textinput_timepicker = 0x7f08010c
co.auter.hcorp:id/mtrl_calendar_year_selector_frame = 0x7f080128
co.auter.hcorp:id/material_minute_text_input = 0x7f08010a
co.auter.hcorp:id/material_label = 0x7f080109
co.auter.hcorp:id/material_hour_tv = 0x7f080108
co.auter.hcorp:id/material_clock_period_pm_button = 0x7f080105
co.auter.hcorp:style/Base.Theme.AppCompat.Light = 0x7f110055
co.auter.hcorp:integer/mtrl_btn_anim_delay_ms = 0x7f090030
co.auter.hcorp:id/material_clock_period_am_button = 0x7f080104
co.auter.hcorp:id/material_clock_hand = 0x7f080102
co.auter.hcorp:id/material_clock_face = 0x7f080101
co.auter.hcorp:layout/mtrl_picker_actions = 0x7f0b005b
co.auter.hcorp:id/material_clock_display_and_toggle = 0x7f080100
co.auter.hcorp:id/material_clock_display = 0x7f0800ff
co.auter.hcorp:id/masked = 0x7f0800fd
co.auter.hcorp:style/RtlOverlay.Widget.AppCompat.PopupMenuItem = 0x7f110156
co.auter.hcorp:id/marquee = 0x7f0800fc
co.auter.hcorp:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox = 0x7f1102fc
co.auter.hcorp:id/list_item = 0x7f0800fa
co.auter.hcorp:id/line3 = 0x7f0800f7
co.auter.hcorp:style/TextAppearance.M3.Sys.Typescale.DisplaySmall = 0x7f1101ee
co.auter.hcorp:style/Base.ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f11008e
co.auter.hcorp:id/line1 = 0x7f0800f6
co.auter.hcorp:id/light = 0x7f0800f5
co.auter.hcorp:layout/redbox_view = 0x7f0b0079
co.auter.hcorp:id/postLayout = 0x7f080166
co.auter.hcorp:id/legacy = 0x7f0800f4
co.auter.hcorp:id/layout = 0x7f0800f1
co.auter.hcorp:id/labelled_by = 0x7f0800f0
co.auter.hcorp:id/jumpToStart = 0x7f0800ee
co.auter.hcorp:styleable/BlurView = 0x7f120016
co.auter.hcorp:id/jumpToEnd = 0x7f0800ed
co.auter.hcorp:id/info = 0x7f0800e7
co.auter.hcorp:id/middle = 0x7f080116
co.auter.hcorp:id/image = 0x7f0800e5
co.auter.hcorp:id/ignore = 0x7f0800e3
co.auter.hcorp:id/ifRoom = 0x7f0800e2
co.auter.hcorp:id/icon = 0x7f0800df
co.auter.hcorp:id/homeAsUp = 0x7f0800dd
co.auter.hcorp:string/common_google_play_services_unknown_issue = 0x7f100057
co.auter.hcorp:id/hideable = 0x7f0800db
co.auter.hcorp:id/hide_ime_id = 0x7f0800da
co.auter.hcorp:id/header_title = 0x7f0800d9
co.auter.hcorp:id/groups = 0x7f0800d8
co.auter.hcorp:id/graph_wrap = 0x7f0800d6
co.auter.hcorp:id/scale = 0x7f080183
co.auter.hcorp:id/graph = 0x7f0800d5
co.auter.hcorp:id/gone = 0x7f0800d4
co.auter.hcorp:id/ghost_view_holder = 0x7f0800d2
co.auter.hcorp:string/m3_sys_motion_easing_emphasized_path_data = 0x7f100083
co.auter.hcorp:id/ghost_view = 0x7f0800d1
co.auter.hcorp:id/fullscreen_header = 0x7f0800d0
co.auter.hcorp:id/fragment_container_view_tag = 0x7f0800cf
co.auter.hcorp:id/fps_text = 0x7f0800ce
co.auter.hcorp:id/focusCrop = 0x7f0800cc
co.auter.hcorp:id/fitStart = 0x7f0800c6
co.auter.hcorp:style/Theme.MaterialComponents.Dialog.FixedSize = 0x7f11028d
co.auter.hcorp:id/fitEnd = 0x7f0800c5
co.auter.hcorp:id/fitCenter = 0x7f0800c4
co.auter.hcorp:id/fitBottomStart = 0x7f0800c3
co.auter.hcorp:id/fill_horizontal = 0x7f0800bf
co.auter.hcorp:id/expand_activities_button = 0x7f0800bb
co.auter.hcorp:style/ThemeOverlay.Material3.DynamicColors.Light = 0x7f1102d3
co.auter.hcorp:id/exitUntilCollapsed = 0x7f0800ba
co.auter.hcorp:id/escape = 0x7f0800b9
co.auter.hcorp:style/Platform.MaterialComponents.Dialog = 0x7f110148
co.auter.hcorp:id/enterAlwaysCollapsed = 0x7f0800b8
co.auter.hcorp:id/enterAlways = 0x7f0800b7
co.auter.hcorp:id/endToStart = 0x7f0800b5
co.auter.hcorp:id/end = 0x7f0800b4
co.auter.hcorp:id/embed = 0x7f0800b3
co.auter.hcorp:layout/abc_activity_chooser_view = 0x7f0b0006
co.auter.hcorp:id/elastic = 0x7f0800b2
co.auter.hcorp:id/easeOut = 0x7f0800ae
co.auter.hcorp:id/end_padder = 0x7f0800b6
co.auter.hcorp:id/easeInOut = 0x7f0800ad
co.auter.hcorp:id/dropdown_menu = 0x7f0800ab
co.auter.hcorp:id/dragLeft = 0x7f0800a7
co.auter.hcorp:id/dragEnd = 0x7f0800a6
co.auter.hcorp:string/combobox_description = 0x7f10004e
co.auter.hcorp:id/disjoint = 0x7f0800a4
co.auter.hcorp:id/disableHome = 0x7f0800a1
co.auter.hcorp:id/dimensions = 0x7f08009f
co.auter.hcorp:style/ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f1102ae
co.auter.hcorp:style/ShapeAppearanceOverlay.MaterialComponents.TextInputLayout.FilledBox = 0x7f1101a1
co.auter.hcorp:id/design_bottom_sheet = 0x7f080099
co.auter.hcorp:id/decor_content_parent = 0x7f080096
co.auter.hcorp:id/decelerateAndComplete = 0x7f080095
co.auter.hcorp:id/date_picker_actions = 0x7f080093
co.auter.hcorp:id/cut = 0x7f080091
co.auter.hcorp:id/customPanel = 0x7f080090
co.auter.hcorp:id/cradle = 0x7f08008e
co.auter.hcorp:id/month_navigation_next = 0x7f08011c
co.auter.hcorp:id/coordinator = 0x7f08008b
co.auter.hcorp:string/mtrl_switch_thumb_path_morphing = 0x7f1000da
co.auter.hcorp:id/contiguous = 0x7f08008a
co.auter.hcorp:id/contentPanel = 0x7f080089
co.auter.hcorp:id/content = 0x7f080088
co.auter.hcorp:id/confirm_button = 0x7f080086
co.auter.hcorp:style/Widget.AppCompat.Light.ActionBar.Solid = 0x7f110338
co.auter.hcorp:id/compress = 0x7f080085
co.auter.hcorp:id/collapseActionView = 0x7f080084
co.auter.hcorp:styleable/CoordinatorLayout = 0x7f12002c
co.auter.hcorp:id/clockwise = 0x7f080083
co.auter.hcorp:macro/m3_comp_radio_button_unselected_pressed_state_layer_color = 0x7f0c00e5
co.auter.hcorp:id/circle_center = 0x7f08007f
co.auter.hcorp:id/checkbox = 0x7f08007c
co.auter.hcorp:id/chains = 0x7f08007b
co.auter.hcorp:id/chain = 0x7f08007a
co.auter.hcorp:id/center_vertical = 0x7f080079
co.auter.hcorp:id/centerCrop = 0x7f080076
co.auter.hcorp:id/center = 0x7f080075
co.auter.hcorp:id/rn_redbox_report_label = 0x7f08017b
co.auter.hcorp:id/catalyst_redbox_title = 0x7f080074
co.auter.hcorp:id/cancel_action = 0x7f080072
co.auter.hcorp:id/button_text = 0x7f080071
co.auter.hcorp:id/button = 0x7f08006f
co.auter.hcorp:id/browser_actions_menu_view = 0x7f08006e
co.auter.hcorp:id/browser_actions_menu_item_text = 0x7f08006c
co.auter.hcorp:id/bounce = 0x7f080069
co.auter.hcorp:id/bottom = 0x7f080068
co.auter.hcorp:id/blocking = 0x7f080067
co.auter.hcorp:style/Base.Theme.Material3.Dark = 0x7f11005c
co.auter.hcorp:string/progressbar_description = 0x7f1000e7
co.auter.hcorp:id/beginOnFirstDraw = 0x7f080065
co.auter.hcorp:style/Widget.MaterialComponents.AppBarLayout.PrimarySurface = 0x7f11042a
co.auter.hcorp:id/baseline = 0x7f080064
co.auter.hcorp:id/autofill_inline_suggestion_title = 0x7f080062
co.auter.hcorp:id/autofill_inline_suggestion_subtitle = 0x7f080061
co.auter.hcorp:id/autofill_inline_suggestion_start_icon = 0x7f080060
co.auter.hcorp:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense = 0x7f11048f
co.auter.hcorp:style/Theme.MaterialComponents.DayNight.BottomSheetDialog = 0x7f11027a
co.auter.hcorp:id/autofill_inline_suggestion_end_icon = 0x7f08005f
co.auter.hcorp:style/Base.Theme.Material3.Dark.BottomSheetDialog = 0x7f11005d
co.auter.hcorp:string/mtrl_picker_end_date_description = 0x7f1000be
co.auter.hcorp:id/autoCompleteToStart = 0x7f08005e
co.auter.hcorp:string/google_app_id = 0x7f100071
co.auter.hcorp:id/autoCompleteToEnd = 0x7f08005d
co.auter.hcorp:id/auto = 0x7f08005b
co.auter.hcorp:id/async = 0x7f08005a
co.auter.hcorp:style/Theme.Material3.Light.Dialog.Alert = 0x7f110270
co.auter.hcorp:id/asConfigured = 0x7f080059
co.auter.hcorp:id/animateToStart = 0x7f080057
co.auter.hcorp:id/animateToEnd = 0x7f080056
co.auter.hcorp:id/always = 0x7f080055
co.auter.hcorp:style/Theme.MaterialComponents.Light.Dialog = 0x7f110297
co.auter.hcorp:id/aligned = 0x7f080053
co.auter.hcorp:layout/abc_expanded_menu_layout = 0x7f0b000d
co.auter.hcorp:id/alert_title = 0x7f080052
co.auter.hcorp:id/alertTitle = 0x7f080051
co.auter.hcorp:id/adjust_width = 0x7f080050
co.auter.hcorp:integer/design_snackbar_text_max_lines = 0x7f090007
co.auter.hcorp:id/add = 0x7f08004e
co.auter.hcorp:id/activity_chooser_view_content = 0x7f08004d
co.auter.hcorp:id/action_mode_close_button = 0x7f08004a
co.auter.hcorp:id/action_mode_bar_stub = 0x7f080049
co.auter.hcorp:id/action_mode_bar = 0x7f080048
co.auter.hcorp:style/Widget.Material3.Toolbar.Surface = 0x7f110422
co.auter.hcorp:style/Widget.Material3.SideSheet.Detached = 0x7f110407
co.auter.hcorp:id/action_menu_presenter = 0x7f080047
co.auter.hcorp:id/browser_actions_header_text = 0x7f08006a
co.auter.hcorp:id/action_bar_title = 0x7f080041
co.auter.hcorp:id/action_bar_spinner = 0x7f08003f
co.auter.hcorp:id/action_bar_container = 0x7f08003d
co.auter.hcorp:string/fallback_menu_item_share_link = 0x7f10006d
co.auter.hcorp:id/action_bar_activity_content = 0x7f08003c
co.auter.hcorp:id/showHome = 0x7f080199
co.auter.hcorp:id/action_bar = 0x7f08003b
co.auter.hcorp:style/Widget.Material3.Button.TextButton.Snackbar = 0x7f11039a
co.auter.hcorp:id/action0 = 0x7f08003a
co.auter.hcorp:id/accessibility_state = 0x7f080037
co.auter.hcorp:id/accessibility_role = 0x7f080036
co.auter.hcorp:id/accessibility_links = 0x7f080035
co.auter.hcorp:id/accessibility_label = 0x7f080034
co.auter.hcorp:id/invalidate_transform = 0x7f0800e8
co.auter.hcorp:id/accessibility_hint = 0x7f080033
co.auter.hcorp:id/accessibility_custom_action_9 = 0x7f080032
co.auter.hcorp:id/visible_removing_fragment_view_tag = 0x7f0801fb
co.auter.hcorp:id/accessibility_custom_action_8 = 0x7f080031
co.auter.hcorp:integer/material_motion_duration_medium_1 = 0x7f09002a
co.auter.hcorp:id/accessibility_custom_action_7 = 0x7f080030
co.auter.hcorp:id/accessibility_custom_action_5 = 0x7f08002e
co.auter.hcorp:style/TextAppearance.Design.Snackbar.Message = 0x7f1101e6
co.auter.hcorp:id/accessibility_custom_action_4 = 0x7f08002d
co.auter.hcorp:id/accessibility_custom_action_31 = 0x7f08002c
co.auter.hcorp:styleable/MaterialCalendar = 0x7f120053
co.auter.hcorp:id/accessibility_custom_action_3 = 0x7f08002a
co.auter.hcorp:id/accessibility_custom_action_28 = 0x7f080028
co.auter.hcorp:id/accessibility_custom_action_27 = 0x7f080027
co.auter.hcorp:id/accessibility_custom_action_26 = 0x7f080026
co.auter.hcorp:string/material_motion_easing_emphasized = 0x7f100094
co.auter.hcorp:id/accessibility_custom_action_25 = 0x7f080025
co.auter.hcorp:style/Theme.Catalyst.RedBox = 0x7f11023c
co.auter.hcorp:macro/m3_comp_switch_disabled_unselected_track_outline_color = 0x7f0c011f
co.auter.hcorp:id/accessibility_custom_action_23 = 0x7f080023
co.auter.hcorp:id/accessibility_custom_action_20 = 0x7f080020
co.auter.hcorp:style/Animation.Material3.SideSheetDialog.Right = 0x7f11000b
co.auter.hcorp:id/accessibility_custom_action_2 = 0x7f08001f
co.auter.hcorp:id/accessibility_custom_action_17 = 0x7f08001c
co.auter.hcorp:id/accessibility_custom_action_15 = 0x7f08001a
co.auter.hcorp:style/Base.Widget.AppCompat.ActionButton.Overflow = 0x7f1100cf
co.auter.hcorp:id/accessibility_custom_action_13 = 0x7f080018
co.auter.hcorp:id/accessibility_custom_action_10 = 0x7f080015
co.auter.hcorp:layout/mtrl_calendar_month_navigation = 0x7f0b0054
co.auter.hcorp:id/accessibility_custom_action_1 = 0x7f080014
co.auter.hcorp:id/accessibility_custom_action_0 = 0x7f080013
co.auter.hcorp:id/accessibility_collection = 0x7f080011
co.auter.hcorp:style/TextAppearance.MaterialComponents.Subtitle2 = 0x7f11021b
co.auter.hcorp:id/accessibility_action_clickable_span = 0x7f08000f
co.auter.hcorp:id/accelerate = 0x7f08000e
co.auter.hcorp:id/SYM = 0x7f08000b
co.auter.hcorp:id/NO_DEBUG = 0x7f080006
co.auter.hcorp:id/BOTTOM_START = 0x7f080002
co.auter.hcorp:id/SHOW_PROGRESS = 0x7f08000a
co.auter.hcorp:id/BOTTOM_END = 0x7f080001
co.auter.hcorp:style/TextAppearance.Material3.HeadlineSmall = 0x7f110202
co.auter.hcorp:id/ALT = 0x7f080000
co.auter.hcorp:drawable/test_level_drawable = 0x7f070106
co.auter.hcorp:drawable/splashscreen_logo = 0x7f070105
co.auter.hcorp:drawable/rns_rounder_top_corners_shape = 0x7f070104
co.auter.hcorp:drawable/rn_edit_text_material = 0x7f070103
co.auter.hcorp:attr/prefixTextColor = 0x7f03036a
co.auter.hcorp:drawable/paused_in_debugger_background = 0x7f0700ff
co.auter.hcorp:macro/m3_comp_slider_disabled_active_track_color = 0x7f0c010c
co.auter.hcorp:drawable/notification_bg_normal = 0x7f0700f7
co.auter.hcorp:attr/clockIcon = 0x7f0300d2
co.auter.hcorp:color/m3_sys_color_dark_surface_container_low = 0x7f050189
co.auter.hcorp:drawable/notification_bg_low = 0x7f0700f4
co.auter.hcorp:color/mtrl_chip_text_color = 0x7f0502d9
co.auter.hcorp:drawable/notification_action_background = 0x7f0700f2
co.auter.hcorp:style/Base.V21.Theme.AppCompat.Light = 0x7f1100a9
co.auter.hcorp:color/m3_button_ripple_color_selector = 0x7f05007b
co.auter.hcorp:color/m3_chip_assist_text_color = 0x7f050083
co.auter.hcorp:drawable/mtrl_switch_thumb_checked_unchecked = 0x7f0700e7
co.auter.hcorp:id/accessibility_custom_action_24 = 0x7f080024
co.auter.hcorp:drawable/mtrl_switch_thumb_checked = 0x7f0700e5
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral10 = 0x7f0500b2
co.auter.hcorp:drawable/mtrl_switch_thumb = 0x7f0700e4
co.auter.hcorp:style/Widget.Material3.Slider.Label = 0x7f11040b
co.auter.hcorp:drawable/mtrl_popupmenu_background = 0x7f0700e2
co.auter.hcorp:drawable/mtrl_ic_checkbox_unchecked = 0x7f0700de
co.auter.hcorp:string/material_hour_selection = 0x7f10008e
co.auter.hcorp:attr/dayTodayStyle = 0x7f030163
co.auter.hcorp:color/m3_tabs_icon_color_secondary = 0x7f05020e
co.auter.hcorp:drawable/mtrl_ic_checkbox_checked = 0x7f0700dd
co.auter.hcorp:drawable/mtrl_ic_cancel = 0x7f0700db
co.auter.hcorp:drawable/mtrl_dropdown_arrow = 0x7f0700d8
co.auter.hcorp:drawable/mtrl_checkbox_button_icon_indeterminate_unchecked = 0x7f0700d3
co.auter.hcorp:id/accessibility_custom_action_6 = 0x7f08002f
co.auter.hcorp:drawable/mtrl_checkbox_button_icon_checked_unchecked = 0x7f0700d1
co.auter.hcorp:drawable/mtrl_checkbox_button = 0x7f0700cd
co.auter.hcorp:styleable/MenuView = 0x7f120062
co.auter.hcorp:drawable/material_ic_menu_arrow_up_black_24dp = 0x7f0700cb
co.auter.hcorp:drawable/material_ic_menu_arrow_down_black_24dp = 0x7f0700ca
co.auter.hcorp:anim/rns_ios_from_left_foreground_close = 0x7f01003c
co.auter.hcorp:attr/shapeAppearanceOverlay = 0x7f0303ab
co.auter.hcorp:drawable/material_ic_edit_black_24dp = 0x7f0700c5
co.auter.hcorp:drawable/material_cursor_drawable = 0x7f0700c2
co.auter.hcorp:style/Widget.Material3.PopupMenu.ListPopupWindow = 0x7f1103fd
co.auter.hcorp:drawable/m3_tabs_transparent_background = 0x7f0700c1
co.auter.hcorp:drawable/m3_tabs_line_indicator = 0x7f0700bf
co.auter.hcorp:style/Widget.MaterialComponents.Toolbar.Primary = 0x7f11049e
co.auter.hcorp:color/m3_sys_color_dynamic_dark_outline_variant = 0x7f0501a1
co.auter.hcorp:drawable/m3_radiobutton_ripple = 0x7f0700bc
co.auter.hcorp:dimen/compat_button_inset_vertical_material = 0x7f06005a
co.auter.hcorp:drawable/m3_avd_hide_password = 0x7f0700b7
co.auter.hcorp:style/MaterialAlertDialog.Material3.Title.Text = 0x7f110138
co.auter.hcorp:drawable/icon_background = 0x7f0700b5
co.auter.hcorp:drawable/ic_mtrl_chip_checked_black = 0x7f0700ad
co.auter.hcorp:drawable/ic_m3_chip_close = 0x7f0700ab
co.auter.hcorp:attr/expandedTitleTextColor = 0x7f0301b7
co.auter.hcorp:attr/behavior_autoShrink = 0x7f03006d
co.auter.hcorp:drawable/ic_launcher_background = 0x7f0700a8
co.auter.hcorp:anim/rns_fade_from_bottom = 0x7f010036
co.auter.hcorp:color/material_dynamic_neutral30 = 0x7f050239
co.auter.hcorp:drawable/ic_call_decline_low = 0x7f0700a4
co.auter.hcorp:string/catalyst_debug_connecting = 0x7f100030
co.auter.hcorp:drawable/ic_call_answer_video_low = 0x7f0700a2
co.auter.hcorp:drawable/googleg_standard_color_18 = 0x7f07009d
co.auter.hcorp:drawable/googleg_disabled_color_18 = 0x7f07009c
co.auter.hcorp:color/m3_ref_palette_dynamic_primary95 = 0x7f0500ec
co.auter.hcorp:drawable/material_ic_keyboard_arrow_previous_black_24dp = 0x7f0700c8
co.auter.hcorp:drawable/design_password_eye = 0x7f07009a
co.auter.hcorp:drawable/design_ic_visibility_off = 0x7f070099
co.auter.hcorp:dimen/mtrl_btn_text_btn_icon_padding = 0x7f06026f
co.auter.hcorp:drawable/compat_splash_screen_no_icon_background = 0x7f070096
co.auter.hcorp:drawable/common_google_signin_btn_text_light_focused = 0x7f070092
co.auter.hcorp:drawable/common_google_signin_btn_text_disabled = 0x7f070090
co.auter.hcorp:style/ThemeOverlay.Material3.DayNight.BottomSheetDialog = 0x7f1102cc
co.auter.hcorp:drawable/abc_textfield_default_mtrl_alpha = 0x7f070072
co.auter.hcorp:drawable/common_google_signin_btn_text_dark_normal = 0x7f07008e
co.auter.hcorp:dimen/m3_comp_search_bar_container_height = 0x7f060173
co.auter.hcorp:drawable/common_google_signin_btn_icon_light = 0x7f070088
co.auter.hcorp:macro/m3_comp_top_app_bar_small_headline_type = 0x7f0c0171
co.auter.hcorp:attr/voiceIcon = 0x7f0304a8
co.auter.hcorp:dimen/abc_action_bar_overflow_padding_end_material = 0x7f060007
co.auter.hcorp:dimen/m3_comp_sheet_bottom_docked_standard_container_elevation = 0x7f060180
co.auter.hcorp:drawable/common_google_signin_btn_icon_dark_normal_background = 0x7f070086
co.auter.hcorp:drawable/common_google_signin_btn_icon_dark = 0x7f070083
co.auter.hcorp:color/m3_timepicker_secondary_text_button_ripple_color = 0x7f050222
co.auter.hcorp:drawable/common_full_open_on_phone = 0x7f070082
co.auter.hcorp:color/mtrl_btn_ripple_color = 0x7f0502cb
co.auter.hcorp:drawable/btn_radio_on_to_off_mtrl_animation = 0x7f070081
co.auter.hcorp:style/Base.Widget.AppCompat.ListPopupWindow = 0x7f1100ec
co.auter.hcorp:drawable/btn_radio_on_mtrl = 0x7f070080
co.auter.hcorp:drawable/btn_checkbox_unchecked_to_checked_mtrl_animation = 0x7f07007d
co.auter.hcorp:style/Widget.MaterialComponents.Chip.Entry = 0x7f110449
co.auter.hcorp:drawable/btn_checkbox_checked_mtrl = 0x7f07007a
co.auter.hcorp:drawable/avd_hide_password = 0x7f070078
co.auter.hcorp:layout/mtrl_picker_text_input_date = 0x7f0b0063
co.auter.hcorp:layout/custom_dialog = 0x7f0b0020
co.auter.hcorp:drawable/abc_vector_test = 0x7f070076
co.auter.hcorp:dimen/m3_comp_fab_primary_hover_state_layer_opacity = 0x7f06011c
co.auter.hcorp:drawable/abc_textfield_search_material = 0x7f070075
co.auter.hcorp:style/ShapeAppearance.Material3.Corner.Medium = 0x7f110181
co.auter.hcorp:id/search_button = 0x7f08018c
co.auter.hcorp:attr/dragThreshold = 0x7f03017a
co.auter.hcorp:color/m3_ref_palette_neutral92 = 0x7f050127
co.auter.hcorp:drawable/abc_textfield_search_activated_mtrl_alpha = 0x7f070073
co.auter.hcorp:drawable/abc_text_select_handle_middle_mtrl = 0x7f07006f
co.auter.hcorp:drawable/$mtrl_switch_thumb_unchecked_checked__0 = 0x7f070025
co.auter.hcorp:drawable/abc_text_cursor_material = 0x7f07006d
co.auter.hcorp:attr/animateMenuItems = 0x7f030032
co.auter.hcorp:dimen/highlight_alpha_material_light = 0x7f060098
co.auter.hcorp:attr/shapeAppearanceLargeComponent = 0x7f0303a9
co.auter.hcorp:drawable/abc_switch_thumb_material = 0x7f070069
co.auter.hcorp:drawable/abc_star_half_black_48dp = 0x7f070068
co.auter.hcorp:drawable/abc_star_black_48dp = 0x7f070067
co.auter.hcorp:style/ShapeAppearance.M3.Comp.Switch.Track.Shape = 0x7f110174
co.auter.hcorp:style/Base.Theme.Material3.Light.DialogWhenLarge = 0x7f110066
co.auter.hcorp:drawable/abc_spinner_textfield_background_material = 0x7f070066
co.auter.hcorp:drawable/abc_seekbar_track_material = 0x7f070064
co.auter.hcorp:drawable/abc_seekbar_thumb_material = 0x7f070062
co.auter.hcorp:drawable/abc_scrubber_control_to_pressed_mtrl_000 = 0x7f07005e
co.auter.hcorp:drawable/abc_scrubber_control_off_mtrl_alpha = 0x7f07005d
co.auter.hcorp:style/Theme.PlayCore.Transparent = 0x7f1102a4
co.auter.hcorp:id/fixed = 0x7f0800c9
co.auter.hcorp:attr/colorPrimaryVariant = 0x7f03010f
co.auter.hcorp:attr/shapeAppearanceCornerLarge = 0x7f0303a6
co.auter.hcorp:dimen/m3_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f0600c9
co.auter.hcorp:drawable/abc_ratingbar_small_material = 0x7f07005c
co.auter.hcorp:macro/m3_comp_time_picker_period_selector_unselected_focus_state_layer_color = 0x7f0c015a
co.auter.hcorp:attr/navigationMode = 0x7f030332
co.auter.hcorp:drawable/common_google_signin_btn_text_light_normal_background = 0x7f070094
co.auter.hcorp:drawable/abc_ratingbar_material = 0x7f07005b
co.auter.hcorp:style/TextAppearance.Widget.AppCompat.Toolbar.Subtitle = 0x7f11021f
co.auter.hcorp:macro/m3_comp_extended_fab_primary_icon_color = 0x7f0c002e
co.auter.hcorp:drawable/abc_list_selector_disabled_holo_light = 0x7f070055
co.auter.hcorp:dimen/mtrl_calendar_action_confirm_button_min_width = 0x7f060274
co.auter.hcorp:drawable/mtrl_bottomsheet_drag_handle = 0x7f0700cc
co.auter.hcorp:color/secondary_text_disabled_material_light = 0x7f050312
co.auter.hcorp:drawable/abc_list_selector_disabled_holo_dark = 0x7f070054
co.auter.hcorp:drawable/abc_list_focused_holo = 0x7f07004e
co.auter.hcorp:drawable/abc_item_background_holo_dark = 0x7f07004a
co.auter.hcorp:drawable/abc_ic_voice_search_api_material = 0x7f070049
co.auter.hcorp:drawable/abc_ic_menu_overflow_material = 0x7f070044
co.auter.hcorp:style/Base.Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f110074
co.auter.hcorp:drawable/abc_ic_menu_cut_mtrl_alpha = 0x7f070043
co.auter.hcorp:drawable/abc_ic_menu_copy_mtrl_am_alpha = 0x7f070042
co.auter.hcorp:drawable/abc_ic_arrow_drop_right_black_24dp = 0x7f07003e
co.auter.hcorp:drawable/abc_dialog_material_background = 0x7f07003b
co.auter.hcorp:drawable/abc_btn_switch_to_on_mtrl_00001 = 0x7f070035
co.auter.hcorp:drawable/abc_btn_radio_to_on_mtrl_015 = 0x7f070034
co.auter.hcorp:drawable/abc_btn_radio_to_on_mtrl_000 = 0x7f070033
co.auter.hcorp:attr/textAppearanceSearchResultSubtitle = 0x7f03043a
co.auter.hcorp:drawable/abc_btn_radio_material = 0x7f070031
co.auter.hcorp:drawable/abc_btn_colored_material = 0x7f07002f
co.auter.hcorp:dimen/mtrl_calendar_selection_text_baseline_to_bottom_fullscreen = 0x7f060294
co.auter.hcorp:attr/showDelay = 0x7f0303b2
co.auter.hcorp:drawable/abc_action_bar_item_background_material = 0x7f070029
co.auter.hcorp:id/accessibility_custom_action_21 = 0x7f080021
co.auter.hcorp:drawable/$mtrl_switch_thumb_unchecked_checked__1 = 0x7f070026
co.auter.hcorp:dimen/design_snackbar_padding_horizontal = 0x7f060088
co.auter.hcorp:drawable/$mtrl_switch_thumb_pressed_unchecked__0 = 0x7f070024
co.auter.hcorp:drawable/$mtrl_switch_thumb_checked_unchecked__0 = 0x7f070021
co.auter.hcorp:dimen/tooltip_horizontal_padding = 0x7f06032a
co.auter.hcorp:drawable/$mtrl_checkbox_button_icon_unchecked_checked__2 = 0x7f070019
co.auter.hcorp:string/common_google_play_services_enable_text = 0x7f100050
co.auter.hcorp:drawable/$mtrl_checkbox_button_icon_unchecked_checked__1 = 0x7f070018
co.auter.hcorp:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__0 = 0x7f070014
co.auter.hcorp:xml/file_provider_paths = 0x7f130000
co.auter.hcorp:dimen/m3_comp_time_picker_period_selector_outline_width = 0x7f0601a6
co.auter.hcorp:drawable/$mtrl_checkbox_button_icon_indeterminate_checked__0 = 0x7f070013
co.auter.hcorp:drawable/$mtrl_checkbox_button_icon_checked_unchecked__2 = 0x7f070012
co.auter.hcorp:drawable/$mtrl_checkbox_button_icon_checked_unchecked__1 = 0x7f070011
co.auter.hcorp:drawable/$mtrl_checkbox_button_icon_checked_unchecked__0 = 0x7f070010
co.auter.hcorp:drawable/$mtrl_checkbox_button_checked_unchecked__1 = 0x7f07000d
co.auter.hcorp:drawable/$mtrl_checkbox_button_checked_unchecked__0 = 0x7f07000c
co.auter.hcorp:drawable/$m3_avd_show_password__2 = 0x7f07000b
co.auter.hcorp:drawable/$m3_avd_show_password__1 = 0x7f07000a
co.auter.hcorp:drawable/$avd_show_password__1 = 0x7f070004
co.auter.hcorp:attr/roundPercent = 0x7f03038b
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral12 = 0x7f0500b4
co.auter.hcorp:drawable/$avd_hide_password__2 = 0x7f070002
co.auter.hcorp:animator/fragment_open_exit = 0x7f020008
co.auter.hcorp:drawable/$avd_hide_password__1 = 0x7f070001
co.auter.hcorp:dimen/tooltip_y_offset_touch = 0x7f060330
co.auter.hcorp:dimen/tooltip_vertical_padding = 0x7f06032e
co.auter.hcorp:string/abc_toolbar_collapse_description = 0x7f10001a
co.auter.hcorp:macro/m3_comp_outlined_text_field_error_trailing_icon_color = 0x7f0c00b8
co.auter.hcorp:layout/design_layout_tab_icon = 0x7f0b0025
co.auter.hcorp:id/home = 0x7f0800dc
co.auter.hcorp:dimen/material_bottom_sheet_max_width = 0x7f060224
co.auter.hcorp:dimen/tooltip_margin = 0x7f06032b
co.auter.hcorp:dimen/tooltip_corner_radius = 0x7f060329
co.auter.hcorp:style/ThemeOverlay.Material3.AutoCompleteTextView.OutlinedBox = 0x7f1102ba
co.auter.hcorp:style/Theme.EdgeToEdge.Material2.Light = 0x7f110249
co.auter.hcorp:drawable/ic_mtrl_checked_circle = 0x7f0700ac
co.auter.hcorp:attr/horizontalOffset = 0x7f03021a
co.auter.hcorp:dimen/subtitle_corner_radius = 0x7f060325
co.auter.hcorp:integer/m3_sys_shape_corner_small_corner_family = 0x7f090027
co.auter.hcorp:color/m3_ref_palette_dynamic_tertiary95 = 0x7f050106
co.auter.hcorp:dimen/splashscreen_icon_size_with_background = 0x7f060324
co.auter.hcorp:id/filled = 0x7f0800c1
co.auter.hcorp:dimen/splashscreen_icon_size_no_background = 0x7f060323
co.auter.hcorp:macro/m3_comp_navigation_bar_active_hover_label_text_color = 0x7f0c0063
co.auter.hcorp:dimen/splashscreen_icon_mask_stroke_no_background = 0x7f060320
co.auter.hcorp:attr/chainUseRtl = 0x7f0300a8
co.auter.hcorp:dimen/splashscreen_icon_mask_size_with_background = 0x7f06031f
co.auter.hcorp:string/m3_sys_motion_easing_legacy = 0x7f100084
co.auter.hcorp:dimen/m3_navigation_menu_headline_horizontal_padding = 0x7f0601cb
co.auter.hcorp:dimen/splashscreen_icon_mask_size_no_background = 0x7f06031e
co.auter.hcorp:style/RtlOverlay.Widget.AppCompat.DialogTitle.Icon = 0x7f110155
co.auter.hcorp:dimen/notification_top_pad_large_text = 0x7f06031d
co.auter.hcorp:color/m3_card_ripple_color = 0x7f05007f
co.auter.hcorp:color/abc_tint_default = 0x7f050014
co.auter.hcorp:attr/activeIndicatorLabelPadding = 0x7f030024
co.auter.hcorp:dimen/notification_small_icon_background_padding = 0x7f060319
co.auter.hcorp:styleable/CustomAttribute = 0x7f12002e
co.auter.hcorp:string/abc_search_hint = 0x7f100012
co.auter.hcorp:dimen/notification_right_side_padding_top = 0x7f060318
co.auter.hcorp:styleable/BottomSheetBehavior_Layout = 0x7f120019
co.auter.hcorp:dimen/notification_right_icon_size = 0x7f060317
co.auter.hcorp:dimen/mtrl_btn_padding_top = 0x7f06026b
co.auter.hcorp:dimen/notification_content_margin_start = 0x7f060312
co.auter.hcorp:dimen/m3_comp_text_button_hover_state_layer_opacity = 0x7f0601a0
co.auter.hcorp:dimen/notification_big_circle_margin = 0x7f060311
co.auter.hcorp:dimen/notification_action_text_size = 0x7f060310
co.auter.hcorp:dimen/notification_action_icon_size = 0x7f06030f
co.auter.hcorp:id/visible = 0x7f0801fa
co.auter.hcorp:dimen/mtrl_toolbar_default_height = 0x7f060308
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral_variant94 = 0x7f0500dc
co.auter.hcorp:dimen/mtrl_textinput_start_icon_margin_end = 0x7f060307
co.auter.hcorp:id/navigation_header_container = 0x7f08013e
co.auter.hcorp:dimen/mtrl_textinput_outline_box_expanded_padding = 0x7f060306
co.auter.hcorp:dimen/mtrl_textinput_counter_margin_start = 0x7f060304
co.auter.hcorp:id/easeIn = 0x7f0800ac
co.auter.hcorp:dimen/mtrl_textinput_box_stroke_width_focused = 0x7f060303
co.auter.hcorp:id/action_text = 0x7f08004b
co.auter.hcorp:dimen/mtrl_textinput_box_label_cutout_padding = 0x7f060301
co.auter.hcorp:macro/m3_comp_navigation_bar_active_focus_state_layer_color = 0x7f0c0061
co.auter.hcorp:dimen/mtrl_switch_track_width = 0x7f0602fe
co.auter.hcorp:drawable/ic_other_sign_in = 0x7f0700b0
co.auter.hcorp:color/m3_highlighted_text = 0x7f05009f
co.auter.hcorp:color/m3_sys_color_dynamic_light_on_error = 0x7f0501b8
co.auter.hcorp:dimen/mtrl_switch_thumb_icon_size = 0x7f0602fb
co.auter.hcorp:dimen/mtrl_switch_text_padding = 0x7f0602f9
co.auter.hcorp:dimen/mtrl_snackbar_background_corner_radius = 0x7f0602f4
co.auter.hcorp:dimen/mtrl_btn_stroke_size = 0x7f06026e
co.auter.hcorp:dimen/mtrl_slider_widget_height = 0x7f0602f2
co.auter.hcorp:dimen/mtrl_slider_track_side_padding = 0x7f0602f1
co.auter.hcorp:dimen/mtrl_slider_tick_radius = 0x7f0602ef
co.auter.hcorp:dimen/mtrl_slider_tick_min_spacing = 0x7f0602ee
co.auter.hcorp:attr/showPaths = 0x7f0303b6
co.auter.hcorp:dimen/mtrl_slider_thumb_elevation = 0x7f0602ec
co.auter.hcorp:bool/abc_config_actionMenuItemAllCaps = 0x7f040001
co.auter.hcorp:dimen/mtrl_slider_label_padding = 0x7f0602e9
co.auter.hcorp:dimen/mtrl_shape_corner_size_large_component = 0x7f0602e5
co.auter.hcorp:dimen/mtrl_progress_track_thickness = 0x7f0602e4
co.auter.hcorp:dimen/mtrl_progress_circular_track_thickness_small = 0x7f0602e2
co.auter.hcorp:attr/subtitleCentered = 0x7f0303ed
co.auter.hcorp:drawable/abc_ic_commit_search_api_mtrl_alpha = 0x7f070040
co.auter.hcorp:dimen/mtrl_progress_circular_track_thickness_medium = 0x7f0602e1
co.auter.hcorp:dimen/mtrl_progress_circular_size_small = 0x7f0602df
co.auter.hcorp:dimen/mtrl_switch_thumb_size = 0x7f0602fc
co.auter.hcorp:dimen/mtrl_progress_circular_size_medium = 0x7f0602de
co.auter.hcorp:dimen/mtrl_progress_circular_size_extra_small = 0x7f0602dd
co.auter.hcorp:macro/m3_comp_divider_color = 0x7f0c0028
co.auter.hcorp:dimen/splashscreen_icon_mask_stroke_with_background = 0x7f060321
co.auter.hcorp:macro/m3_comp_outlined_text_field_focus_label_text_color = 0x7f0c00ba
co.auter.hcorp:dimen/mtrl_progress_circular_radius = 0x7f0602db
co.auter.hcorp:dimen/mtrl_progress_circular_inset_small = 0x7f0602da
co.auter.hcorp:dimen/mtrl_navigation_rail_margin = 0x7f0602d4
co.auter.hcorp:id/buttonPanel = 0x7f080070
co.auter.hcorp:dimen/m3_comp_outlined_card_icon_size = 0x7f060155
co.auter.hcorp:dimen/mtrl_navigation_rail_icon_margin = 0x7f0602d2
co.auter.hcorp:dimen/mtrl_navigation_rail_elevation = 0x7f0602d1
co.auter.hcorp:style/Widget.Material3.MaterialTimePicker.Display.Divider = 0x7f1103f2
co.auter.hcorp:dimen/mtrl_navigation_elevation = 0x7f0602c8
co.auter.hcorp:dimen/mtrl_tooltip_minHeight = 0x7f06030b
co.auter.hcorp:dimen/mtrl_navigation_item_horizontal_padding = 0x7f0602c9
co.auter.hcorp:dimen/mtrl_min_touch_target_size = 0x7f0602c5
co.auter.hcorp:dimen/mtrl_low_ripple_pressed_alpha = 0x7f0602c4
co.auter.hcorp:dimen/mtrl_low_ripple_focused_alpha = 0x7f0602c2
co.auter.hcorp:dimen/mtrl_high_ripple_pressed_alpha = 0x7f0602c0
co.auter.hcorp:style/Theme.FullScreenDialogAnimatedSlide = 0x7f110255
co.auter.hcorp:drawable/mtrl_ic_arrow_drop_down = 0x7f0700d9
co.auter.hcorp:dimen/mtrl_high_ripple_hovered_alpha = 0x7f0602bf
co.auter.hcorp:dimen/mtrl_fab_translation_z_pressed = 0x7f0602bc
co.auter.hcorp:dimen/m3_sys_motion_easing_standard_control_x2 = 0x7f060216
co.auter.hcorp:drawable/indeterminate_static = 0x7f0700b6
co.auter.hcorp:drawable/mtrl_checkbox_button_checked_unchecked = 0x7f0700ce
co.auter.hcorp:dimen/mtrl_fab_translation_z_hovered_focused = 0x7f0602bb
co.auter.hcorp:style/Base.TextAppearance.AppCompat.Light.Widget.PopupMenu.Large = 0x7f110026
co.auter.hcorp:id/clip_horizontal = 0x7f080081
co.auter.hcorp:attr/paddingTopSystemWindowInsets = 0x7f03034c
co.auter.hcorp:dimen/mtrl_fab_min_touch_target = 0x7f0602ba
co.auter.hcorp:dimen/mtrl_fab_elevation = 0x7f0602b9
co.auter.hcorp:drawable/$mtrl_checkbox_button_checked_unchecked__2 = 0x7f07000e
co.auter.hcorp:dimen/mtrl_extended_fab_top_padding = 0x7f0602b5
co.auter.hcorp:dimen/mtrl_extended_fab_start_padding_icon = 0x7f0602b4
co.auter.hcorp:style/Theme.AppCompat.DialogWhenLarge = 0x7f11022f
co.auter.hcorp:macro/m3_comp_switch_unselected_pressed_icon_color = 0x7f0c013c
co.auter.hcorp:color/m3_icon_button_icon_color_selector = 0x7f0500a1
co.auter.hcorp:dimen/mtrl_extended_fab_min_width = 0x7f0602b2
co.auter.hcorp:style/Base.V7.ThemeOverlay.AppCompat.Dialog = 0x7f1100c4
co.auter.hcorp:id/dragDown = 0x7f0800a5
co.auter.hcorp:color/m3_sys_color_dynamic_light_surface = 0x7f0501c8
co.auter.hcorp:dimen/mtrl_extended_fab_icon_text_spacing = 0x7f0602b0
co.auter.hcorp:attr/drawableStartCompat = 0x7f030181
co.auter.hcorp:drawable/abc_ic_ab_back_material = 0x7f07003d
co.auter.hcorp:style/Theme.MaterialComponents.NoActionBar = 0x7f1102a2
co.auter.hcorp:attr/colorSurfaceInverse = 0x7f03011e
co.auter.hcorp:color/material_slider_inactive_tick_marks_color = 0x7f0502c2
co.auter.hcorp:dimen/mtrl_extended_fab_icon_size = 0x7f0602af
co.auter.hcorp:style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox.Dense = 0x7f110314
co.auter.hcorp:attr/tabUnboundedRipple = 0x7f030417
co.auter.hcorp:dimen/mtrl_extended_fab_end_padding_icon = 0x7f0602ae
co.auter.hcorp:dimen/mtrl_extended_fab_end_padding = 0x7f0602ad
co.auter.hcorp:dimen/mtrl_extended_fab_disabled_translation_z = 0x7f0602ab
co.auter.hcorp:dimen/mtrl_extended_fab_bottom_padding = 0x7f0602a9
co.auter.hcorp:style/Widget.AppCompat.Light.ActionBar.TabView = 0x7f11033e
co.auter.hcorp:integer/mtrl_calendar_year_selector_span = 0x7f090034
co.auter.hcorp:color/m3_sys_color_dynamic_dark_surface_container_highest = 0x7f0501aa
co.auter.hcorp:dimen/mtrl_exposed_dropdown_menu_popup_vertical_offset = 0x7f0602a7
co.auter.hcorp:color/m3_button_background_color_selector = 0x7f050077
co.auter.hcorp:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__2 = 0x7f07001c
co.auter.hcorp:attr/sliderStyle = 0x7f0303c4
co.auter.hcorp:dimen/m3_badge_with_text_vertical_offset = 0x7f0600bd
co.auter.hcorp:dimen/mtrl_card_spacing = 0x7f0602a3
co.auter.hcorp:string/mtrl_picker_announce_current_selection = 0x7f1000b6
co.auter.hcorp:id/dragRight = 0x7f0800a8
co.auter.hcorp:dimen/mtrl_card_checked_icon_size = 0x7f06029f
co.auter.hcorp:dimen/mtrl_calendar_year_width = 0x7f06029d
co.auter.hcorp:dimen/m3_searchbar_padding_start = 0x7f0601e2
co.auter.hcorp:dimen/mtrl_calendar_year_vertical_padding = 0x7f06029c
co.auter.hcorp:anim/btn_radio_to_off_mtrl_dot_group_animation = 0x7f010012
co.auter.hcorp:dimen/mtrl_calendar_year_horizontal_padding = 0x7f06029b
co.auter.hcorp:style/Theme.MaterialComponents.Bridge = 0x7f110277
co.auter.hcorp:attr/colorContainer = 0x7f0300eb
co.auter.hcorp:dimen/mtrl_calendar_title_baseline_to_top_fullscreen = 0x7f060298
co.auter.hcorp:dimen/mtrl_calendar_title_baseline_to_top = 0x7f060297
co.auter.hcorp:attr/motionEasingStandardInterpolator = 0x7f030324
co.auter.hcorp:dimen/mtrl_calendar_selection_text_baseline_to_top = 0x7f060295
co.auter.hcorp:dimen/mtrl_calendar_selection_text_baseline_to_bottom = 0x7f060293
co.auter.hcorp:style/Widget.Material3.Button.ElevatedButton = 0x7f11038c
co.auter.hcorp:string/m3_sys_motion_easing_linear = 0x7f100087
co.auter.hcorp:dimen/mtrl_calendar_pre_l_text_clip_padding = 0x7f060291
co.auter.hcorp:color/m3_sys_color_primary_fixed = 0x7f050207
co.auter.hcorp:dimen/design_bottom_sheet_modal_elevation = 0x7f06006f
co.auter.hcorp:dimen/mtrl_calendar_header_toggle_margin_top = 0x7f060289
co.auter.hcorp:attr/materialCalendarHeaderSelection = 0x7f0302d0
co.auter.hcorp:anim/abc_popup_exit = 0x7f010004
co.auter.hcorp:dimen/mtrl_calendar_header_toggle_margin_bottom = 0x7f060288
co.auter.hcorp:dimen/mtrl_calendar_header_text_padding = 0x7f060287
co.auter.hcorp:dimen/m3_comp_switch_track_width = 0x7f06019b
co.auter.hcorp:dimen/mtrl_calendar_header_divider_thickness = 0x7f060283
co.auter.hcorp:dimen/mtrl_calendar_header_content_padding_fullscreen = 0x7f060282
co.auter.hcorp:drawable/abc_btn_check_material = 0x7f07002b
co.auter.hcorp:dimen/mtrl_calendar_dialog_background_inset = 0x7f060280
co.auter.hcorp:styleable/NavigationRailView = 0x7f12006b
co.auter.hcorp:dimen/mtrl_calendar_day_width = 0x7f06027e
co.auter.hcorp:dimen/m3_card_elevated_dragged_z = 0x7f0600ea
co.auter.hcorp:drawable/ic_call_answer_video = 0x7f0700a1
co.auter.hcorp:dimen/mtrl_calendar_day_today_stroke = 0x7f06027c
co.auter.hcorp:attr/listPreferredItemHeight = 0x7f0302b1
co.auter.hcorp:dimen/mtrl_calendar_day_horizontal_padding = 0x7f06027b
co.auter.hcorp:drawable/ic_call_decline = 0x7f0700a3
co.auter.hcorp:color/m3_switch_track_tint = 0x7f05016c
co.auter.hcorp:dimen/mtrl_calendar_day_height = 0x7f06027a
co.auter.hcorp:drawable/mtrl_switch_thumb_unchecked = 0x7f0700eb
co.auter.hcorp:dimen/design_snackbar_background_corner_radius = 0x7f060083
co.auter.hcorp:dimen/mtrl_calendar_day_corner = 0x7f060279
co.auter.hcorp:attr/clockNumberTextColor = 0x7f0300d3
co.auter.hcorp:drawable/common_google_signin_btn_icon_light_normal = 0x7f07008a
co.auter.hcorp:style/Widget.AppCompat.CompoundButton.Switch = 0x7f110332
co.auter.hcorp:dimen/m3_small_fab_size = 0x7f0601f0
co.auter.hcorp:dimen/mtrl_textinput_end_icon_margin_start = 0x7f060305
co.auter.hcorp:style/Base.Widget.Material3.Snackbar = 0x7f110113
co.auter.hcorp:dimen/m3_carousel_debug_keyline_width = 0x7f0600f0
co.auter.hcorp:dimen/abc_button_inset_horizontal_material = 0x7f060012
co.auter.hcorp:dimen/mtrl_calendar_action_height = 0x7f060275
co.auter.hcorp:attr/animationMode = 0x7f030035
co.auter.hcorp:dimen/mtrl_btn_text_size = 0x7f060272
co.auter.hcorp:style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu = 0x7f11048c
co.auter.hcorp:dimen/mtrl_btn_snackbar_margin_horizontal = 0x7f06026d
co.auter.hcorp:dimen/mtrl_bottomappbar_fab_bottom_margin = 0x7f060257
co.auter.hcorp:dimen/mtrl_btn_pressed_z = 0x7f06026c
co.auter.hcorp:drawable/abc_list_selector_background_transition_holo_dark = 0x7f070052
co.auter.hcorp:style/TextAppearance.M3.Sys.Typescale.HeadlineSmall = 0x7f1101f1
co.auter.hcorp:id/dragStart = 0x7f0800a9
co.auter.hcorp:attr/dropDownListViewStyle = 0x7f030189
co.auter.hcorp:dimen/mtrl_btn_padding_right = 0x7f06026a
co.auter.hcorp:macro/m3_comp_radio_button_selected_hover_state_layer_color = 0x7f0c00db
co.auter.hcorp:dimen/m3_back_progress_side_container_max_scale_x_distance_grow = 0x7f0600b3
co.auter.hcorp:dimen/mtrl_btn_letter_spacing = 0x7f060266
co.auter.hcorp:dimen/mtrl_btn_inset = 0x7f060265
co.auter.hcorp:id/transition_image_transform = 0x7f0801e5
co.auter.hcorp:dimen/notification_small_icon_size_as_large = 0x7f06031a
co.auter.hcorp:dimen/mtrl_btn_disabled_z = 0x7f06025f
co.auter.hcorp:dimen/m3_comp_search_view_docked_header_container_height = 0x7f060177
co.auter.hcorp:drawable/$mtrl_checkbox_button_icon_checked_indeterminate__0 = 0x7f07000f
co.auter.hcorp:dimen/mtrl_btn_dialog_btn_min_width = 0x7f06025d
co.auter.hcorp:dimen/mtrl_bottomappbar_height = 0x7f06025b
co.auter.hcorp:dimen/mtrl_bottomappbar_fab_cradle_vertical_offset = 0x7f06025a
co.auter.hcorp:dimen/mtrl_bottomappbar_fab_cradle_margin = 0x7f060258
co.auter.hcorp:dimen/mtrl_bottomappbar_fabOffsetEndMode = 0x7f060256
co.auter.hcorp:id/view_clipped = 0x7f0801f2
co.auter.hcorp:attr/layout_constraintVertical_bias = 0x7f03028c
co.auter.hcorp:dimen/mtrl_badge_toolbar_action_menu_item_horizontal_offset = 0x7f060253
co.auter.hcorp:style/Widget.MaterialComponents.Button.OutlinedButton.Icon = 0x7f11043c
co.auter.hcorp:macro/m3_comp_outlined_text_field_error_supporting_text_color = 0x7f0c00b7
co.auter.hcorp:dimen/mtrl_badge_text_horizontal_edge_offset = 0x7f060251
co.auter.hcorp:attr/ensureMinTouchTargetSize = 0x7f0301a2
co.auter.hcorp:dimen/mtrl_badge_long_text_horizontal_padding = 0x7f06024f
co.auter.hcorp:attr/behavior_draggable = 0x7f03006e
co.auter.hcorp:dimen/mtrl_badge_horizontal_edge_offset = 0x7f06024e
co.auter.hcorp:dimen/mtrl_alert_dialog_background_inset_top = 0x7f06024c
co.auter.hcorp:color/m3_elevated_chip_background_color = 0x7f05009a
co.auter.hcorp:dimen/mtrl_calendar_month_vertical_padding = 0x7f06028d
co.auter.hcorp:dimen/mtrl_alert_dialog_background_inset_start = 0x7f06024b
co.auter.hcorp:dimen/mtrl_alert_dialog_background_inset_end = 0x7f06024a
co.auter.hcorp:dimen/mtrl_alert_dialog_background_inset_bottom = 0x7f060249
co.auter.hcorp:drawable/ic_passkey = 0x7f0700b1
co.auter.hcorp:styleable/DrawerLayout = 0x7f120030
co.auter.hcorp:dimen/material_time_picker_minimum_screen_width = 0x7f060248
co.auter.hcorp:style/Widget.AppCompat.Light.ActionButton.CloseMode = 0x7f110341
co.auter.hcorp:id/month_navigation_bar = 0x7f08011a
co.auter.hcorp:attr/animateNavigationIcon = 0x7f030033
co.auter.hcorp:color/mtrl_navigation_item_text_color = 0x7f0502eb
co.auter.hcorp:dimen/material_time_picker_minimum_screen_height = 0x7f060247
co.auter.hcorp:dimen/material_font_1_3_box_collapsed_padding_top = 0x7f06023e
co.auter.hcorp:dimen/material_textinput_default_width = 0x7f060244
co.auter.hcorp:color/m3_navigation_item_ripple_color = 0x7f0500a7
co.auter.hcorp:dimen/material_input_text_to_prefix_suffix_padding = 0x7f060243
co.auter.hcorp:dimen/material_filled_edittext_font_2_0_padding_top = 0x7f06023d
co.auter.hcorp:color/mtrl_scrim_color = 0x7f0502f1
co.auter.hcorp:dimen/material_filled_edittext_font_1_3_padding_top = 0x7f06023b
co.auter.hcorp:dimen/material_filled_edittext_font_1_3_padding_bottom = 0x7f06023a
co.auter.hcorp:attr/buttonIconTintMode = 0x7f030097
co.auter.hcorp:dimen/material_emphasis_high_type = 0x7f060238
co.auter.hcorp:attr/contentInsetLeft = 0x7f030130
co.auter.hcorp:dimen/material_emphasis_disabled_background = 0x7f060237
co.auter.hcorp:style/ShapeAppearanceOverlay.MaterialAlertDialog.Material3 = 0x7f110199
co.auter.hcorp:dimen/material_emphasis_disabled = 0x7f060236
co.auter.hcorp:dimen/m3_searchbar_height = 0x7f0601de
co.auter.hcorp:dimen/material_clock_period_toggle_vertical_gap = 0x7f060230
co.auter.hcorp:styleable/Transform = 0x7f120093
co.auter.hcorp:macro/m3_comp_outlined_button_pressed_outline_color = 0x7f0c00a7
co.auter.hcorp:dimen/material_clock_number_text_size = 0x7f06022d
co.auter.hcorp:style/Base.Theme.AppCompat.Light.DialogWhenLarge = 0x7f11005b
co.auter.hcorp:dimen/material_clock_hand_stroke_width = 0x7f06022c
co.auter.hcorp:attr/autoSizeMaxTextSize = 0x7f03003f
co.auter.hcorp:attr/cursorErrorColor = 0x7f030155
co.auter.hcorp:dimen/material_clock_hand_padding = 0x7f06022b
co.auter.hcorp:style/Base.Widget.MaterialComponents.MaterialCalendar.NavigationButton = 0x7f11011b
co.auter.hcorp:dimen/material_clock_hand_center_dot_radius = 0x7f06022a
co.auter.hcorp:id/matrix = 0x7f080113
co.auter.hcorp:dimen/material_clock_face_margin_top = 0x7f060229
co.auter.hcorp:dimen/material_clock_face_margin_bottom = 0x7f060228
co.auter.hcorp:dimen/material_clock_period_toggle_horizontal_gap = 0x7f06022f
co.auter.hcorp:color/m3_sys_color_dark_outline = 0x7f05017e
co.auter.hcorp:dimen/m3_timepicker_display_stroke_width = 0x7f060221
co.auter.hcorp:dimen/m3_comp_slider_inactive_track_height = 0x7f06018a
co.auter.hcorp:dimen/m3_sys_state_pressed_state_layer_opacity = 0x7f060220
co.auter.hcorp:drawable/design_ic_visibility = 0x7f070098
co.auter.hcorp:dimen/m3_sys_state_hover_state_layer_opacity = 0x7f06021f
co.auter.hcorp:dimen/m3_searchbar_margin_vertical = 0x7f0601e0
co.auter.hcorp:dimen/m3_sys_state_dragged_state_layer_opacity = 0x7f06021d
co.auter.hcorp:attr/alertDialogButtonGroupStyle = 0x7f03002a
co.auter.hcorp:dimen/mtrl_slider_label_radius = 0x7f0602ea
co.auter.hcorp:color/mtrl_navigation_item_background_color = 0x7f0502e9
co.auter.hcorp:dimen/m3_sys_motion_easing_standard_decelerate_control_x2 = 0x7f06021a
co.auter.hcorp:style/Widget.MaterialComponents.ActionBar.PrimarySurface = 0x7f110425
co.auter.hcorp:dimen/m3_sys_motion_easing_standard_decelerate_control_x1 = 0x7f060219
co.auter.hcorp:color/m3_sys_color_dynamic_dark_on_error_container = 0x7f050197
co.auter.hcorp:dimen/m3_sys_motion_easing_standard_accelerate_control_y1 = 0x7f060213
co.auter.hcorp:style/TextAppearance.AppCompat.Subhead.Inverse = 0x7f1101bd
co.auter.hcorp:attr/expandActivityOverflowButtonDrawable = 0x7f0301ad
co.auter.hcorp:dimen/m3_comp_filled_text_field_disabled_active_indicator_opacity = 0x7f06012d
co.auter.hcorp:dimen/m3_sys_motion_easing_standard_accelerate_control_x2 = 0x7f060212
co.auter.hcorp:attr/tintNavigationIcon = 0x7f03046b
co.auter.hcorp:color/m3_sys_color_dynamic_dark_background = 0x7f05018f
co.auter.hcorp:drawable/material_ic_keyboard_arrow_next_black_24dp = 0x7f0700c7
co.auter.hcorp:drawable/m3_tabs_rounded_line_indicator = 0x7f0700c0
co.auter.hcorp:attr/fabCustomSize = 0x7f0301c7
co.auter.hcorp:dimen/m3_sys_motion_easing_standard_accelerate_control_x1 = 0x7f060211
co.auter.hcorp:dimen/m3_sys_motion_easing_linear_control_x1 = 0x7f06020d
co.auter.hcorp:dimen/m3_sys_motion_easing_legacy_decelerate_control_y1 = 0x7f06020b
co.auter.hcorp:color/m3_sys_color_dynamic_light_on_background = 0x7f0501b7
co.auter.hcorp:dimen/abc_action_button_min_width_material = 0x7f06000e
co.auter.hcorp:dimen/m3_sys_motion_easing_legacy_decelerate_control_x1 = 0x7f060209
co.auter.hcorp:id/accessibility_custom_action_12 = 0x7f080017
co.auter.hcorp:dimen/m3_sys_motion_easing_legacy_accelerate_control_y2 = 0x7f060204
co.auter.hcorp:dimen/m3_sys_motion_easing_legacy_accelerate_control_y1 = 0x7f060203
co.auter.hcorp:dimen/m3_comp_radio_button_disabled_unselected_icon_opacity = 0x7f060169
co.auter.hcorp:attr/roundBottomEnd = 0x7f030387
co.auter.hcorp:attr/textAppearanceLargePopupMenu = 0x7f030433
co.auter.hcorp:dimen/m3_sys_motion_easing_legacy_accelerate_control_x1 = 0x7f060201
co.auter.hcorp:dimen/m3_sys_motion_easing_emphasized_decelerate_control_y1 = 0x7f0601ff
co.auter.hcorp:style/Base.TextAppearance.AppCompat.Large = 0x7f110024
co.auter.hcorp:string/catalyst_reload_error = 0x7f100043
co.auter.hcorp:color/m3_sys_color_dynamic_dark_inverse_surface = 0x7f050194
co.auter.hcorp:dimen/m3_sys_motion_easing_emphasized_accelerate_control_x1 = 0x7f0601f9
co.auter.hcorp:dimen/m3_sys_elevation_level5 = 0x7f0601f8
co.auter.hcorp:dimen/m3_sys_elevation_level4 = 0x7f0601f7
co.auter.hcorp:dimen/m3_sys_elevation_level3 = 0x7f0601f6
co.auter.hcorp:macro/m3_comp_switch_unselected_focus_state_layer_color = 0x7f0c0131
co.auter.hcorp:color/m3_sys_color_light_surface_dim = 0x7f0501fd
co.auter.hcorp:dimen/m3_sys_elevation_level1 = 0x7f0601f4
co.auter.hcorp:id/snackbar_text = 0x7f08019f
co.auter.hcorp:attr/textAppearanceListItemSmall = 0x7f030437
co.auter.hcorp:dimen/m3_snackbar_action_text_color_alpha = 0x7f0601f1
co.auter.hcorp:style/Widget.AppCompat.Light.AutoCompleteTextView = 0x7f110345
co.auter.hcorp:color/m3_ref_palette_neutral17 = 0x7f050119
co.auter.hcorp:drawable/notification_template_icon_low_bg = 0x7f0700fc
co.auter.hcorp:color/material_dynamic_secondary50 = 0x7f050262
co.auter.hcorp:dimen/m3_side_sheet_standard_elevation = 0x7f0601ea
co.auter.hcorp:dimen/m3_side_sheet_modal_elevation = 0x7f0601e9
co.auter.hcorp:macro/m3_comp_switch_selected_handle_color = 0x7f0c0124
co.auter.hcorp:color/m3_ref_palette_neutral20 = 0x7f05011a
co.auter.hcorp:dimen/m3_searchview_height = 0x7f0601e7
co.auter.hcorp:dimen/m3_searchview_elevation = 0x7f0601e6
co.auter.hcorp:dimen/m3_simple_item_color_hovered_alpha = 0x7f0601ec
co.auter.hcorp:dimen/m3_searchbar_margin_horizontal = 0x7f0601df
co.auter.hcorp:color/m3_ref_palette_dynamic_tertiary80 = 0x7f050104
co.auter.hcorp:attr/materialCalendarTheme = 0x7f0302d6
co.auter.hcorp:dimen/m3_comp_top_app_bar_large_container_height = 0x7f0601ab
co.auter.hcorp:dimen/m3_searchbar_elevation = 0x7f0601dd
co.auter.hcorp:dimen/m3_ripple_selectable_pressed_alpha = 0x7f0601dc
co.auter.hcorp:dimen/m3_ripple_focused_alpha = 0x7f0601d9
co.auter.hcorp:style/ThemeOverlay.Material3.TextInputEditText.OutlinedBox.Dense = 0x7f1102f2
co.auter.hcorp:dimen/m3_navigation_rail_label_padding_horizontal = 0x7f0601d7
co.auter.hcorp:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__2 = 0x7f070016
co.auter.hcorp:dimen/m3_navigation_rail_item_min_height = 0x7f0601d2
co.auter.hcorp:drawable/abc_menu_hardkey_panel_mtrl_mult = 0x7f070058
co.auter.hcorp:attr/onCross = 0x7f03033b
co.auter.hcorp:dimen/m3_navigation_rail_item_active_indicator_height = 0x7f0601cf
co.auter.hcorp:dimen/mtrl_btn_icon_btn_padding_left = 0x7f060263
co.auter.hcorp:dimen/m3_navigation_rail_icon_size = 0x7f0601ce
co.auter.hcorp:style/ThemeOverlay.MaterialComponents.TextInputEditText.FilledBox = 0x7f110313
co.auter.hcorp:style/Base.V21.Theme.MaterialComponents.Light = 0x7f1100ad
co.auter.hcorp:dimen/m3_navigation_rail_default_width = 0x7f0601cc
co.auter.hcorp:dimen/m3_navigation_item_shape_inset_top = 0x7f0601c8
co.auter.hcorp:dimen/m3_navigation_item_shape_inset_start = 0x7f0601c7
co.auter.hcorp:integer/hide_password_duration = 0x7f09000a
co.auter.hcorp:attr/layout_constraintGuide_percent = 0x7f030278
co.auter.hcorp:dimen/m3_navigation_item_icon_padding = 0x7f0601c4
co.auter.hcorp:dimen/mtrl_btn_corner_radius = 0x7f06025c
co.auter.hcorp:dimen/m3_menu_elevation = 0x7f0601bf
co.auter.hcorp:style/Base.Widget.AppCompat.Light.ActionBar.TabText.Inverse = 0x7f1100e7
co.auter.hcorp:dimen/m3_large_fab_max_image_size = 0x7f0601bc
co.auter.hcorp:dimen/m3_fab_translation_z_pressed = 0x7f0601bb
co.auter.hcorp:dimen/m3_extended_fab_icon_padding = 0x7f0601b4
co.auter.hcorp:dimen/m3_fab_translation_z_hovered_focused = 0x7f0601ba
co.auter.hcorp:dimen/m3_fab_corner_size = 0x7f0601b9
co.auter.hcorp:dimen/m3_extended_fab_top_padding = 0x7f0601b7
co.auter.hcorp:dimen/material_emphasis_medium = 0x7f060239
co.auter.hcorp:attr/tabGravity = 0x7f0303fd
co.auter.hcorp:dimen/m3_extended_fab_end_padding = 0x7f0601b3
co.auter.hcorp:style/Theme.Material3.DayNight.Dialog = 0x7f110261
co.auter.hcorp:macro/m3_comp_secondary_navigation_tab_label_text_type = 0x7f0c0100
co.auter.hcorp:dimen/m3_comp_top_app_bar_small_on_scroll_container_elevation = 0x7f0601af
co.auter.hcorp:layout/abc_dialog_title_material = 0x7f0b000c
co.auter.hcorp:dimen/m3_comp_progress_indicator_stop_indicator_size = 0x7f060166
co.auter.hcorp:dimen/m3_comp_time_input_time_input_field_focus_outline_width = 0x7f0601a2
co.auter.hcorp:id/report_drawn = 0x7f08016e
co.auter.hcorp:id/cancel_button = 0x7f080073
co.auter.hcorp:color/switch_thumb_normal_material_light = 0x7f050319
co.auter.hcorp:dimen/m3_comp_text_button_pressed_state_layer_opacity = 0x7f0601a1
co.auter.hcorp:macro/m3_comp_search_bar_input_text_type = 0x7f0c00ea
co.auter.hcorp:drawable/m3_selection_control_ripple = 0x7f0700bd
co.auter.hcorp:dimen/m3_comp_switch_disabled_track_opacity = 0x7f060194
co.auter.hcorp:integer/m3_sys_motion_duration_extra_long3 = 0x7f090013
co.auter.hcorp:dimen/m3_comp_switch_disabled_selected_icon_opacity = 0x7f060193
co.auter.hcorp:dimen/m3_comp_switch_disabled_selected_handle_opacity = 0x7f060192
co.auter.hcorp:style/ThemeOverlay.Material3.MaterialCalendar = 0x7f1102e2
co.auter.hcorp:dimen/cardview_default_radius = 0x7f060057
co.auter.hcorp:dimen/m3_comp_suggestion_chip_with_leading_icon_leading_icon_size = 0x7f060191
co.auter.hcorp:attr/extendedFloatingActionButtonSecondaryStyle = 0x7f0301bb
co.auter.hcorp:dimen/m3_comp_snackbar_container_elevation = 0x7f06018c
co.auter.hcorp:dimen/mtrl_slider_halo_radius = 0x7f0602e8
co.auter.hcorp:id/dragUp = 0x7f0800aa
co.auter.hcorp:dimen/m3_comp_sheet_side_docked_standard_container_elevation = 0x7f060183
co.auter.hcorp:dimen/m3_comp_slider_disabled_inactive_track_opacity = 0x7f060189
co.auter.hcorp:dimen/m3_comp_slider_disabled_handle_opacity = 0x7f060188
co.auter.hcorp:style/Widget.AppCompat.Light.ActionButton.Overflow = 0x7f110342
co.auter.hcorp:dimen/m3_comp_slider_active_handle_leading_space = 0x7f060185
co.auter.hcorp:dimen/m3_comp_slider_active_handle_height = 0x7f060184
co.auter.hcorp:dimen/m3_ripple_default_alpha = 0x7f0601d8
co.auter.hcorp:dimen/m3_comp_sheet_side_docked_modal_container_elevation = 0x7f060182
co.auter.hcorp:dimen/m3_comp_sheet_bottom_docked_modal_container_elevation = 0x7f06017f
co.auter.hcorp:color/m3_ref_palette_neutral_variant70 = 0x7f050135
co.auter.hcorp:dimen/m3_comp_secondary_navigation_tab_pressed_state_layer_opacity = 0x7f06017c
co.auter.hcorp:dimen/m3_comp_secondary_navigation_tab_hover_state_layer_opacity = 0x7f06017b
co.auter.hcorp:styleable/MaterialButtonToggleGroup = 0x7f120052
co.auter.hcorp:layout/mtrl_alert_dialog = 0x7f0b0047
co.auter.hcorp:dimen/m3_comp_search_view_full_screen_header_container_height = 0x7f060178
co.auter.hcorp:attr/errorIconDrawable = 0x7f0301a7
co.auter.hcorp:drawable/btn_checkbox_unchecked_mtrl = 0x7f07007c
co.auter.hcorp:dimen/m3_comp_search_bar_hover_state_layer_opacity = 0x7f060174
co.auter.hcorp:dimen/m3_comp_search_bar_avatar_size = 0x7f060171
co.auter.hcorp:dimen/m3_comp_scrim_container_opacity = 0x7f060170
co.auter.hcorp:dimen/m3_btn_icon_only_icon_padding = 0x7f0600d8
co.auter.hcorp:dimen/m3_comp_radio_button_unselected_pressed_state_layer_opacity = 0x7f06016f
co.auter.hcorp:drawable/abc_btn_switch_to_on_mtrl_00012 = 0x7f070036
co.auter.hcorp:dimen/m3_comp_navigation_bar_container_elevation = 0x7f06013b
co.auter.hcorp:dimen/m3_comp_progress_indicator_track_thickness = 0x7f060167
co.auter.hcorp:style/Widget.MaterialComponents.Light.ActionBar.Solid = 0x7f110457
co.auter.hcorp:dimen/m3_comp_progress_indicator_active_indicator_track_space = 0x7f060165
co.auter.hcorp:style/Widget.Material3.Tooltip = 0x7f110423
co.auter.hcorp:attr/overlapAnchor = 0x7f030341
co.auter.hcorp:dimen/mtrl_snackbar_message_margin_horizontal = 0x7f0602f7
co.auter.hcorp:macro/m3_comp_navigation_drawer_inactive_icon_color = 0x7f0c008c
co.auter.hcorp:attr/shapeAppearanceCornerMedium = 0x7f0303a7
co.auter.hcorp:dimen/m3_fab_border_width = 0x7f0601b8
co.auter.hcorp:macro/m3_comp_switch_selected_hover_state_layer_color = 0x7f0c0127
co.auter.hcorp:dimen/m3_comp_primary_navigation_tab_inactive_pressed_state_layer_opacity = 0x7f060163
co.auter.hcorp:string/call_notification_decline_action = 0x7f100029
co.auter.hcorp:dimen/m3_comp_primary_navigation_tab_inactive_hover_state_layer_opacity = 0x7f060162
co.auter.hcorp:drawable/abc_popup_background_mtrl_mult = 0x7f070059
co.auter.hcorp:dimen/m3_carousel_small_item_size_max = 0x7f0600f4
co.auter.hcorp:dimen/m3_comp_primary_navigation_tab_inactive_focus_state_layer_opacity = 0x7f060161
co.auter.hcorp:color/m3_checkbox_button_icon_tint = 0x7f050081
co.auter.hcorp:dimen/m3_comp_primary_navigation_tab_active_indicator_height = 0x7f06015f
co.auter.hcorp:dimen/m3_comp_primary_navigation_tab_active_hover_state_layer_opacity = 0x7f06015e
co.auter.hcorp:dimen/m3_comp_outlined_text_field_outline_width = 0x7f06015c
co.auter.hcorp:drawable/design_snackbar_background = 0x7f07009b
co.auter.hcorp:macro/m3_comp_snackbar_container_color = 0x7f0c0113
co.auter.hcorp:attr/cardViewStyle = 0x7f0300a5
co.auter.hcorp:attr/marginHorizontal = 0x7f0302bc
co.auter.hcorp:dimen/m3_comp_outlined_text_field_disabled_supporting_text_opacity = 0x7f06015a
co.auter.hcorp:style/Platform.MaterialComponents.Light.Dialog = 0x7f11014a
co.auter.hcorp:interpolator/m3_sys_motion_easing_standard_accelerate = 0x7f0a000c
co.auter.hcorp:dimen/m3_comp_suggestion_chip_container_height = 0x7f06018d
co.auter.hcorp:dimen/m3_comp_switch_disabled_unselected_handle_opacity = 0x7f060195
co.auter.hcorp:style/Theme.AppCompat.DayNight.DarkActionBar = 0x7f110226
co.auter.hcorp:dimen/m3_comp_outlined_button_outline_width = 0x7f060152
co.auter.hcorp:style/Base.Theme.AppCompat = 0x7f11004e
co.auter.hcorp:dimen/m3_comp_outlined_autocomplete_menu_container_elevation = 0x7f060150
co.auter.hcorp:dimen/m3_comp_navigation_rail_pressed_state_layer_opacity = 0x7f06014f
co.auter.hcorp:dimen/m3_comp_navigation_rail_hover_state_layer_opacity = 0x7f06014d
co.auter.hcorp:drawable/notification_template_icon_bg = 0x7f0700fb
co.auter.hcorp:string/abc_searchview_description_submit = 0x7f100016
co.auter.hcorp:dimen/m3_comp_navigation_rail_focus_state_layer_opacity = 0x7f06014c
co.auter.hcorp:color/m3_sys_color_dark_secondary_container = 0x7f050183
co.auter.hcorp:drawable/notification_oversize_large_icon_bg = 0x7f0700fa
co.auter.hcorp:dimen/m3_comp_switch_unselected_hover_state_layer_opacity = 0x7f06019d
co.auter.hcorp:dimen/material_clock_period_toggle_width = 0x7f060231
co.auter.hcorp:dimen/m3_comp_time_picker_time_selector_hover_state_layer_opacity = 0x7f0601a9
co.auter.hcorp:dimen/m3_comp_navigation_rail_container_elevation = 0x7f06014a
co.auter.hcorp:dimen/m3_comp_navigation_rail_active_indicator_width = 0x7f060149
co.auter.hcorp:dimen/m3_comp_navigation_drawer_pressed_state_layer_opacity = 0x7f060146
co.auter.hcorp:style/Widget.AppCompat.PopupMenu = 0x7f110352
co.auter.hcorp:style/TextAppearance.Widget.AppCompat.ExpandedMenu.Item = 0x7f11021e
co.auter.hcorp:attr/toolbarNavigationButtonStyle = 0x7f03047d
co.auter.hcorp:dimen/m3_comp_navigation_drawer_modal_container_elevation = 0x7f060145
co.auter.hcorp:macro/m3_comp_navigation_drawer_active_hover_label_text_color = 0x7f0c007c
co.auter.hcorp:integer/mtrl_card_anim_duration_ms = 0x7f090036
co.auter.hcorp:dimen/m3_comp_navigation_drawer_icon_size = 0x7f060144
co.auter.hcorp:dimen/m3_comp_navigation_drawer_hover_state_layer_opacity = 0x7f060143
co.auter.hcorp:color/material_blue_grey_800 = 0x7f050226
co.auter.hcorp:dimen/m3_comp_navigation_drawer_focus_state_layer_opacity = 0x7f060142
co.auter.hcorp:dimen/m3_comp_navigation_bar_pressed_state_layer_opacity = 0x7f060140
co.auter.hcorp:dimen/m3_comp_navigation_bar_icon_size = 0x7f06013f
co.auter.hcorp:style/Widget.Material3.CollapsingToolbar = 0x7f1103b5
co.auter.hcorp:string/hide_bottom_view_on_scroll_behavior = 0x7f100075
co.auter.hcorp:dimen/m3_comp_navigation_bar_focus_state_layer_opacity = 0x7f06013d
co.auter.hcorp:dimen/m3_comp_navigation_bar_container_height = 0x7f06013c
co.auter.hcorp:macro/m3_comp_checkbox_selected_disabled_container_color = 0x7f0c0007
co.auter.hcorp:dimen/m3_comp_outlined_card_disabled_outline_opacity = 0x7f060154
co.auter.hcorp:dimen/m3_comp_navigation_bar_active_indicator_height = 0x7f060139
co.auter.hcorp:attr/windowActionModeOverlay = 0x7f0304b1
co.auter.hcorp:dimen/m3_comp_input_chip_with_leading_icon_leading_icon_size = 0x7f060137
co.auter.hcorp:dimen/m3_comp_input_chip_with_avatar_avatar_size = 0x7f060136
co.auter.hcorp:style/ThemeOverlay.Material3.FloatingActionButton.Tertiary = 0x7f1102db
co.auter.hcorp:dimen/m3_comp_input_chip_unselected_outline_width = 0x7f060135
co.auter.hcorp:dimen/m3_comp_filter_chip_flat_container_elevation = 0x7f060130
co.auter.hcorp:id/action_container = 0x7f080042
co.auter.hcorp:dimen/m3_comp_filter_chip_elevated_container_elevation = 0x7f06012f
co.auter.hcorp:dimen/m3_comp_filled_card_focus_state_layer_opacity = 0x7f060129
co.auter.hcorp:color/m3_sys_color_dynamic_dark_secondary = 0x7f0501a4
co.auter.hcorp:dimen/m3_comp_filled_button_with_icon_icon_size = 0x7f060126
co.auter.hcorp:dimen/abc_list_item_height_small_material = 0x7f060032
co.auter.hcorp:dimen/m3_comp_filled_autocomplete_menu_container_elevation = 0x7f060124
co.auter.hcorp:dimen/m3_comp_fab_primary_small_container_height = 0x7f060122
co.auter.hcorp:dimen/mtrl_navigation_rail_text_size = 0x7f0602d6
co.auter.hcorp:dimen/mtrl_progress_indicator_full_rounded_corner_radius = 0x7f0602e3
co.auter.hcorp:dimen/m3_comp_fab_primary_icon_size = 0x7f06011d
co.auter.hcorp:style/Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f11034a
co.auter.hcorp:attr/coplanarSiblingViewId = 0x7f03013f
co.auter.hcorp:dimen/mtrl_calendar_text_input_padding_top = 0x7f060296
co.auter.hcorp:dimen/m3_comp_fab_primary_hover_container_elevation = 0x7f06011b
co.auter.hcorp:dimen/m3_comp_extended_fab_primary_pressed_container_elevation = 0x7f060116
co.auter.hcorp:attr/endIconCheckable = 0x7f030196
co.auter.hcorp:dimen/m3_comp_extended_fab_primary_icon_size = 0x7f060115
co.auter.hcorp:attr/layout_editor_absoluteX = 0x7f030294
co.auter.hcorp:dimen/abc_disabled_alpha_material_light = 0x7f060028
co.auter.hcorp:dimen/m3_comp_extended_fab_primary_container_height = 0x7f060110
co.auter.hcorp:dimen/m3_comp_elevated_card_icon_size = 0x7f06010e
co.auter.hcorp:attr/isAutofillInlineSuggestionTheme = 0x7f030233
co.auter.hcorp:dimen/m3_comp_date_picker_modal_range_selection_header_container_height = 0x7f060109
co.auter.hcorp:dimen/m3_comp_checkbox_selected_disabled_container_opacity = 0x7f060106
co.auter.hcorp:attr/lastItemDecorated = 0x7f03025f
co.auter.hcorp:dimen/autofill_inline_suggestion_icon_size = 0x7f060052
co.auter.hcorp:dimen/m3_comp_bottom_app_bar_container_height = 0x7f060105
co.auter.hcorp:attr/layout_constraintBottom_toTopOf = 0x7f03026f
co.auter.hcorp:dimen/m3_comp_badge_large_size = 0x7f060102
co.auter.hcorp:style/Base.Theme.MaterialComponents.Dialog.Alert = 0x7f11006c
co.auter.hcorp:dimen/m3_comp_assist_chip_with_icon_icon_size = 0x7f060101
co.auter.hcorp:style/Widget.Material3.AutoCompleteTextView.FilledBox = 0x7f11037c
co.auter.hcorp:style/Base.V14.Theme.Material3.Light.SideSheetDialog = 0x7f110096
co.auter.hcorp:dimen/m3_comp_assist_chip_flat_outline_width = 0x7f060100
co.auter.hcorp:dimen/m3_chip_hovered_translation_z = 0x7f0600fb
co.auter.hcorp:dimen/m3_chip_dragged_translation_z = 0x7f0600f9
co.auter.hcorp:attr/maxNumber = 0x7f0302f6
co.auter.hcorp:dimen/m3_navigation_drawer_layout_corner_size = 0x7f0601c1
co.auter.hcorp:id/FUNCTION = 0x7f080004
co.auter.hcorp:dimen/m3_chip_disabled_translation_z = 0x7f0600f8
co.auter.hcorp:dimen/m3_chip_checked_hovered_translation_z = 0x7f0600f6
co.auter.hcorp:string/password_toggle_content_description = 0x7f1000e2
co.auter.hcorp:attr/transitionShapeAppearance = 0x7f03049a
co.auter.hcorp:dimen/m3_carousel_small_item_size_min = 0x7f0600f5
co.auter.hcorp:dimen/m3_carousel_small_item_default_corner_size = 0x7f0600f3
co.auter.hcorp:style/Base.Theme.SplashScreen.DayNight = 0x7f11007c
co.auter.hcorp:dimen/m3_carousel_extra_small_item_size = 0x7f0600f1
co.auter.hcorp:dimen/m3_card_stroke_width = 0x7f0600ef
co.auter.hcorp:dimen/m3_card_hovered_z = 0x7f0600ee
co.auter.hcorp:dimen/m3_card_elevated_elevation = 0x7f0600eb
co.auter.hcorp:dimen/material_clock_display_width = 0x7f060227
co.auter.hcorp:integer/abc_config_activityShortDur = 0x7f090001
co.auter.hcorp:dimen/mtrl_calendar_action_padding = 0x7f060276
co.auter.hcorp:styleable/CompoundButton = 0x7f120027
co.auter.hcorp:dimen/m3_card_dragged_z = 0x7f0600e8
co.auter.hcorp:dimen/m3_card_disabled_z = 0x7f0600e7
co.auter.hcorp:dimen/m3_btn_text_btn_padding_right = 0x7f0600e4
co.auter.hcorp:attr/tabIndicatorFullWidth = 0x7f030404
co.auter.hcorp:dimen/m3_btn_text_btn_padding_left = 0x7f0600e3
co.auter.hcorp:color/browser_actions_bg_grey = 0x7f050027
co.auter.hcorp:dimen/m3_btn_padding_right = 0x7f0600de
co.auter.hcorp:color/common_google_signin_btn_text_dark_default = 0x7f050038
co.auter.hcorp:dimen/m3_btn_inset = 0x7f0600da
co.auter.hcorp:id/TOP_START = 0x7f08000d
co.auter.hcorp:dimen/m3_btn_icon_only_min_width = 0x7f0600d9
co.auter.hcorp:dimen/mtrl_card_dragged_z = 0x7f0602a1
co.auter.hcorp:color/splashscreen_background = 0x7f050313
co.auter.hcorp:dimen/m3_btn_icon_btn_padding_left = 0x7f0600d4
co.auter.hcorp:attr/fontProviderFetchStrategy = 0x7f0301f8
co.auter.hcorp:dimen/m3_sys_elevation_level0 = 0x7f0601f3
co.auter.hcorp:dimen/m3_btn_dialog_btn_spacing = 0x7f0600cf
co.auter.hcorp:dimen/m3_datepicker_elevation = 0x7f0601b0
co.auter.hcorp:drawable/abc_text_select_handle_left_mtrl = 0x7f07006e
co.auter.hcorp:dimen/m3_btn_dialog_btn_min_width = 0x7f0600ce
co.auter.hcorp:dimen/m3_bottomappbar_horizontal_padding = 0x7f0600cd
co.auter.hcorp:dimen/m3_bottomappbar_height = 0x7f0600cc
co.auter.hcorp:attr/height = 0x7f03020a
co.auter.hcorp:color/m3_button_ripple_color = 0x7f05007a
co.auter.hcorp:dimen/m3_bottomappbar_fab_end_margin = 0x7f0600cb
co.auter.hcorp:attr/materialDividerStyle = 0x7f0302e0
co.auter.hcorp:dimen/m3_comp_switch_selected_focus_state_layer_opacity = 0x7f060197
co.auter.hcorp:animator/fragment_fade_exit = 0x7f020006
co.auter.hcorp:dimen/m3_bottom_sheet_modal_elevation = 0x7f0600c7
co.auter.hcorp:dimen/m3_bottom_nav_item_active_indicator_width = 0x7f0600c1
co.auter.hcorp:dimen/m3_bottom_nav_item_active_indicator_margin_horizontal = 0x7f0600c0
co.auter.hcorp:dimen/m3_badge_with_text_vertical_padding = 0x7f0600be
co.auter.hcorp:dimen/m3_badge_vertical_offset = 0x7f0600b9
co.auter.hcorp:dimen/m3_badge_offset = 0x7f0600b7
co.auter.hcorp:dimen/m3_simple_item_color_selected_alpha = 0x7f0601ed
co.auter.hcorp:dimen/m3_back_progress_side_container_max_scale_x_distance_shrink = 0x7f0600b4
co.auter.hcorp:dimen/m3_back_progress_main_container_max_translation_y = 0x7f0600b1
co.auter.hcorp:style/TextAppearance.AppCompat.Title.Inverse = 0x7f1101bf
co.auter.hcorp:dimen/m3_back_progress_bottom_container_max_scale_y_distance = 0x7f0600b0
co.auter.hcorp:dimen/m3_appbar_size_medium = 0x7f0600ae
co.auter.hcorp:string/path_password_eye = 0x7f1000e3
co.auter.hcorp:dimen/m3_appbar_size_compact = 0x7f0600ac
co.auter.hcorp:dimen/m3_appbar_scrim_height_trigger_medium = 0x7f0600ab
co.auter.hcorp:dimen/m3_card_elevated_hovered_z = 0x7f0600ec
co.auter.hcorp:dimen/m3_appbar_expanded_title_margin_horizontal = 0x7f0600a8
co.auter.hcorp:color/material_on_background_emphasis_medium = 0x7f050283
co.auter.hcorp:dimen/m3_alert_dialog_title_bottom_margin = 0x7f0600a6
co.auter.hcorp:dimen/m3_alert_dialog_icon_size = 0x7f0600a5
co.auter.hcorp:style/Widget.Material3.Slider.Legacy = 0x7f11040c
co.auter.hcorp:macro/m3_comp_outlined_text_field_input_text_type = 0x7f0c00c1
co.auter.hcorp:dimen/mtrl_calendar_header_height = 0x7f060284
co.auter.hcorp:id/adjust_height = 0x7f08004f
co.auter.hcorp:dimen/m3_alert_dialog_icon_margin = 0x7f0600a4
co.auter.hcorp:styleable/SideSheetBehavior_Layout = 0x7f12007d
co.auter.hcorp:color/mtrl_filled_stroke_color = 0x7f0502e3
co.auter.hcorp:dimen/mtrl_calendar_header_content_padding = 0x7f060281
co.auter.hcorp:attr/buttonGravity = 0x7f030093
co.auter.hcorp:dimen/m3_alert_dialog_corner_size = 0x7f0600a2
co.auter.hcorp:attr/placeholderImage = 0x7f03035d
co.auter.hcorp:dimen/m3_sys_motion_easing_standard_control_y2 = 0x7f060218
co.auter.hcorp:color/m3_dynamic_dark_primary_text_disable_only = 0x7f050093
co.auter.hcorp:dimen/notification_top_pad = 0x7f06031c
co.auter.hcorp:dimen/item_touch_helper_swipe_escape_max_velocity = 0x7f06009e
co.auter.hcorp:color/material_personalized_color_surface_bright = 0x7f0502ab
co.auter.hcorp:dimen/item_touch_helper_max_drag_scroll_per_frame = 0x7f06009d
co.auter.hcorp:color/m3_ref_palette_neutral_variant20 = 0x7f050130
co.auter.hcorp:dimen/hint_alpha_material_light = 0x7f06009a
co.auter.hcorp:dimen/hint_pressed_alpha_material_light = 0x7f06009c
co.auter.hcorp:drawable/abc_ic_menu_paste_mtrl_am_alpha = 0x7f070045
co.auter.hcorp:color/secondary_text_default_material_light = 0x7f050310
co.auter.hcorp:dimen/hint_pressed_alpha_material_dark = 0x7f06009b
co.auter.hcorp:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Surface = 0x7f1102d6
co.auter.hcorp:dimen/hint_alpha_material_dark = 0x7f060099
co.auter.hcorp:attr/layout_anchorGravity = 0x7f030265
co.auter.hcorp:drawable/ic_search_black_24 = 0x7f0700b4
co.auter.hcorp:dimen/highlight_alpha_material_dark = 0x7f060097
co.auter.hcorp:style/Base.Widget.AppCompat.Button.Colored = 0x7f1100d7
co.auter.hcorp:macro/m3_comp_switch_unselected_pressed_track_outline_color = 0x7f0c013f
co.auter.hcorp:dimen/fastscroll_minimum_range = 0x7f060095
co.auter.hcorp:dimen/fastscroll_default_thickness = 0x7f060093
co.auter.hcorp:styleable/MaterialTimePicker = 0x7f12005e
co.auter.hcorp:style/TextAppearance.Design.Counter.Overflow = 0x7f1101e0
co.auter.hcorp:string/summary_description = 0x7f1000fb
co.auter.hcorp:dimen/disabled_alpha_material_light = 0x7f060092
co.auter.hcorp:style/Theme.Design.Light.BottomSheetDialog = 0x7f110240
co.auter.hcorp:color/mtrl_fab_ripple_color = 0x7f0502e0
co.auter.hcorp:dimen/mtrl_calendar_year_height = 0x7f06029a
co.auter.hcorp:dimen/disabled_alpha_material_dark = 0x7f060091
co.auter.hcorp:style/Base.V7.Theme.AppCompat.Light = 0x7f1100c2
co.auter.hcorp:attr/materialTimePickerTitleStyle = 0x7f0302ee
co.auter.hcorp:dimen/design_tab_text_size_2line = 0x7f06008f
co.auter.hcorp:style/Base.MaterialAlertDialog.MaterialComponents.Title.Text = 0x7f110018
co.auter.hcorp:dimen/design_snackbar_text_size = 0x7f06008b
co.auter.hcorp:dimen/design_snackbar_padding_vertical = 0x7f060089
co.auter.hcorp:style/Widget.MaterialComponents.NavigationRailView.Compact = 0x7f110476
co.auter.hcorp:layout/m3_alert_dialog = 0x7f0b0034
co.auter.hcorp:dimen/design_snackbar_max_width = 0x7f060086
co.auter.hcorp:dimen/design_snackbar_extra_spacing_horizontal = 0x7f060085
co.auter.hcorp:color/m3_sys_color_dynamic_dark_primary = 0x7f0501a2
co.auter.hcorp:dimen/design_snackbar_elevation = 0x7f060084
co.auter.hcorp:style/Base.V21.Theme.MaterialComponents.Dialog = 0x7f1100ac
co.auter.hcorp:dimen/design_snackbar_action_inline_max_width = 0x7f060081
co.auter.hcorp:dimen/design_navigation_item_horizontal_padding = 0x7f06007b
co.auter.hcorp:dimen/design_navigation_icon_padding = 0x7f060079
co.auter.hcorp:style/Base.TextAppearance.AppCompat.Display2 = 0x7f11001f
co.auter.hcorp:dimen/design_navigation_elevation = 0x7f060078
co.auter.hcorp:dimen/m3_extended_fab_min_height = 0x7f0601b5
co.auter.hcorp:color/m3_sys_color_dynamic_dark_surface_variant = 0x7f0501ae
co.auter.hcorp:dimen/design_fab_translation_z_pressed = 0x7f060077
co.auter.hcorp:macro/m3_comp_switch_disabled_selected_handle_color = 0x7f0c0119
co.auter.hcorp:dimen/design_fab_translation_z_hovered_focused = 0x7f060076
co.auter.hcorp:style/ThemeOverlay.Material3.MaterialAlertDialog = 0x7f1102e0
co.auter.hcorp:color/highlighted_text_material_light = 0x7f050071
co.auter.hcorp:dimen/m3_comp_radio_button_selected_pressed_state_layer_opacity = 0x7f06016c
co.auter.hcorp:dimen/design_fab_size_normal = 0x7f060075
co.auter.hcorp:attr/textAppearanceListItemSecondary = 0x7f030436
co.auter.hcorp:dimen/design_fab_size_mini = 0x7f060074
co.auter.hcorp:dimen/mtrl_switch_track_height = 0x7f0602fd
co.auter.hcorp:dimen/mtrl_btn_icon_padding = 0x7f060264
co.auter.hcorp:dimen/design_fab_image_size = 0x7f060073
co.auter.hcorp:dimen/m3_comp_extended_fab_primary_focus_state_layer_opacity = 0x7f060112
co.auter.hcorp:dimen/design_fab_border_width = 0x7f060071
co.auter.hcorp:attr/colorSecondaryFixed = 0x7f030113
co.auter.hcorp:dimen/design_bottom_sheet_peek_height_min = 0x7f060070
co.auter.hcorp:color/m3_switch_thumb_tint = 0x7f05016b
co.auter.hcorp:dimen/splashscreen_icon_size = 0x7f060322
co.auter.hcorp:styleable/MaterialAlertDialog = 0x7f12004e
co.auter.hcorp:dimen/mtrl_badge_size = 0x7f060250
co.auter.hcorp:dimen/design_bottom_sheet_elevation = 0x7f06006e
co.auter.hcorp:macro/m3_comp_filled_text_field_error_trailing_icon_color = 0x7f0c004f
co.auter.hcorp:macro/m3_comp_bottom_app_bar_container_color = 0x7f0c0005
co.auter.hcorp:dimen/mtrl_tooltip_minWidth = 0x7f06030c
co.auter.hcorp:macro/m3_comp_outlined_button_outline_color = 0x7f0c00a6
co.auter.hcorp:dimen/abc_text_size_body_2_material = 0x7f060040
co.auter.hcorp:dimen/m3_btn_padding_bottom = 0x7f0600dc
co.auter.hcorp:style/Theme.AppCompat.DayNight = 0x7f110225
co.auter.hcorp:color/m3_ref_palette_primary50 = 0x7f050140
co.auter.hcorp:dimen/m3_alert_dialog_action_bottom_padding = 0x7f0600a0
co.auter.hcorp:dimen/design_bottom_navigation_shadow_height = 0x7f06006c
co.auter.hcorp:dimen/design_bottom_navigation_icon_size = 0x7f060067
co.auter.hcorp:dimen/design_bottom_navigation_elevation = 0x7f060065
co.auter.hcorp:macro/m3_comp_time_picker_period_selector_selected_focus_state_layer_color = 0x7f0c0156
co.auter.hcorp:dimen/m3_bottom_sheet_elevation = 0x7f0600c6
co.auter.hcorp:color/m3_sys_color_dark_primary = 0x7f050180
co.auter.hcorp:dimen/design_bottom_navigation_active_text_size = 0x7f060064
co.auter.hcorp:dimen/mtrl_card_checked_icon_margin = 0x7f06029e
co.auter.hcorp:id/direct = 0x7f0800a0
co.auter.hcorp:dimen/design_bottom_navigation_active_item_min_width = 0x7f060063
co.auter.hcorp:dimen/design_bottom_navigation_active_item_max_width = 0x7f060062
co.auter.hcorp:dimen/design_appbar_elevation = 0x7f060061
co.auter.hcorp:color/mtrl_textinput_focused_box_stroke_color = 0x7f0502ff
co.auter.hcorp:dimen/def_drawer_elevation = 0x7f060060
co.auter.hcorp:style/TextAppearance.MaterialComponents.Tooltip = 0x7f11021d
co.auter.hcorp:dimen/notification_subtext_size = 0x7f06031b
co.auter.hcorp:dimen/mtrl_textinput_box_stroke_width_default = 0x7f060302
co.auter.hcorp:layout/material_timepicker_dialog = 0x7f0b0045
co.auter.hcorp:dimen/m3_bottomappbar_fab_cradle_margin = 0x7f0600c8
co.auter.hcorp:attr/bottomSheetDialogTheme = 0x7f03007e
co.auter.hcorp:dimen/compat_notification_large_icon_max_width = 0x7f06005f
co.auter.hcorp:dimen/compat_notification_large_icon_max_height = 0x7f06005e
co.auter.hcorp:style/ThemeOverlay.Material3.TextInputEditText.FilledBox = 0x7f1102ef
co.auter.hcorp:dimen/design_tab_scrollable_min_width = 0x7f06008d
co.auter.hcorp:dimen/compat_button_padding_vertical_material = 0x7f06005c
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral24 = 0x7f0500b8
co.auter.hcorp:color/m3_ref_palette_secondary70 = 0x7f05014f
co.auter.hcorp:dimen/compat_button_padding_horizontal_material = 0x7f06005b
co.auter.hcorp:id/fill = 0x7f0800be
co.auter.hcorp:dimen/cardview_default_elevation = 0x7f060056
co.auter.hcorp:dimen/mtrl_slider_thumb_radius = 0x7f0602ed
co.auter.hcorp:macro/m3_comp_checkbox_selected_icon_color = 0x7f0c000b
co.auter.hcorp:color/tooltip_background_light = 0x7f05031b
co.auter.hcorp:dimen/browser_actions_context_menu_min_padding = 0x7f060054
co.auter.hcorp:style/Widget.MaterialComponents.TimePicker.Display = 0x7f110496
co.auter.hcorp:dimen/clock_face_margin_start = 0x7f060058
co.auter.hcorp:dimen/mtrl_card_elevation = 0x7f0602a2
co.auter.hcorp:dimen/appcompat_dialog_background_inset = 0x7f060051
co.auter.hcorp:dimen/m3_appbar_scrim_height_trigger_large = 0x7f0600aa
co.auter.hcorp:style/Platform.ThemeOverlay.AppCompat.Dark = 0x7f11014c
co.auter.hcorp:dimen/abc_text_size_subtitle_material_toolbar = 0x7f06004e
co.auter.hcorp:dimen/abc_text_size_subhead_material = 0x7f06004d
co.auter.hcorp:attr/counterOverflowTextColor = 0x7f03014e
co.auter.hcorp:dimen/abc_text_size_small_material = 0x7f06004c
co.auter.hcorp:style/Base.Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton = 0x7f11011a
co.auter.hcorp:dimen/mtrl_snackbar_action_text_color_alpha = 0x7f0602f3
co.auter.hcorp:attr/tabPaddingEnd = 0x7f03040d
co.auter.hcorp:dimen/abc_text_size_menu_material = 0x7f06004b
co.auter.hcorp:layout/material_clock_period_toggle = 0x7f0b003c
co.auter.hcorp:dimen/abc_text_size_menu_header_material = 0x7f06004a
co.auter.hcorp:style/Widget.MaterialComponents.MaterialCalendar.Item = 0x7f11046b
co.auter.hcorp:style/Base.Widget.AppCompat.Toolbar.Button.Navigation = 0x7f110101
co.auter.hcorp:dimen/m3_appbar_size_large = 0x7f0600ad
co.auter.hcorp:attr/enforceNavigationBarContrast = 0x7f03019f
co.auter.hcorp:dimen/abc_text_size_large_material = 0x7f060048
co.auter.hcorp:drawable/abc_btn_check_to_on_mtrl_000 = 0x7f07002d
co.auter.hcorp:dimen/abc_text_size_headline_material = 0x7f060047
co.auter.hcorp:style/Widget.Material3.BottomAppBar = 0x7f110382
co.auter.hcorp:dimen/abc_text_size_display_3_material = 0x7f060045
co.auter.hcorp:dimen/mtrl_tooltip_cornerSize = 0x7f06030a
co.auter.hcorp:dimen/abc_text_size_display_1_material = 0x7f060043
co.auter.hcorp:color/m3_chip_stroke_color = 0x7f050086
co.auter.hcorp:dimen/abc_text_size_body_1_material = 0x7f06003f
co.auter.hcorp:string/m3_exceed_max_badge_text_suffix = 0x7f10007b
co.auter.hcorp:dimen/abc_star_medium = 0x7f06003c
co.auter.hcorp:string/mtrl_checkbox_state_description_indeterminate = 0x7f1000ae
co.auter.hcorp:color/m3_fab_ripple_color_selector = 0x7f05009d
co.auter.hcorp:dimen/abc_seekbar_track_background_height_material = 0x7f060038
co.auter.hcorp:color/design_dark_default_color_error = 0x7f050045
co.auter.hcorp:dimen/abc_search_view_preferred_width = 0x7f060037
co.auter.hcorp:dimen/abc_search_view_preferred_height = 0x7f060036
co.auter.hcorp:anim/rns_fade_out = 0x7f010038
co.auter.hcorp:dimen/abc_progress_bar_height_material = 0x7f060035
co.auter.hcorp:style/Theme.ReactNative.TextInput.DefaultBackground = 0x7f1102a7
co.auter.hcorp:drawable/common_google_signin_btn_text_dark_normal_background = 0x7f07008f
co.auter.hcorp:dimen/abc_floating_window_z = 0x7f06002f
co.auter.hcorp:style/Theme.AppCompat.Empty = 0x7f110230
co.auter.hcorp:color/material_dynamic_color_light_error_container = 0x7f050232
co.auter.hcorp:dimen/abc_edit_text_inset_top_material = 0x7f06002e
co.auter.hcorp:dimen/abc_edit_text_inset_horizontal_material = 0x7f06002d
co.auter.hcorp:drawable/abc_text_select_handle_right_mtrl = 0x7f070070
co.auter.hcorp:dimen/abc_edit_text_inset_bottom_material = 0x7f06002c
co.auter.hcorp:dimen/abc_dropdownitem_text_padding_right = 0x7f06002b
co.auter.hcorp:style/Widget.MaterialComponents.Badge = 0x7f110430
co.auter.hcorp:dimen/abc_dropdownitem_icon_width = 0x7f060029
co.auter.hcorp:id/chronometer = 0x7f08007e
co.auter.hcorp:attr/roundTopStart = 0x7f03038f
co.auter.hcorp:color/m3_textfield_indicator_text_color = 0x7f050217
co.auter.hcorp:dimen/m3_navigation_rail_item_padding_bottom = 0x7f0601d3
co.auter.hcorp:style/Base.Widget.Material3.FloatingActionButton.Small = 0x7f110110
co.auter.hcorp:attr/cornerFamily = 0x7f030140
co.auter.hcorp:dimen/abc_disabled_alpha_material_dark = 0x7f060027
co.auter.hcorp:dimen/abc_dialog_title_divider_material = 0x7f060026
co.auter.hcorp:attr/indicatorSize = 0x7f03022f
co.auter.hcorp:dimen/abc_dialog_padding_top_material = 0x7f060025
co.auter.hcorp:attr/titleMargins = 0x7f030475
co.auter.hcorp:dimen/abc_dialog_padding_material = 0x7f060024
co.auter.hcorp:style/Theme.Material3.Dark = 0x7f110257
co.auter.hcorp:attr/layout_constraintCircle = 0x7f030270
co.auter.hcorp:dimen/abc_dialog_min_width_minor = 0x7f060023
co.auter.hcorp:style/Widget.MaterialComponents.BottomAppBar.Colored = 0x7f110432
co.auter.hcorp:dimen/abc_dialog_min_width_major = 0x7f060022
co.auter.hcorp:dimen/abc_dialog_list_padding_bottom_no_buttons = 0x7f060020
co.auter.hcorp:dimen/m3_comp_navigation_drawer_standard_container_elevation = 0x7f060147
co.auter.hcorp:dimen/abc_dialog_fixed_height_minor = 0x7f06001d
co.auter.hcorp:style/ShapeAppearance.Material3.Corner.None = 0x7f110182
co.auter.hcorp:dimen/abc_dialog_corner_radius_material = 0x7f06001b
co.auter.hcorp:style/AppTheme = 0x7f11000d
co.auter.hcorp:dimen/abc_control_padding_material = 0x7f06001a
co.auter.hcorp:dimen/abc_button_padding_horizontal_material = 0x7f060014
co.auter.hcorp:style/Widget.MaterialComponents.Chip.Choice = 0x7f110448
co.auter.hcorp:dimen/m3_sys_motion_easing_linear_control_y1 = 0x7f06020f
co.auter.hcorp:dimen/abc_alert_dialog_button_dimen = 0x7f060011
co.auter.hcorp:dimen/abc_action_button_min_width_overflow_material = 0x7f06000f
co.auter.hcorp:dimen/abc_text_size_medium_material = 0x7f060049
co.auter.hcorp:dimen/abc_action_button_min_height_material = 0x7f06000d
co.auter.hcorp:style/DialogAnimationFade = 0x7f11012e
co.auter.hcorp:dimen/abc_action_bar_stacked_max_height = 0x7f060009
co.auter.hcorp:dimen/abc_action_bar_elevation_material = 0x7f060005
co.auter.hcorp:dimen/abc_action_bar_default_padding_end_material = 0x7f060003
co.auter.hcorp:dimen/abc_action_bar_content_inset_with_nav = 0x7f060001
co.auter.hcorp:style/Base.V23.Theme.AppCompat = 0x7f1100b5
co.auter.hcorp:id/tabMode = 0x7f0801b9
co.auter.hcorp:color/tooltip_background_dark = 0x7f05031a
co.auter.hcorp:dimen/mtrl_navigation_rail_compact_width = 0x7f0602cf
co.auter.hcorp:style/Base.TextAppearance.AppCompat.Medium = 0x7f110028
co.auter.hcorp:dimen/m3_comp_filter_chip_flat_unselected_outline_width = 0x7f060131
co.auter.hcorp:style/ThemeOverlay.Material3.MaterialTimePicker.Display.TextInputEditText = 0x7f1102e6
co.auter.hcorp:color/switch_thumb_material_dark = 0x7f050316
co.auter.hcorp:id/search_src_text = 0x7f080192
co.auter.hcorp:color/m3_timepicker_button_text_color = 0x7f05021d
co.auter.hcorp:dimen/abc_action_bar_overflow_padding_start_material = 0x7f060008
co.auter.hcorp:color/switch_thumb_disabled_material_dark = 0x7f050314
co.auter.hcorp:id/rn_redbox_stack = 0x7f08017c
co.auter.hcorp:attr/checkedState = 0x7f0300b6
co.auter.hcorp:dimen/m3_sys_motion_easing_legacy_decelerate_control_y2 = 0x7f06020c
co.auter.hcorp:color/secondary_text_disabled_material_dark = 0x7f050311
co.auter.hcorp:style/ThemeOverlay.Material3.Dark = 0x7f1102ca
co.auter.hcorp:animator/mtrl_extended_fab_state_list_animator = 0x7f02001d
co.auter.hcorp:color/secondary_text_default_material_dark = 0x7f05030f
co.auter.hcorp:color/ripple_material_dark = 0x7f05030d
co.auter.hcorp:color/primary_text_disabled_material_dark = 0x7f05030b
co.auter.hcorp:color/primary_text_default_material_light = 0x7f05030a
co.auter.hcorp:color/m3_dynamic_default_color_secondary_text = 0x7f050095
co.auter.hcorp:color/primary_material_dark = 0x7f050307
co.auter.hcorp:style/Base.TextAppearance.AppCompat.Display1 = 0x7f11001e
co.auter.hcorp:color/notification_material_background_media_default_color = 0x7f050304
co.auter.hcorp:color/notification_action_color_filter = 0x7f050302
co.auter.hcorp:color/navigationBarColor = 0x7f050301
co.auter.hcorp:color/m3_dynamic_dark_highlighted_text = 0x7f050091
co.auter.hcorp:color/mtrl_textinput_filled_box_default_background_color = 0x7f0502fe
co.auter.hcorp:attr/textAppearanceSearchResultTitle = 0x7f03043b
co.auter.hcorp:color/mtrl_tabs_legacy_text_color_selector = 0x7f0502f9
co.auter.hcorp:drawable/design_fab_background = 0x7f070097
co.auter.hcorp:color/mtrl_tabs_icon_color_selector_colored = 0x7f0502f8
co.auter.hcorp:style/Theme.EdgeToEdge.Light = 0x7f110245
co.auter.hcorp:attr/fontStyle = 0x7f0301fd
co.auter.hcorp:dimen/design_bottom_navigation_margin = 0x7f06006b
co.auter.hcorp:color/mtrl_tabs_colored_ripple_color = 0x7f0502f6
co.auter.hcorp:attr/textInputOutlinedDenseStyle = 0x7f030449
co.auter.hcorp:color/mtrl_switch_track_decoration_tint = 0x7f0502f4
co.auter.hcorp:color/dim_foreground_material_dark = 0x7f05006a
co.auter.hcorp:color/mtrl_switch_thumb_tint = 0x7f0502f3
co.auter.hcorp:style/Base.Widget.MaterialComponents.PopupMenu.ContextMenu = 0x7f11011d
co.auter.hcorp:color/mtrl_popupmenu_overlay_color = 0x7f0502f0
co.auter.hcorp:macro/m3_comp_outlined_card_container_color = 0x7f0c00a8
co.auter.hcorp:color/mtrl_outlined_icon_tint = 0x7f0502ee
co.auter.hcorp:attr/layout_constraintRight_toLeftOf = 0x7f030284
co.auter.hcorp:attr/subMenuArrow = 0x7f0303e6
co.auter.hcorp:drawable/mtrl_tabs_default_indicator = 0x7f0700f0
co.auter.hcorp:color/design_icon_tint = 0x7f050066
co.auter.hcorp:color/mtrl_on_surface_ripple_color = 0x7f0502ed
co.auter.hcorp:string/abc_action_mode_done = 0x7f100003
co.auter.hcorp:color/mtrl_navigation_item_icon_tint = 0x7f0502ea
co.auter.hcorp:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date.Calendar = 0x7f11030c
co.auter.hcorp:dimen/m3_comp_menu_container_elevation = 0x7f060138
co.auter.hcorp:color/mtrl_navigation_bar_ripple_color = 0x7f0502e8
co.auter.hcorp:style/Theme.Material3.Dark.Dialog.Alert = 0x7f11025a
co.auter.hcorp:drawable/mtrl_switch_track_decoration = 0x7f0700ef
co.auter.hcorp:color/primary_dark_material_light = 0x7f050306
co.auter.hcorp:color/mtrl_navigation_bar_item_tint = 0x7f0502e7
co.auter.hcorp:color/m3_ref_palette_neutral50 = 0x7f050120
co.auter.hcorp:color/mtrl_navigation_bar_colored_ripple_color = 0x7f0502e6
co.auter.hcorp:id/transition_position = 0x7f0801e8
co.auter.hcorp:color/mtrl_indicator_text_color = 0x7f0502e4
co.auter.hcorp:style/Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f11029a
co.auter.hcorp:string/m3_sys_motion_easing_legacy_accelerate = 0x7f100085
co.auter.hcorp:id/topPanel = 0x7f0801dd
co.auter.hcorp:color/mtrl_filled_icon_tint = 0x7f0502e2
co.auter.hcorp:integer/m3_sys_motion_duration_long2 = 0x7f090016
co.auter.hcorp:attr/textAppearanceTitleSmall = 0x7f030441
co.auter.hcorp:dimen/compat_control_corner_material = 0x7f06005d
co.auter.hcorp:color/mtrl_fab_icon_text_color_selector = 0x7f0502df
co.auter.hcorp:id/pointer_events = 0x7f080164
co.auter.hcorp:color/mtrl_fab_bg_color_selector = 0x7f0502de
co.auter.hcorp:dimen/tooltip_precise_anchor_threshold = 0x7f06032d
co.auter.hcorp:style/DialogAnimationSlide = 0x7f11012f
co.auter.hcorp:color/mtrl_error = 0x7f0502dd
co.auter.hcorp:color/mtrl_chip_close_icon_tint = 0x7f0502d7
co.auter.hcorp:color/material_slider_active_tick_marks_color = 0x7f0502bf
co.auter.hcorp:dimen/m3_comp_filled_card_pressed_state_layer_opacity = 0x7f06012c
co.auter.hcorp:color/mtrl_card_view_foreground = 0x7f0502d4
co.auter.hcorp:dimen/mtrl_btn_padding_bottom = 0x7f060268
co.auter.hcorp:color/mtrl_calendar_selected_range = 0x7f0502d3
co.auter.hcorp:color/mtrl_calendar_item_stroke_color = 0x7f0502d2
co.auter.hcorp:id/accessibility_state_expanded = 0x7f080038
co.auter.hcorp:dimen/subtitle_shadow_offset = 0x7f060327
co.auter.hcorp:attr/roundingBorderWidth = 0x7f030394
co.auter.hcorp:dimen/m3_comp_assist_chip_container_height = 0x7f0600fd
co.auter.hcorp:color/mtrl_btn_text_color_selector = 0x7f0502d0
co.auter.hcorp:style/TextAppearance.MaterialComponents.Subtitle1 = 0x7f11021a
co.auter.hcorp:color/mtrl_btn_text_color_disabled = 0x7f0502cf
co.auter.hcorp:color/mtrl_btn_text_btn_ripple_color = 0x7f0502ce
co.auter.hcorp:attr/materialDisplayDividerStyle = 0x7f0302de
co.auter.hcorp:color/mtrl_btn_stroke_color_selector = 0x7f0502cc
co.auter.hcorp:style/TextAppearance.Design.Error = 0x7f1101e1
co.auter.hcorp:color/material_timepicker_modebutton_tint = 0x7f0502c9
co.auter.hcorp:color/material_timepicker_clockface = 0x7f0502c8
co.auter.hcorp:styleable/PopupWindowBackgroundState = 0x7f120070
co.auter.hcorp:dimen/mtrl_btn_disabled_elevation = 0x7f06025e
co.auter.hcorp:style/Base.Widget.MaterialComponents.TextView = 0x7f110124
co.auter.hcorp:color/material_timepicker_clock_text_color = 0x7f0502c7
co.auter.hcorp:color/material_slider_thumb_color = 0x7f0502c4
co.auter.hcorp:attr/layout_behavior = 0x7f030266
co.auter.hcorp:dimen/mtrl_tooltip_padding = 0x7f06030d
co.auter.hcorp:color/material_slider_inactive_track_color = 0x7f0502c3
co.auter.hcorp:dimen/m3_comp_switch_selected_pressed_state_layer_opacity = 0x7f060199
co.auter.hcorp:style/Widget.MaterialComponents.TimePicker.Display.TextInputEditText = 0x7f110499
co.auter.hcorp:color/material_personalized_primary_inverse_text_disable_only = 0x7f0502bd
co.auter.hcorp:integer/config_tooltipAnimTime = 0x7f090005
co.auter.hcorp:animator/m3_appbar_state_list_animator = 0x7f020009
co.auter.hcorp:color/material_personalized_color_text_secondary_and_tertiary_inverse = 0x7f0502b9
co.auter.hcorp:dimen/m3_comp_filled_card_container_elevation = 0x7f060127
co.auter.hcorp:color/material_personalized_color_text_primary_inverse = 0x7f0502b7
co.auter.hcorp:string/item_view_role_description = 0x7f100079
co.auter.hcorp:interpolator/m3_sys_motion_easing_emphasized_accelerate = 0x7f0a0008
co.auter.hcorp:attr/badgeWidePadding = 0x7f030061
co.auter.hcorp:color/material_personalized_color_text_hint_foreground_inverse = 0x7f0502b6
co.auter.hcorp:style/TextAppearance.Material3.MaterialTimePicker.Title = 0x7f110206
co.auter.hcorp:color/material_personalized_color_tertiary_container = 0x7f0502b5
co.auter.hcorp:styleable/NavigationBarView = 0x7f12006a
co.auter.hcorp:macro/m3_comp_time_input_time_input_field_container_shape = 0x7f0c0147
co.auter.hcorp:color/material_personalized_color_tertiary = 0x7f0502b4
co.auter.hcorp:attr/hintAnimationEnabled = 0x7f030214
co.auter.hcorp:color/material_personalized_color_surface_dim = 0x7f0502b1
co.auter.hcorp:attr/closeIconStartPadding = 0x7f0300d8
co.auter.hcorp:color/material_personalized_color_surface_container_low = 0x7f0502af
co.auter.hcorp:attr/itemShapeAppearance = 0x7f030246
co.auter.hcorp:dimen/mtrl_navigation_bar_item_default_icon_size = 0x7f0602c6
co.auter.hcorp:color/material_personalized_color_surface_container = 0x7f0502ac
co.auter.hcorp:drawable/abc_ic_clear_material = 0x7f07003f
co.auter.hcorp:style/Widget.Material3.TextInputLayout.FilledBox.Dense.ExposedDropdownMenu = 0x7f11041a
co.auter.hcorp:color/material_personalized_color_secondary_text_inverse = 0x7f0502a9
co.auter.hcorp:color/material_personalized_color_primary_inverse = 0x7f0502a3
co.auter.hcorp:style/Widget.MaterialComponents.CheckedTextView = 0x7f110446
co.auter.hcorp:color/material_personalized_color_outline_variant = 0x7f0502a0
co.auter.hcorp:color/material_personalized_color_outline = 0x7f05029f
co.auter.hcorp:integer/design_tab_indicator_anim_duration_ms = 0x7f090008
co.auter.hcorp:dimen/m3_comp_switch_disabled_unselected_icon_opacity = 0x7f060196
co.auter.hcorp:color/material_personalized_color_on_tertiary_container = 0x7f05029e
co.auter.hcorp:color/material_personalized_color_on_tertiary = 0x7f05029d
co.auter.hcorp:color/material_personalized_color_on_surface_inverse = 0x7f05029b
co.auter.hcorp:drawable/btn_checkbox_checked_to_unchecked_mtrl_animation = 0x7f07007b
co.auter.hcorp:style/AlertDialog.AppCompat = 0x7f110000
co.auter.hcorp:dimen/m3_comp_date_picker_modal_header_container_height = 0x7f060108
co.auter.hcorp:color/m3_dark_primary_text_disable_only = 0x7f05008c
co.auter.hcorp:color/material_personalized_color_on_secondary = 0x7f050298
co.auter.hcorp:dimen/mtrl_navigation_rail_text_bottom_margin = 0x7f0602d5
co.auter.hcorp:style/ThemeOverlay.MaterialComponents.Dialog.Alert.Framework = 0x7f110306
co.auter.hcorp:color/material_personalized_color_on_primary = 0x7f050296
co.auter.hcorp:style/Base.TextAppearance.MaterialComponents.Headline6 = 0x7f110049
co.auter.hcorp:attr/iconEndPadding = 0x7f03021e
co.auter.hcorp:color/material_personalized_color_surface_inverse = 0x7f0502b2
co.auter.hcorp:color/material_personalized_color_on_error_container = 0x7f050295
co.auter.hcorp:color/material_personalized_color_on_error = 0x7f050294
co.auter.hcorp:color/common_google_signin_btn_text_dark_focused = 0x7f05003a
co.auter.hcorp:color/material_personalized_color_error_container = 0x7f050292
co.auter.hcorp:attr/endIconTint = 0x7f03019c
co.auter.hcorp:dimen/cardview_compat_inset_shadow = 0x7f060055
co.auter.hcorp:style/Widget.Material3.Button.IconButton.Filled = 0x7f110390
co.auter.hcorp:color/material_personalized_color_control_activated = 0x7f05028e
co.auter.hcorp:color/material_dynamic_secondary20 = 0x7f05025f
co.auter.hcorp:color/material_personalized__highlighted_text_inverse = 0x7f05028c
co.auter.hcorp:style/ThemeOverlay.MaterialComponents.Dialog.Alert = 0x7f110305
co.auter.hcorp:dimen/material_textinput_max_width = 0x7f060245
co.auter.hcorp:color/material_personalized__highlighted_text = 0x7f05028b
co.auter.hcorp:styleable/Toolbar = 0x7f120091
co.auter.hcorp:layout/mtrl_calendar_months = 0x7f0b0055
co.auter.hcorp:color/material_on_surface_stroke = 0x7f05028a
co.auter.hcorp:style/Base.TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f11004d
co.auter.hcorp:string/appbar_scrolling_view_behavior = 0x7f100020
co.auter.hcorp:attr/values = 0x7f0304a2
co.auter.hcorp:color/material_on_primary_emphasis_high_type = 0x7f050285
co.auter.hcorp:dimen/material_helper_text_font_1_3_padding_horizontal = 0x7f060241
co.auter.hcorp:color/material_on_background_disabled = 0x7f050281
co.auter.hcorp:dimen/design_navigation_item_icon_padding = 0x7f06007c
co.auter.hcorp:attr/closeIconEndPadding = 0x7f0300d6
co.auter.hcorp:color/material_harmonized_color_on_error_container = 0x7f050280
co.auter.hcorp:color/material_harmonized_color_error_container = 0x7f05027e
co.auter.hcorp:color/m3_sys_color_tertiary_fixed_dim = 0x7f05020c
co.auter.hcorp:color/material_harmonized_color_error = 0x7f05027d
co.auter.hcorp:id/browser_actions_menu_item_icon = 0x7f08006b
co.auter.hcorp:attr/layout_constraintWidth_percent = 0x7f030292
co.auter.hcorp:color/material_grey_900 = 0x7f05027c
co.auter.hcorp:attr/layout_constraintBottom_toBottomOf = 0x7f03026e
co.auter.hcorp:drawable/m3_popupmenu_background_overlay = 0x7f0700bb
co.auter.hcorp:dimen/m3_sys_motion_easing_emphasized_accelerate_control_x2 = 0x7f0601fa
co.auter.hcorp:color/material_grey_850 = 0x7f05027b
co.auter.hcorp:color/material_grey_600 = 0x7f050279
co.auter.hcorp:style/Widget.Material3.TextInputLayout.FilledBox.Dense = 0x7f110419
co.auter.hcorp:id/accessibility_custom_action_22 = 0x7f080022
co.auter.hcorp:color/m3_sys_color_on_tertiary_fixed_variant = 0x7f050206
co.auter.hcorp:color/material_grey_50 = 0x7f050278
co.auter.hcorp:styleable/FloatingActionButton_Behavior_Layout = 0x7f120034
co.auter.hcorp:color/material_grey_100 = 0x7f050276
co.auter.hcorp:color/material_dynamic_tertiary95 = 0x7f050274
co.auter.hcorp:drawable/ic_keyboard_black_24dp = 0x7f0700a7
co.auter.hcorp:color/material_dynamic_tertiary60 = 0x7f050270
co.auter.hcorp:attr/passwordToggleTintMode = 0x7f030354
co.auter.hcorp:dimen/design_tab_max_width = 0x7f06008c
co.auter.hcorp:color/material_dynamic_tertiary40 = 0x7f05026e
co.auter.hcorp:macro/m3_comp_secondary_navigation_tab_with_icon_inactive_icon_color = 0x7f0c0103
co.auter.hcorp:attr/errorAccessibilityLabel = 0x7f0301a3
co.auter.hcorp:color/material_dynamic_tertiary20 = 0x7f05026c
co.auter.hcorp:dimen/m3_badge_with_text_size = 0x7f0600bc
co.auter.hcorp:color/material_dynamic_tertiary0 = 0x7f050269
co.auter.hcorp:attr/borderlessButtonStyle = 0x7f03007a
co.auter.hcorp:color/material_dynamic_secondary99 = 0x7f050268
co.auter.hcorp:attr/floatingActionButtonSmallSurfaceStyle = 0x7f0301dc
co.auter.hcorp:color/material_dynamic_secondary80 = 0x7f050265
co.auter.hcorp:string/m3_ref_typeface_plain_medium = 0x7f10007e
co.auter.hcorp:color/m3_sys_color_light_on_primary_container = 0x7f0501e9
co.auter.hcorp:color/material_dynamic_secondary70 = 0x7f050264
co.auter.hcorp:drawable/mtrl_switch_thumb_pressed_checked = 0x7f0700e9
co.auter.hcorp:color/material_dynamic_secondary60 = 0x7f050263
co.auter.hcorp:anim/m3_bottom_sheet_slide_in = 0x7f010027
co.auter.hcorp:color/material_dynamic_secondary40 = 0x7f050261
co.auter.hcorp:attr/materialSwitchStyle = 0x7f0302ea
co.auter.hcorp:attr/statusBarForeground = 0x7f0303e2
co.auter.hcorp:dimen/abc_list_item_height_material = 0x7f060031
co.auter.hcorp:color/material_dynamic_secondary100 = 0x7f05025e
co.auter.hcorp:macro/m3_comp_navigation_drawer_modal_container_color = 0x7f0c0092
co.auter.hcorp:color/material_dynamic_secondary10 = 0x7f05025d
co.auter.hcorp:color/material_dynamic_primary95 = 0x7f05025a
co.auter.hcorp:color/material_dynamic_primary90 = 0x7f050259
co.auter.hcorp:color/material_dynamic_primary80 = 0x7f050258
co.auter.hcorp:layout/abc_action_bar_title_item = 0x7f0b0000
co.auter.hcorp:attr/colorOnSecondaryContainer = 0x7f0300fc
co.auter.hcorp:color/material_dynamic_primary60 = 0x7f050256
co.auter.hcorp:drawable/$mtrl_switch_thumb_checked_unchecked__1 = 0x7f070022
co.auter.hcorp:integer/m3_sys_motion_duration_short1 = 0x7f09001d
co.auter.hcorp:color/material_dynamic_primary50 = 0x7f050255
co.auter.hcorp:color/material_dynamic_primary30 = 0x7f050253
co.auter.hcorp:dimen/m3_sys_motion_easing_legacy_control_y2 = 0x7f060208
co.auter.hcorp:color/material_dynamic_primary20 = 0x7f050252
co.auter.hcorp:attr/endIconDrawable = 0x7f030198
co.auter.hcorp:dimen/m3_comp_time_picker_period_selector_pressed_state_layer_opacity = 0x7f0601a7
co.auter.hcorp:style/ThemeOverlay.Material3.FloatingActionButton.Primary = 0x7f1102d8
co.auter.hcorp:color/material_dynamic_primary10 = 0x7f050250
co.auter.hcorp:color/material_dynamic_primary0 = 0x7f05024f
co.auter.hcorp:id/textinput_placeholder = 0x7f0801d4
co.auter.hcorp:dimen/m3_comp_elevated_button_disabled_container_elevation = 0x7f06010c
co.auter.hcorp:color/m3_ref_palette_neutral_variant10 = 0x7f05012e
co.auter.hcorp:color/material_dynamic_neutral_variant95 = 0x7f05024d
co.auter.hcorp:color/material_dynamic_neutral_variant90 = 0x7f05024c
co.auter.hcorp:dimen/m3_comp_radio_button_selected_focus_state_layer_opacity = 0x7f06016a
co.auter.hcorp:string/mtrl_picker_save = 0x7f1000cb
co.auter.hcorp:color/material_dynamic_neutral_variant60 = 0x7f050249
co.auter.hcorp:dimen/design_tab_text_size = 0x7f06008e
co.auter.hcorp:color/material_dynamic_neutral_variant50 = 0x7f050248
co.auter.hcorp:string/abc_menu_delete_shortcut_label = 0x7f10000a
co.auter.hcorp:color/material_dynamic_neutral_variant40 = 0x7f050247
co.auter.hcorp:color/material_dynamic_neutral_variant30 = 0x7f050246
co.auter.hcorp:color/material_dynamic_neutral_variant20 = 0x7f050245
co.auter.hcorp:dimen/m3_comp_radio_button_unselected_hover_state_layer_opacity = 0x7f06016e
co.auter.hcorp:color/material_dynamic_neutral99 = 0x7f050241
co.auter.hcorp:macro/m3_comp_date_picker_modal_range_selection_month_subhead_color = 0x7f0c001b
co.auter.hcorp:dimen/mtrl_badge_with_text_size = 0x7f060255
co.auter.hcorp:dimen/design_bottom_navigation_item_min_width = 0x7f060069
co.auter.hcorp:color/material_dynamic_neutral70 = 0x7f05023d
co.auter.hcorp:color/material_dynamic_neutral50 = 0x7f05023b
co.auter.hcorp:attr/closeIcon = 0x7f0300d4
co.auter.hcorp:color/material_dynamic_neutral40 = 0x7f05023a
co.auter.hcorp:attr/flow_firstVerticalStyle = 0x7f0301e4
co.auter.hcorp:drawable/abc_seekbar_tick_mark_material = 0x7f070063
co.auter.hcorp:style/Base.TextAppearance.AppCompat.SearchResult.Subtitle = 0x7f11002c
co.auter.hcorp:layout/notification_template_media = 0x7f0b0072
co.auter.hcorp:color/material_dynamic_neutral10 = 0x7f050236
co.auter.hcorp:drawable/mtrl_checkbox_button_icon = 0x7f0700cf
co.auter.hcorp:style/ThemeOverlay.MaterialComponents.BottomAppBar.Surface = 0x7f1102ff
co.auter.hcorp:color/material_dynamic_neutral0 = 0x7f050235
co.auter.hcorp:style/TextAppearance.AppCompat.Widget.ActionBar.Menu = 0x7f1101c1
co.auter.hcorp:color/material_dynamic_color_light_on_error = 0x7f050233
co.auter.hcorp:color/material_slider_active_track_color = 0x7f0502c0
co.auter.hcorp:color/material_dynamic_color_dark_on_error = 0x7f05022f
co.auter.hcorp:color/material_dynamic_color_dark_error_container = 0x7f05022e
co.auter.hcorp:color/material_dynamic_color_dark_error = 0x7f05022d
co.auter.hcorp:dimen/m3_comp_suggestion_chip_flat_container_elevation = 0x7f06018f
co.auter.hcorp:string/material_timepicker_pm = 0x7f10009e
co.auter.hcorp:dimen/m3_comp_fab_primary_large_container_height = 0x7f06011e
co.auter.hcorp:dimen/mtrl_textinput_box_corner_radius_medium = 0x7f0602ff
co.auter.hcorp:dimen/m3_comp_switch_selected_hover_state_layer_opacity = 0x7f060198
co.auter.hcorp:color/m3_sys_color_on_primary_fixed = 0x7f050201
co.auter.hcorp:color/material_divider_color = 0x7f05022c
co.auter.hcorp:color/m3_tonal_button_ripple_color_selector = 0x7f050225
co.auter.hcorp:color/m3_timepicker_time_input_stroke_color = 0x7f050224
co.auter.hcorp:style/Theme = 0x7f110221
co.auter.hcorp:style/Base.Widget.Material3.Light.ActionBar.Solid = 0x7f110111
co.auter.hcorp:color/m3_sys_color_dark_inverse_primary = 0x7f050171
co.auter.hcorp:dimen/design_snackbar_min_width = 0x7f060087
co.auter.hcorp:attr/helperTextTextAppearance = 0x7f03020d
co.auter.hcorp:color/m3_timepicker_secondary_text_button_text_color = 0x7f050223
co.auter.hcorp:id/mini = 0x7f080117
co.auter.hcorp:dimen/mtrl_calendar_day_vertical_padding = 0x7f06027d
co.auter.hcorp:style/Theme.MaterialComponents.Light.DarkActionBar.Bridge = 0x7f110296
co.auter.hcorp:style/Base.DialogWindowTitleBackground.AppCompat = 0x7f110015
co.auter.hcorp:color/m3_timepicker_display_text_color = 0x7f050221
co.auter.hcorp:macro/m3_comp_filled_icon_button_toggle_selected_icon_color = 0x7f0c0049
co.auter.hcorp:dimen/mtrl_progress_circular_inset_extra_small = 0x7f0602d8
co.auter.hcorp:color/m3_timepicker_display_ripple_color = 0x7f050220
co.auter.hcorp:color/m3_timepicker_display_background_color = 0x7f05021f
co.auter.hcorp:drawable/mtrl_checkbox_button_icon_unchecked_checked = 0x7f0700d4
co.auter.hcorp:color/m3_timepicker_clock_text_color = 0x7f05021e
co.auter.hcorp:color/m3_textfield_stroke_color = 0x7f05021a
co.auter.hcorp:color/m3_textfield_label_color = 0x7f050219
co.auter.hcorp:dimen/m3_comp_suggestion_chip_flat_outline_width = 0x7f060190
co.auter.hcorp:color/m3_sys_color_dynamic_light_secondary_container = 0x7f0501c7
co.auter.hcorp:color/material_dynamic_neutral_variant70 = 0x7f05024a
co.auter.hcorp:style/Animation.Material3.BottomSheetDialog = 0x7f110008
co.auter.hcorp:color/m3_textfield_filled_background_color = 0x7f050216
co.auter.hcorp:dimen/m3_btn_elevated_btn_elevation = 0x7f0600d2
co.auter.hcorp:style/CalendarDatePickerStyle = 0x7f11012a
co.auter.hcorp:color/m3_text_button_ripple_color_selector = 0x7f050215
co.auter.hcorp:color/m3_text_button_foreground_color_selector = 0x7f050214
co.auter.hcorp:styleable/AnimatedStateListDrawableItem = 0x7f120008
co.auter.hcorp:color/m3_text_button_background_color_selector = 0x7f050213
co.auter.hcorp:style/Base.Theme.MaterialComponents.Dialog.Bridge = 0x7f11006d
co.auter.hcorp:color/material_grey_300 = 0x7f050277
co.auter.hcorp:color/m3_tabs_text_color_secondary = 0x7f050212
co.auter.hcorp:attr/textAppearanceBodyLarge = 0x7f03041f
co.auter.hcorp:color/m3_sys_color_dynamic_dark_on_tertiary = 0x7f05019e
co.auter.hcorp:color/m3_tabs_ripple_color_secondary = 0x7f050210
co.auter.hcorp:string/mtrl_checkbox_button_icon_path_checked = 0x7f1000a5
co.auter.hcorp:color/material_dynamic_neutral_variant99 = 0x7f05024e
co.auter.hcorp:style/Widget.MaterialComponents.TimePicker = 0x7f110493
co.auter.hcorp:style/Widget.MaterialComponents.CircularProgressIndicator.Small = 0x7f11044f
co.auter.hcorp:string/m3_sys_motion_easing_standard_decelerate = 0x7f10008a
co.auter.hcorp:color/m3_sys_color_secondary_fixed = 0x7f050209
co.auter.hcorp:dimen/notification_media_narrow_margin = 0x7f060316
co.auter.hcorp:color/m3_sys_color_primary_fixed_dim = 0x7f050208
co.auter.hcorp:attr/rippleColor = 0x7f030384
co.auter.hcorp:color/material_dynamic_secondary95 = 0x7f050267
co.auter.hcorp:macro/m3_comp_slider_active_track_color = 0x7f0c010b
co.auter.hcorp:dimen/m3_divider_heavy_thickness = 0x7f0601b1
co.auter.hcorp:color/m3_sys_color_on_tertiary_fixed = 0x7f050205
co.auter.hcorp:anim/m3_side_sheet_enter_from_right = 0x7f01002c
co.auter.hcorp:dimen/m3_appbar_expanded_title_margin_bottom = 0x7f0600a7
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral_variant98 = 0x7f0500df
co.auter.hcorp:color/m3_sys_color_on_secondary_fixed = 0x7f050203
co.auter.hcorp:dimen/material_textinput_min_width = 0x7f060246
co.auter.hcorp:style/Base.Widget.AppCompat.Button = 0x7f1100d3
co.auter.hcorp:macro/m3_comp_navigation_rail_label_text_type = 0x7f0c009f
co.auter.hcorp:color/m3_sys_color_light_tertiary_container = 0x7f050200
co.auter.hcorp:color/m3_sys_color_light_tertiary = 0x7f0501ff
co.auter.hcorp:style/Base.V24.Theme.Material3.Dark = 0x7f1100b7
co.auter.hcorp:dimen/m3_sys_motion_easing_legacy_accelerate_control_x2 = 0x7f060202
co.auter.hcorp:color/m3_sys_color_light_surface_variant = 0x7f0501fe
co.auter.hcorp:color/material_dynamic_tertiary100 = 0x7f05026b
co.auter.hcorp:color/m3_sys_color_light_surface_container_highest = 0x7f0501fa
co.auter.hcorp:color/m3_sys_color_light_surface_container = 0x7f0501f8
co.auter.hcorp:style/Base.TextAppearance.AppCompat.Display4 = 0x7f110021
co.auter.hcorp:color/material_timepicker_button_background = 0x7f0502c5
co.auter.hcorp:color/m3_sys_color_light_surface = 0x7f0501f6
co.auter.hcorp:color/m3_sys_color_light_secondary_container = 0x7f0501f5
co.auter.hcorp:macro/m3_comp_slider_inactive_track_color = 0x7f0c0110
co.auter.hcorp:id/search_close_btn = 0x7f08018d
co.auter.hcorp:dimen/abc_text_size_display_2_material = 0x7f060044
co.auter.hcorp:attr/iconifiedByDefault = 0x7f030225
co.auter.hcorp:drawable/abc_scrubber_primary_mtrl_alpha = 0x7f070060
co.auter.hcorp:attr/titlePositionInterpolator = 0x7f030476
co.auter.hcorp:color/m3_sys_color_light_secondary = 0x7f0501f4
co.auter.hcorp:color/m3_sys_color_light_primary_container = 0x7f0501f3
co.auter.hcorp:attr/colorOnErrorContainer = 0x7f0300f5
co.auter.hcorp:color/m3_sys_color_light_outline = 0x7f0501f0
co.auter.hcorp:style/Widget.AppCompat.RatingBar.Small = 0x7f110359
co.auter.hcorp:macro/m3_comp_switch_unselected_pressed_track_color = 0x7f0c013e
co.auter.hcorp:layout/notification_template_big_media_narrow = 0x7f0b006d
co.auter.hcorp:color/m3_sys_color_light_on_tertiary_container = 0x7f0501ef
co.auter.hcorp:style/Widget.Material3.MaterialCalendar.HeaderSelection.Fullscreen = 0x7f1103e2
co.auter.hcorp:color/m3_sys_color_light_on_tertiary = 0x7f0501ee
co.auter.hcorp:color/m3_sys_color_light_on_surface_variant = 0x7f0501ed
co.auter.hcorp:style/Base.Theme.Material3.Dark.DialogWhenLarge = 0x7f110060
co.auter.hcorp:color/m3_sys_color_light_on_secondary_container = 0x7f0501eb
co.auter.hcorp:string/mtrl_picker_text_input_day_abbr = 0x7f1000d0
co.auter.hcorp:macro/m3_comp_extended_fab_tertiary_container_color = 0x7f0c0034
co.auter.hcorp:color/m3_sys_color_light_on_secondary = 0x7f0501ea
co.auter.hcorp:attr/title = 0x7f03046c
co.auter.hcorp:color/m3_ref_palette_neutral98 = 0x7f05012b
co.auter.hcorp:color/m3_sys_color_light_on_primary = 0x7f0501e8
co.auter.hcorp:id/centerInside = 0x7f080077
co.auter.hcorp:dimen/abc_action_bar_default_padding_start_material = 0x7f060004
co.auter.hcorp:dimen/mtrl_exposed_dropdown_menu_popup_vertical_padding = 0x7f0602a8
co.auter.hcorp:color/m3_sys_color_light_on_error_container = 0x7f0501e7
co.auter.hcorp:dimen/m3_comp_fab_primary_container_height = 0x7f060119
co.auter.hcorp:dimen/m3_bottom_nav_min_height = 0x7f0600c4
co.auter.hcorp:color/m3_sys_color_light_on_error = 0x7f0501e6
co.auter.hcorp:macro/m3_comp_dialog_container_shape = 0x7f0c0023
co.auter.hcorp:color/m3_sys_color_light_inverse_primary = 0x7f0501e3
co.auter.hcorp:color/m3_sys_color_light_error = 0x7f0501e0
co.auter.hcorp:string/mtrl_picker_text_input_date_hint = 0x7f1000cd
co.auter.hcorp:dimen/m3_comp_filled_card_dragged_state_layer_opacity = 0x7f060128
co.auter.hcorp:color/m3_sys_color_dynamic_tertiary_fixed_dim = 0x7f0501de
co.auter.hcorp:attr/ratingBarStyle = 0x7f030378
co.auter.hcorp:drawable/notification_bg_low_normal = 0x7f0700f5
co.auter.hcorp:layout/mtrl_calendar_vertical = 0x7f0b0056
co.auter.hcorp:color/m3_sys_color_dynamic_secondary_fixed_dim = 0x7f0501dc
co.auter.hcorp:dimen/m3_badge_with_text_offset = 0x7f0600bb
co.auter.hcorp:style/Widget.Material3.MaterialCalendar.Year.Today = 0x7f1103ea
co.auter.hcorp:color/material_dynamic_neutral95 = 0x7f050240
co.auter.hcorp:color/m3_sys_color_dynamic_secondary_fixed = 0x7f0501db
co.auter.hcorp:styleable/LinearLayoutCompat = 0x7f120049
co.auter.hcorp:color/m3_sys_color_dynamic_on_tertiary_fixed_variant = 0x7f0501d8
co.auter.hcorp:color/m3_sys_color_dynamic_light_tertiary = 0x7f0501d1
co.auter.hcorp:style/Base.Widget.AppCompat.Light.ActionBar.TabText = 0x7f1100e6
co.auter.hcorp:dimen/material_helper_text_font_1_3_padding_top = 0x7f060242
co.auter.hcorp:color/m3_sys_color_dynamic_light_surface_variant = 0x7f0501d0
co.auter.hcorp:color/m3_sys_color_dynamic_light_surface_container_lowest = 0x7f0501ce
co.auter.hcorp:color/m3_sys_color_dynamic_light_surface_container_low = 0x7f0501cd
co.auter.hcorp:style/Widget.Design.CollapsingToolbar = 0x7f110371
co.auter.hcorp:color/m3_sys_color_dynamic_light_surface_container_highest = 0x7f0501cc
co.auter.hcorp:styleable/NavigationView = 0x7f12006c
co.auter.hcorp:layout/material_timepicker_textinput_display = 0x7f0b0046
co.auter.hcorp:dimen/m3_comp_assist_chip_elevated_container_elevation = 0x7f0600fe
co.auter.hcorp:color/m3_sys_color_dynamic_light_on_tertiary_container = 0x7f0501c1
co.auter.hcorp:color/m3_sys_color_dynamic_light_surface_container_high = 0x7f0501cb
co.auter.hcorp:macro/m3_comp_time_input_time_input_field_supporting_text_color = 0x7f0c014a
co.auter.hcorp:dimen/m3_comp_extended_fab_primary_container_elevation = 0x7f06010f
co.auter.hcorp:string/character_counter_overflowed_content_description = 0x7f100049
co.auter.hcorp:macro/m3_comp_navigation_drawer_label_text_type = 0x7f0c0091
co.auter.hcorp:color/m3_sys_color_dynamic_light_surface_container = 0x7f0501ca
co.auter.hcorp:color/m3_sys_color_dynamic_light_surface_bright = 0x7f0501c9
co.auter.hcorp:color/m3_sys_color_dynamic_light_primary = 0x7f0501c4
co.auter.hcorp:color/m3_sys_color_dynamic_light_outline = 0x7f0501c2
co.auter.hcorp:id/fitXY = 0x7f0800c8
co.auter.hcorp:dimen/m3_comp_sheet_bottom_docked_drag_handle_width = 0x7f06017e
co.auter.hcorp:style/Widget.Material3.TabLayout.Secondary = 0x7f110413
co.auter.hcorp:style/Widget.Material3.Button.TextButton.Dialog.Icon = 0x7f110398
co.auter.hcorp:style/TextAppearance.MaterialComponents.Headline4 = 0x7f110216
co.auter.hcorp:drawable/ic_resume = 0x7f0700b3
co.auter.hcorp:color/m3_sys_color_dynamic_light_on_surface_variant = 0x7f0501bf
co.auter.hcorp:color/m3_sys_color_dynamic_light_on_surface = 0x7f0501be
co.auter.hcorp:macro/m3_comp_primary_navigation_tab_active_hover_state_layer_color = 0x7f0c00c8
co.auter.hcorp:attr/suffixTextAppearance = 0x7f0303f2
co.auter.hcorp:color/m3_sys_color_dynamic_light_on_secondary_container = 0x7f0501bd
co.auter.hcorp:style/Base.Theme.MaterialComponents.Bridge = 0x7f110069
co.auter.hcorp:dimen/m3_navigation_rail_item_active_indicator_margin_horizontal = 0x7f0601d0
co.auter.hcorp:color/m3_sys_color_dynamic_light_on_primary = 0x7f0501ba
co.auter.hcorp:id/submit_area = 0x7f0801b8
co.auter.hcorp:color/m3_sys_color_dynamic_light_inverse_surface = 0x7f0501b6
co.auter.hcorp:color/material_dynamic_color_light_error = 0x7f050231
co.auter.hcorp:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Tertiary = 0x7f1103c0
co.auter.hcorp:color/design_dark_default_color_primary_dark = 0x7f05004c
co.auter.hcorp:attr/tickRadiusActive = 0x7f030466
co.auter.hcorp:color/m3_sys_color_dynamic_dark_surface_container_lowest = 0x7f0501ac
co.auter.hcorp:style/Base.TextAppearance.AppCompat.Title = 0x7f110032
co.auter.hcorp:dimen/mtrl_badge_text_size = 0x7f060252
co.auter.hcorp:attr/behavior_hideable = 0x7f030072
co.auter.hcorp:dimen/design_bottom_navigation_label_padding = 0x7f06006a
co.auter.hcorp:anim/rns_slide_in_from_right = 0x7f010048
co.auter.hcorp:attr/colorAccent = 0x7f0300e8
co.auter.hcorp:color/m3_sys_color_dynamic_dark_surface_container_low = 0x7f0501ab
co.auter.hcorp:color/design_default_color_primary_variant = 0x7f05005a
co.auter.hcorp:color/material_personalized_color_primary_text_inverse = 0x7f0502a5
co.auter.hcorp:macro/m3_comp_switch_selected_pressed_state_layer_color = 0x7f0c012c
co.auter.hcorp:color/m3_sys_color_dynamic_dark_surface_container = 0x7f0501a8
co.auter.hcorp:color/m3_sys_color_dynamic_dark_surface_bright = 0x7f0501a7
co.auter.hcorp:color/m3_slider_inactive_track_color_legacy = 0x7f050168
co.auter.hcorp:color/m3_sys_color_dynamic_dark_on_tertiary_container = 0x7f05019f
co.auter.hcorp:style/TextAppearance.AppCompat.Widget.DropDownItem = 0x7f1101ce
co.auter.hcorp:drawable/mtrl_switch_thumb_unchecked_pressed = 0x7f0700ed
co.auter.hcorp:dimen/m3_comp_slider_disabled_active_track_opacity = 0x7f060187
co.auter.hcorp:dimen/browser_actions_context_menu_max_width = 0x7f060053
co.auter.hcorp:color/m3_sys_color_dynamic_dark_on_surface_variant = 0x7f05019d
co.auter.hcorp:anim/rns_ios_from_right_background_close = 0x7f01003e
co.auter.hcorp:color/m3_sys_color_dynamic_dark_on_surface = 0x7f05019c
co.auter.hcorp:color/m3_sys_color_dynamic_dark_on_secondary = 0x7f05019a
co.auter.hcorp:color/m3_sys_color_dynamic_dark_on_error = 0x7f050196
co.auter.hcorp:style/TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f1101cf
co.auter.hcorp:string/androidx.credentials.TYPE_PUBLIC_KEY_CREDENTIAL = 0x7f10001d
co.auter.hcorp:id/accessibility_value = 0x7f080039
co.auter.hcorp:color/m3_sys_color_dynamic_light_tertiary_container = 0x7f0501d2
co.auter.hcorp:drawable/notification_bg_normal_pressed = 0x7f0700f8
co.auter.hcorp:color/m3_sys_color_dynamic_dark_error = 0x7f050190
co.auter.hcorp:color/common_google_signin_btn_text_light_disabled = 0x7f05003e
co.auter.hcorp:dimen/design_bottom_navigation_item_max_width = 0x7f060068
co.auter.hcorp:color/m3_ref_palette_neutral4 = 0x7f05011e
co.auter.hcorp:color/m3_sys_color_dark_tertiary_container = 0x7f05018e
co.auter.hcorp:color/m3_sys_color_dark_surface_dim = 0x7f05018b
co.auter.hcorp:color/m3_sys_color_dark_surface_container_lowest = 0x7f05018a
co.auter.hcorp:dimen/m3_navigation_rail_item_active_indicator_width = 0x7f0601d1
co.auter.hcorp:color/abc_color_highlight_material = 0x7f050004
co.auter.hcorp:color/m3_sys_color_dark_surface_container_highest = 0x7f050188
co.auter.hcorp:attr/backgroundInsetBottom = 0x7f03004e
co.auter.hcorp:color/m3_sys_color_on_secondary_fixed_variant = 0x7f050204
co.auter.hcorp:color/m3_sys_color_dark_surface_container = 0x7f050186
co.auter.hcorp:attr/trackCornerRadius = 0x7f03048c
co.auter.hcorp:color/m3_sys_color_dark_surface_bright = 0x7f050185
co.auter.hcorp:dimen/mtrl_navigation_item_icon_size = 0x7f0602cb
co.auter.hcorp:id/decelerate = 0x7f080094
co.auter.hcorp:color/m3_dynamic_dark_default_color_primary_text = 0x7f05008f
co.auter.hcorp:color/m3_sys_color_dark_surface = 0x7f050184
co.auter.hcorp:dimen/mtrl_alert_dialog_picker_background_inset = 0x7f06024d
co.auter.hcorp:layout/mtrl_picker_dialog = 0x7f0b005c
co.auter.hcorp:color/m3_sys_color_dark_inverse_surface = 0x7f050172
co.auter.hcorp:color/abc_tint_seek_thumb = 0x7f050016
co.auter.hcorp:color/m3_sys_color_dynamic_on_primary_fixed_variant = 0x7f0501d4
co.auter.hcorp:integer/m3_sys_motion_duration_medium2 = 0x7f09001a
co.auter.hcorp:color/m3_sys_color_light_on_background = 0x7f0501e5
co.auter.hcorp:color/m3_sys_color_dark_secondary = 0x7f050182
co.auter.hcorp:attr/textAppearanceHeadline3 = 0x7f030429
co.auter.hcorp:color/m3_textfield_input_text_color = 0x7f050218
co.auter.hcorp:color/m3_sys_color_dark_primary_container = 0x7f050181
co.auter.hcorp:dimen/m3_sys_motion_easing_legacy_control_x2 = 0x7f060206
co.auter.hcorp:color/m3_sys_color_dark_on_surface = 0x7f05017a
co.auter.hcorp:color/m3_sys_color_dynamic_dark_surface = 0x7f0501a6
co.auter.hcorp:color/m3_sys_color_dark_on_secondary_container = 0x7f050179
co.auter.hcorp:color/m3_sys_color_dark_on_secondary = 0x7f050178
co.auter.hcorp:style/Theme.EdgeToEdge.Material3.Dynamic = 0x7f11024d
co.auter.hcorp:drawable/$mtrl_checkbox_button_unchecked_checked__2 = 0x7f07001f
co.auter.hcorp:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__1 = 0x7f07001b
co.auter.hcorp:color/m3_sys_color_dark_on_primary_container = 0x7f050177
co.auter.hcorp:attr/numericModifiers = 0x7f030339
co.auter.hcorp:drawable/$mtrl_checkbox_button_icon_indeterminate_unchecked__1 = 0x7f070015
co.auter.hcorp:style/Base.Widget.MaterialComponents.CheckedTextView = 0x7f110118
co.auter.hcorp:color/m3_sys_color_dark_on_primary = 0x7f050176
co.auter.hcorp:style/Widget.MaterialComponents.NavigationRailView.Colored = 0x7f110474
co.auter.hcorp:color/material_personalized_color_surface_container_high = 0x7f0502ad
co.auter.hcorp:attr/itemSpacing = 0x7f03024d
co.auter.hcorp:dimen/m3_comp_radio_button_unselected_focus_state_layer_opacity = 0x7f06016d
co.auter.hcorp:attr/cornerFamilyTopLeft = 0x7f030143
co.auter.hcorp:color/m3_sys_color_dark_on_error_container = 0x7f050175
co.auter.hcorp:layout/mtrl_calendar_month = 0x7f0b0052
co.auter.hcorp:color/m3_sys_color_dark_on_error = 0x7f050174
co.auter.hcorp:attr/tooltipFrameBackground = 0x7f030481
co.auter.hcorp:dimen/m3_btn_translation_z_base = 0x7f0600e5
co.auter.hcorp:color/m3_sys_color_dark_background = 0x7f05016d
co.auter.hcorp:dimen/m3_back_progress_bottom_container_max_scale_x_distance = 0x7f0600af
co.auter.hcorp:string/catalyst_debug_open = 0x7f100032
co.auter.hcorp:color/m3_slider_inactive_track_color = 0x7f050167
co.auter.hcorp:dimen/m3_ripple_pressed_alpha = 0x7f0601db
co.auter.hcorp:style/Widget.AppCompat.Light.ListView.DropDown = 0x7f110348
co.auter.hcorp:color/m3_slider_halo_color_legacy = 0x7f050166
co.auter.hcorp:color/m3_slider_active_track_color_legacy = 0x7f050165
co.auter.hcorp:dimen/design_navigation_max_width = 0x7f06007e
co.auter.hcorp:dimen/abc_seekbar_track_progress_height_material = 0x7f060039
co.auter.hcorp:color/m3_slider_active_track_color = 0x7f050164
co.auter.hcorp:attr/textAppearanceSmallPopupMenu = 0x7f03043c
co.auter.hcorp:dimen/abc_select_dialog_padding_start_material = 0x7f06003a
co.auter.hcorp:color/m3_selection_control_ripple_color_selector = 0x7f050162
co.auter.hcorp:dimen/m3_side_sheet_width = 0x7f0601eb
co.auter.hcorp:color/m3_ref_palette_tertiary99 = 0x7f050160
co.auter.hcorp:color/switch_thumb_disabled_material_light = 0x7f050315
co.auter.hcorp:dimen/abc_alert_dialog_button_bar_height = 0x7f060010
co.auter.hcorp:color/m3_ref_palette_tertiary95 = 0x7f05015f
co.auter.hcorp:dimen/mtrl_btn_hovered_z = 0x7f060262
co.auter.hcorp:color/m3_ref_palette_tertiary90 = 0x7f05015e
co.auter.hcorp:drawable/abc_edit_text_material = 0x7f07003c
co.auter.hcorp:attr/labelVisibilityMode = 0x7f03025c
co.auter.hcorp:drawable/$mtrl_switch_thumb_pressed_checked__0 = 0x7f070023
co.auter.hcorp:color/m3_ref_palette_tertiary80 = 0x7f05015d
co.auter.hcorp:drawable/autofill_inline_suggestion_chip_background = 0x7f070077
co.auter.hcorp:color/m3_ref_palette_tertiary50 = 0x7f05015a
co.auter.hcorp:color/m3_ref_palette_tertiary20 = 0x7f050157
co.auter.hcorp:color/m3_ref_palette_tertiary100 = 0x7f050156
co.auter.hcorp:drawable/$mtrl_checkbox_button_unchecked_checked__0 = 0x7f07001d
co.auter.hcorp:string/project_id = 0x7f1000e8
co.auter.hcorp:color/m3_ref_palette_tertiary10 = 0x7f050155
co.auter.hcorp:macro/m3_comp_input_chip_container_shape = 0x7f0c005b
co.auter.hcorp:dimen/m3_sys_motion_easing_standard_accelerate_control_y2 = 0x7f060214
co.auter.hcorp:color/m3_ref_palette_secondary99 = 0x7f050153
co.auter.hcorp:dimen/compat_button_inset_horizontal_material = 0x7f060059
co.auter.hcorp:anim/m3_motion_fade_enter = 0x7f010029
co.auter.hcorp:color/m3_ref_palette_secondary95 = 0x7f050152
co.auter.hcorp:dimen/mtrl_navigation_rail_icon_size = 0x7f0602d3
co.auter.hcorp:color/m3_ref_palette_secondary90 = 0x7f050151
co.auter.hcorp:color/m3_ref_palette_secondary80 = 0x7f050150
co.auter.hcorp:dimen/m3_navigation_item_active_indicator_label_padding = 0x7f0601c2
co.auter.hcorp:string/catalyst_reload_button = 0x7f100042
co.auter.hcorp:dimen/abc_text_size_title_material = 0x7f06004f
co.auter.hcorp:id/text_input_start_icon = 0x7f0801d0
co.auter.hcorp:drawable/abc_ic_menu_selectall_mtrl_alpha = 0x7f070046
co.auter.hcorp:color/m3_ref_palette_secondary50 = 0x7f05014d
co.auter.hcorp:color/m3_ref_palette_secondary40 = 0x7f05014c
co.auter.hcorp:attr/flow_verticalGap = 0x7f0301f1
co.auter.hcorp:drawable/abc_list_divider_material = 0x7f07004c
co.auter.hcorp:color/m3_ref_palette_secondary30 = 0x7f05014b
co.auter.hcorp:color/m3_ref_palette_secondary10 = 0x7f050148
co.auter.hcorp:drawable/abc_btn_check_material_anim = 0x7f07002c
co.auter.hcorp:color/m3_ref_palette_primary70 = 0x7f050142
co.auter.hcorp:dimen/m3_small_fab_max_image_size = 0x7f0601ef
co.auter.hcorp:color/material_dynamic_secondary30 = 0x7f050260
co.auter.hcorp:dimen/m3_comp_elevated_card_container_elevation = 0x7f06010d
co.auter.hcorp:color/material_dynamic_neutral_variant80 = 0x7f05024b
co.auter.hcorp:style/Base.Widget.Material3.FloatingActionButton.Large = 0x7f11010f
co.auter.hcorp:id/all = 0x7f080054
co.auter.hcorp:color/m3_ref_palette_primary30 = 0x7f05013e
co.auter.hcorp:color/m3_ref_palette_primary100 = 0x7f05013c
co.auter.hcorp:id/textSpacerNoTitle = 0x7f0801cb
co.auter.hcorp:color/m3_sys_color_dark_error = 0x7f05016e
co.auter.hcorp:color/m3_ref_palette_primary0 = 0x7f05013a
co.auter.hcorp:dimen/m3_comp_slider_stop_indicator_size = 0x7f06018b
co.auter.hcorp:color/m3_ref_palette_neutral_variant99 = 0x7f050139
co.auter.hcorp:color/m3_ref_palette_neutral_variant90 = 0x7f050137
co.auter.hcorp:color/m3_ref_palette_neutral_variant80 = 0x7f050136
co.auter.hcorp:drawable/abc_ic_go_search_api_material = 0x7f070041
co.auter.hcorp:color/m3_ref_palette_neutral_variant60 = 0x7f050134
co.auter.hcorp:attr/titleMarginTop = 0x7f030474
co.auter.hcorp:color/m3_ref_palette_neutral_variant50 = 0x7f050133
co.auter.hcorp:color/m3_ref_palette_neutral_variant30 = 0x7f050131
co.auter.hcorp:style/RtlOverlay.Widget.AppCompat.SearchView.MagIcon = 0x7f110161
co.auter.hcorp:dimen/m3_comp_top_app_bar_small_container_elevation = 0x7f0601ad
co.auter.hcorp:color/m3_ref_palette_neutral_variant0 = 0x7f05012d
co.auter.hcorp:dimen/m3_comp_filled_button_container_elevation = 0x7f060125
co.auter.hcorp:style/Theme.MaterialComponents.Light.NoActionBar = 0x7f1102a0
co.auter.hcorp:id/action_bar_root = 0x7f08003e
co.auter.hcorp:color/m3_ref_palette_neutral99 = 0x7f05012c
co.auter.hcorp:dimen/m3_comp_extended_fab_primary_hover_container_elevation = 0x7f060113
co.auter.hcorp:dimen/abc_dialog_fixed_height_major = 0x7f06001c
co.auter.hcorp:color/m3_ref_palette_neutral96 = 0x7f05012a
co.auter.hcorp:style/Widget.Material3.Button.UnelevatedButton = 0x7f11039d
co.auter.hcorp:dimen/m3_comp_bottom_app_bar_container_elevation = 0x7f060104
co.auter.hcorp:dimen/abc_text_size_display_4_material = 0x7f060046
co.auter.hcorp:color/m3_ref_palette_neutral94 = 0x7f050128
co.auter.hcorp:attr/badgeWithTextShapeAppearanceOverlay = 0x7f030066
co.auter.hcorp:color/mtrl_filled_background_color = 0x7f0502e1
co.auter.hcorp:color/material_on_primary_disabled = 0x7f050284
co.auter.hcorp:color/m3_sys_color_dynamic_light_secondary = 0x7f0501c6
co.auter.hcorp:color/m3_ref_palette_neutral90 = 0x7f050126
co.auter.hcorp:color/m3_ref_palette_neutral87 = 0x7f050125
co.auter.hcorp:color/m3_ref_palette_neutral60 = 0x7f050122
co.auter.hcorp:dimen/mtrl_shape_corner_size_small_component = 0x7f0602e7
co.auter.hcorp:color/m3_ref_palette_neutral6 = 0x7f050121
co.auter.hcorp:macro/m3_comp_filter_chip_label_text_type = 0x7f0c0058
co.auter.hcorp:color/m3_ref_palette_neutral40 = 0x7f05011f
co.auter.hcorp:style/Widget.MaterialComponents.MaterialCalendar.Day.Today = 0x7f11045e
co.auter.hcorp:dimen/m3_comp_extended_fab_primary_hover_state_layer_opacity = 0x7f060114
co.auter.hcorp:attr/progressBarImage = 0x7f03036f
co.auter.hcorp:drawable/$mtrl_checkbox_button_unchecked_checked__1 = 0x7f07001e
co.auter.hcorp:color/m3_ref_palette_neutral30 = 0x7f05011d
co.auter.hcorp:style/Widget.MaterialComponents.Button.TextButton = 0x7f11043d
co.auter.hcorp:id/counterclockwise = 0x7f08008d
co.auter.hcorp:color/m3_ref_palette_neutral22 = 0x7f05011b
co.auter.hcorp:color/m3_ref_palette_neutral12 = 0x7f050118
co.auter.hcorp:drawable/ic_password = 0x7f0700b2
co.auter.hcorp:color/m3_ref_palette_error99 = 0x7f050114
co.auter.hcorp:color/m3_ref_palette_error95 = 0x7f050113
co.auter.hcorp:dimen/m3_searchbar_outlined_stroke_width = 0x7f0601e1
co.auter.hcorp:color/m3_ref_palette_error90 = 0x7f050112
co.auter.hcorp:attr/barLength = 0x7f030068
co.auter.hcorp:color/m3_ref_palette_error80 = 0x7f050111
co.auter.hcorp:color/m3_ref_palette_error70 = 0x7f050110
co.auter.hcorp:mipmap/ic_launcher = 0x7f0d0000
co.auter.hcorp:id/open_search_view_root = 0x7f080150
co.auter.hcorp:drawable/m3_password_eye = 0x7f0700ba
co.auter.hcorp:color/m3_ref_palette_error50 = 0x7f05010e
co.auter.hcorp:style/RtlUnderlay.Widget.AppCompat.ActionButton = 0x7f110162
co.auter.hcorp:dimen/m3_chip_icon_size = 0x7f0600fc
co.auter.hcorp:color/m3_ref_palette_error30 = 0x7f05010c
co.auter.hcorp:color/m3_ref_palette_error20 = 0x7f05010b
co.auter.hcorp:attr/materialAlertDialogTitleIconStyle = 0x7f0302c3
co.auter.hcorp:id/edge = 0x7f0800af
co.auter.hcorp:color/m3_ref_palette_error0 = 0x7f050108
co.auter.hcorp:attr/actionLayout = 0x7f03000d
co.auter.hcorp:dimen/mtrl_extended_fab_translation_z_base = 0x7f0602b6
co.auter.hcorp:attr/tabSelectedTextAppearance = 0x7f030412
co.auter.hcorp:color/m3_ref_palette_dynamic_tertiary70 = 0x7f050103
co.auter.hcorp:string/abc_action_bar_up_description = 0x7f100001
co.auter.hcorp:attr/materialAlertDialogTitlePanelStyle = 0x7f0302c4
co.auter.hcorp:color/m3_ref_palette_dynamic_tertiary60 = 0x7f050102
co.auter.hcorp:color/material_personalized_color_on_primary_container = 0x7f050297
co.auter.hcorp:style/Base.TextAppearance.AppCompat.Subhead = 0x7f110030
co.auter.hcorp:color/m3_ref_palette_dynamic_tertiary50 = 0x7f050101
co.auter.hcorp:anim/rns_no_animation_350 = 0x7f010044
co.auter.hcorp:color/m3_ref_palette_dynamic_tertiary20 = 0x7f0500fe
co.auter.hcorp:drawable/$mtrl_checkbox_button_icon_unchecked_checked__0 = 0x7f070017
co.auter.hcorp:color/mtrl_textinput_hovered_box_stroke_color = 0x7f050300
co.auter.hcorp:color/design_default_color_on_surface = 0x7f050057
co.auter.hcorp:color/m3_ref_palette_dynamic_tertiary10 = 0x7f0500fc
co.auter.hcorp:color/mtrl_outlined_stroke_color = 0x7f0502ef
co.auter.hcorp:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense = 0x7f1102fd
co.auter.hcorp:anim/abc_tooltip_exit = 0x7f01000b
co.auter.hcorp:dimen/mtrl_progress_circular_track_thickness_extra_small = 0x7f0602e0
co.auter.hcorp:color/m3_ref_palette_dynamic_secondary99 = 0x7f0500fa
co.auter.hcorp:color/m3_ref_palette_dynamic_secondary95 = 0x7f0500f9
co.auter.hcorp:attr/itemIconSize = 0x7f03023e
co.auter.hcorp:style/Widget.Material3.ChipGroup = 0x7f1103ac
co.auter.hcorp:id/title = 0x7f0801d8
co.auter.hcorp:color/m3_ref_palette_dynamic_secondary30 = 0x7f0500f2
co.auter.hcorp:id/edit_text_id = 0x7f0800b1
co.auter.hcorp:color/m3_ref_palette_dynamic_secondary20 = 0x7f0500f1
co.auter.hcorp:dimen/mtrl_extended_fab_disabled_elevation = 0x7f0602aa
co.auter.hcorp:style/Widget.Material3.SearchView = 0x7f110403
co.auter.hcorp:style/Base.Theme.AppCompat.Dialog.MinWidth = 0x7f110053
co.auter.hcorp:color/m3_ref_palette_error40 = 0x7f05010d
co.auter.hcorp:attr/actionModeBackground = 0x7f030010
co.auter.hcorp:color/m3_sys_color_dynamic_light_surface_dim = 0x7f0501cf
co.auter.hcorp:attr/layout_anchor = 0x7f030264
co.auter.hcorp:attr/closeIconTint = 0x7f0300d9
co.auter.hcorp:color/m3_ref_palette_dynamic_secondary100 = 0x7f0500f0
co.auter.hcorp:layout/material_clockface_view = 0x7f0b003f
co.auter.hcorp:color/abc_btn_colored_borderless_text_material = 0x7f050002
co.auter.hcorp:color/mtrl_textinput_default_box_stroke_color = 0x7f0502fc
co.auter.hcorp:color/cardview_light_background = 0x7f050030
co.auter.hcorp:color/design_fab_stroke_top_outer_color = 0x7f050065
co.auter.hcorp:color/m3_ref_palette_dynamic_secondary10 = 0x7f0500ef
co.auter.hcorp:style/Theme.EdgeToEdge.Material3.Dynamic.Light.Common = 0x7f110250
co.auter.hcorp:string/material_slider_range_start = 0x7f100098
co.auter.hcorp:color/m3_ref_palette_dynamic_primary99 = 0x7f0500ed
co.auter.hcorp:color/m3_ref_palette_dynamic_primary70 = 0x7f0500e9
co.auter.hcorp:style/Theme.MaterialComponents.Light.Dialog.Alert.Bridge = 0x7f110299
co.auter.hcorp:attr/expandedTitleMarginBottom = 0x7f0301b2
co.auter.hcorp:style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox = 0x7f110315
co.auter.hcorp:style/Theme.EdgeToEdge.Material3 = 0x7f11024b
co.auter.hcorp:attr/actionBarTabTextStyle = 0x7f030008
co.auter.hcorp:dimen/m3_side_sheet_margin_detached = 0x7f0601e8
co.auter.hcorp:color/m3_ref_palette_dynamic_primary60 = 0x7f0500e8
co.auter.hcorp:color/m3_ref_palette_dynamic_primary40 = 0x7f0500e6
co.auter.hcorp:attr/actionProviderClass = 0x7f030021
co.auter.hcorp:color/m3_ref_palette_dynamic_primary20 = 0x7f0500e4
co.auter.hcorp:color/m3_ref_palette_secondary0 = 0x7f050147
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral0 = 0x7f0500b1
co.auter.hcorp:attr/subtitleTextAppearance = 0x7f0303ee
co.auter.hcorp:color/m3_card_stroke_color = 0x7f050080
co.auter.hcorp:color/m3_ref_palette_dynamic_primary100 = 0x7f0500e3
co.auter.hcorp:attr/colorOnSurfaceInverse = 0x7f030100
co.auter.hcorp:attr/floatingActionButtonSmallPrimaryStyle = 0x7f0301d9
co.auter.hcorp:color/m3_ref_palette_dynamic_primary10 = 0x7f0500e2
co.auter.hcorp:style/MaterialAlertDialog.Material3.Body.Text = 0x7f110132
co.auter.hcorp:dimen/m3_comp_outlined_text_field_disabled_input_text_opacity = 0x7f060158
co.auter.hcorp:attr/actionModeCutDrawable = 0x7f030015
co.auter.hcorp:color/m3_ref_palette_dynamic_primary0 = 0x7f0500e1
co.auter.hcorp:color/m3_ref_palette_dynamic_tertiary100 = 0x7f0500fd
co.auter.hcorp:drawable/abc_scrubber_control_to_pressed_mtrl_005 = 0x7f07005f
co.auter.hcorp:dimen/abc_star_big = 0x7f06003b
co.auter.hcorp:integer/m3_sys_motion_duration_short3 = 0x7f09001f
co.auter.hcorp:color/material_personalized_color_surface_container_highest = 0x7f0502ae
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral_variant99 = 0x7f0500e0
co.auter.hcorp:drawable/abc_control_background_material = 0x7f07003a
co.auter.hcorp:id/path = 0x7f08015f
co.auter.hcorp:color/m3_slider_thumb_color = 0x7f050169
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral_variant95 = 0x7f0500dd
co.auter.hcorp:drawable/btn_radio_off_to_on_mtrl_animation = 0x7f07007f
co.auter.hcorp:attr/tintMode = 0x7f03046a
co.auter.hcorp:attr/layout_constraintGuide_end = 0x7f030277
co.auter.hcorp:color/m3_ref_palette_tertiary30 = 0x7f050158
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral_variant92 = 0x7f0500db
co.auter.hcorp:attr/passwordToggleEnabled = 0x7f030352
co.auter.hcorp:dimen/m3_slider_thumb_elevation = 0x7f0601ee
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral_variant60 = 0x7f0500d6
co.auter.hcorp:attr/flow_lastHorizontalBias = 0x7f0301e9
co.auter.hcorp:attr/state_above_anchor = 0x7f0303d8
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral98 = 0x7f0500c7
co.auter.hcorp:style/ThemeOverlay.Material3.DynamicColors.Dark = 0x7f1102d1
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral_variant6 = 0x7f0500d5
co.auter.hcorp:style/TextAppearance.Material3.SearchView.Prefix = 0x7f110209
co.auter.hcorp:color/m3_ref_palette_neutral_variant95 = 0x7f050138
co.auter.hcorp:id/navigation_bar_item_labels_group = 0x7f08013b
co.auter.hcorp:attr/checkedIcon = 0x7f0300af
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral_variant4 = 0x7f0500d2
co.auter.hcorp:attr/progressBarStyle = 0x7f030372
co.auter.hcorp:dimen/mtrl_chip_text_size = 0x7f0602a5
co.auter.hcorp:attr/checkedTextViewStyle = 0x7f0300b7
co.auter.hcorp:dimen/m3_comp_slider_active_handle_width = 0x7f060186
co.auter.hcorp:attr/logo = 0x7f0302b8
co.auter.hcorp:id/honorRequest = 0x7f0800de
co.auter.hcorp:attr/layout_constraintHorizontal_chainStyle = 0x7f03027e
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral_variant24 = 0x7f0500d0
co.auter.hcorp:macro/m3_comp_navigation_bar_inactive_hover_state_layer_color = 0x7f0c0071
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral_variant22 = 0x7f0500cf
co.auter.hcorp:attr/fontProviderQuery = 0x7f0301fb
co.auter.hcorp:attr/customBoolean = 0x7f030157
co.auter.hcorp:color/design_default_color_error = 0x7f050052
co.auter.hcorp:drawable/avd_show_password = 0x7f070079
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral_variant20 = 0x7f0500ce
co.auter.hcorp:attr/roundBottomStart = 0x7f03038a
co.auter.hcorp:drawable/mtrl_switch_track = 0x7f0700ee
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral_variant17 = 0x7f0500cd
co.auter.hcorp:attr/tickMarkTint = 0x7f030464
co.auter.hcorp:color/material_harmonized_color_on_error = 0x7f05027f
co.auter.hcorp:attr/colorOnContainerUnchecked = 0x7f0300f3
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral95 = 0x7f0500c5
co.auter.hcorp:macro/m3_comp_navigation_bar_active_pressed_state_layer_color = 0x7f0c006a
co.auter.hcorp:color/m3_sys_color_light_surface_container_lowest = 0x7f0501fc
co.auter.hcorp:attr/backgroundTint = 0x7f030055
co.auter.hcorp:attr/retryImage = 0x7f030381
co.auter.hcorp:style/ShapeAppearance.M3.Comp.NavigationRail.Container.Shape = 0x7f11016d
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral92 = 0x7f0500c3
co.auter.hcorp:integer/show_password_duration = 0x7f090045
co.auter.hcorp:attr/materialCircleRadius = 0x7f0302dc
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral90 = 0x7f0500c2
co.auter.hcorp:attr/simpleItemLayout = 0x7f0303bc
co.auter.hcorp:attr/badgeRadius = 0x7f030059
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral87 = 0x7f0500c1
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral70 = 0x7f0500bf
co.auter.hcorp:style/RtlOverlay.Widget.AppCompat.PopupMenuItem.Shortcut = 0x7f110158
co.auter.hcorp:animator/mtrl_extended_fab_change_size_expand_motion_spec = 0x7f02001a
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral30 = 0x7f0500b9
co.auter.hcorp:style/MaterialAlertDialog.MaterialComponents = 0x7f11013a
co.auter.hcorp:dimen/mtrl_btn_text_btn_padding_right = 0x7f060271
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral20 = 0x7f0500b6
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral17 = 0x7f0500b5
co.auter.hcorp:color/m3_radiobutton_ripple_tint = 0x7f0500af
co.auter.hcorp:id/material_hour_text_input = 0x7f080107
co.auter.hcorp:dimen/m3_navigation_rail_item_padding_top_with_large_font = 0x7f0601d6
co.auter.hcorp:layout/mtrl_calendar_month_labeled = 0x7f0b0053
co.auter.hcorp:id/icon_only = 0x7f0800e1
co.auter.hcorp:color/m3_ref_palette_primary60 = 0x7f050141
co.auter.hcorp:attr/minTouchTargetSize = 0x7f030300
co.auter.hcorp:attr/motionDurationShort3 = 0x7f030317
co.auter.hcorp:attr/boxBackgroundColor = 0x7f030081
co.auter.hcorp:styleable/MaterialAlertDialogTheme = 0x7f12004f
co.auter.hcorp:color/m3_primary_text_disable_only = 0x7f0500ad
co.auter.hcorp:id/dark = 0x7f080092
co.auter.hcorp:attr/fontProviderAuthority = 0x7f0301f6
co.auter.hcorp:color/m3_filled_icon_button_container_color_selector = 0x7f05009e
co.auter.hcorp:color/m3_fab_efab_foreground_color_selector = 0x7f05009c
co.auter.hcorp:color/material_on_primary_emphasis_medium = 0x7f050286
co.auter.hcorp:style/ShapeAppearance.M3.Comp.SearchView.FullScreen.Container.Shape = 0x7f110170
co.auter.hcorp:color/m3_dynamic_hint_foreground = 0x7f050097
co.auter.hcorp:id/deltaRelative = 0x7f080098
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral_variant12 = 0x7f0500cc
co.auter.hcorp:color/m3_dynamic_dark_hint_foreground = 0x7f050092
co.auter.hcorp:integer/m3_sys_motion_duration_extra_long2 = 0x7f090012
co.auter.hcorp:attr/actionModeSplitBackground = 0x7f03001b
co.auter.hcorp:drawable/notification_bg = 0x7f0700f3
co.auter.hcorp:id/showCustom = 0x7f080198
co.auter.hcorp:attr/appBarLayoutStyle = 0x7f030036
co.auter.hcorp:style/Theme.Material3.DynamicColors.Light.NoActionBar = 0x7f11026c
co.auter.hcorp:color/m3_dynamic_highlighted_text = 0x7f050096
co.auter.hcorp:color/m3_dark_hint_foreground = 0x7f05008b
co.auter.hcorp:color/m3_dark_default_color_secondary_text = 0x7f050089
co.auter.hcorp:string/material_minute_suffix = 0x7f100091
co.auter.hcorp:id/disablePostScroll = 0x7f0800a2
co.auter.hcorp:color/m3_dark_default_color_primary_text = 0x7f050088
co.auter.hcorp:style/ShapeAppearance.M3.Comp.BottomAppBar.Container.Shape = 0x7f110166
co.auter.hcorp:macro/m3_comp_outlined_card_hover_outline_color = 0x7f0c00ad
co.auter.hcorp:color/m3_chip_text_color = 0x7f050087
co.auter.hcorp:id/labeled = 0x7f0800ef
co.auter.hcorp:drawable/abc_list_selector_holo_dark = 0x7f070056
co.auter.hcorp:color/m3_ref_palette_dynamic_tertiary40 = 0x7f050100
co.auter.hcorp:attr/materialButtonToggleGroupStyle = 0x7f0302c8
co.auter.hcorp:color/m3_chip_ripple_color = 0x7f050085
co.auter.hcorp:color/material_on_surface_emphasis_high_type = 0x7f050288
co.auter.hcorp:drawable/mtrl_ic_error = 0x7f0700df
co.auter.hcorp:macro/m3_comp_navigation_rail_active_focus_state_layer_color = 0x7f0c0093
co.auter.hcorp:color/m3_checkbox_button_tint = 0x7f050082
co.auter.hcorp:style/Widget.Material3.PopupMenu = 0x7f1103fb
co.auter.hcorp:color/m3_calendar_item_stroke_color = 0x7f05007d
co.auter.hcorp:style/Base.Theme.Material3.Dark.Dialog.FixedSize = 0x7f11005f
co.auter.hcorp:animator/m3_extended_fab_show_motion_spec = 0x7f020013
co.auter.hcorp:color/m3_button_outline_color_selector = 0x7f050079
co.auter.hcorp:attr/buttonStyleSmall = 0x7f03009b
co.auter.hcorp:attr/ratingBarStyleIndicator = 0x7f030379
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral4 = 0x7f0500ba
co.auter.hcorp:id/container = 0x7f080087
co.auter.hcorp:id/barrier = 0x7f080063
co.auter.hcorp:drawable/mtrl_popupmenu_background_overlay = 0x7f0700e3
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral80 = 0x7f0500c0
co.auter.hcorp:dimen/m3_btn_text_btn_icon_padding_left = 0x7f0600e1
co.auter.hcorp:id/accessibility_actions = 0x7f080010
co.auter.hcorp:color/m3_assist_chip_stroke_color = 0x7f050075
co.auter.hcorp:style/Widget.MaterialComponents.MaterialCalendar.HeaderCancelButton = 0x7f110462
co.auter.hcorp:attr/itemShapeFillColor = 0x7f030248
co.auter.hcorp:anim/abc_slide_in_top = 0x7f010007
co.auter.hcorp:attr/keyPositionType = 0x7f030256
co.auter.hcorp:style/Widget.MaterialComponents.Button.OutlinedButton = 0x7f11043b
co.auter.hcorp:color/foreground_material_dark = 0x7f05006e
co.auter.hcorp:attr/colorBackgroundFloating = 0x7f0300e9
co.auter.hcorp:style/Widget.MaterialComponents.MaterialCalendar.HeaderSelection.Fullscreen = 0x7f110468
co.auter.hcorp:dimen/m3_comp_search_view_container_elevation = 0x7f060176
co.auter.hcorp:color/error_color_material_light = 0x7f05006d
co.auter.hcorp:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f110037
co.auter.hcorp:color/material_grey_800 = 0x7f05027a
co.auter.hcorp:attr/liftOnScroll = 0x7f0302a2
co.auter.hcorp:attr/failureImageScaleType = 0x7f0301cb
co.auter.hcorp:color/m3_ref_palette_dynamic_tertiary90 = 0x7f050105
co.auter.hcorp:id/notification_main_column = 0x7f080144
co.auter.hcorp:id/listMode = 0x7f0800f9
co.auter.hcorp:attr/trackStopIndicatorSize = 0x7f030492
co.auter.hcorp:color/error_color_material_dark = 0x7f05006c
co.auter.hcorp:color/dim_foreground_material_light = 0x7f05006b
co.auter.hcorp:attr/buttonBarNegativeButtonStyle = 0x7f03008e
co.auter.hcorp:drawable/$avd_hide_password__0 = 0x7f070000
co.auter.hcorp:macro/m3_comp_primary_navigation_tab_inactive_hover_state_layer_color = 0x7f0c00cd
co.auter.hcorp:attr/content = 0x7f03012c
co.auter.hcorp:style/Theme.EdgeToEdge.Material3.Dynamic.DayNight.Common = 0x7f11024e
co.auter.hcorp:style/Theme.Design = 0x7f11023d
co.auter.hcorp:attr/colorPrimaryFixedDim = 0x7f03010c
co.auter.hcorp:color/design_error = 0x7f05005e
co.auter.hcorp:style/Base.Widget.AppCompat.Spinner.Underlined = 0x7f1100fd
co.auter.hcorp:dimen/design_bottom_navigation_text_size = 0x7f06006d
co.auter.hcorp:color/m3_sys_color_dynamic_dark_outline = 0x7f0501a0
co.auter.hcorp:style/Widget.MaterialComponents.TimePicker.Display.Divider = 0x7f110497
co.auter.hcorp:style/Widget.AppCompat.SeekBar.Discrete = 0x7f11035d
co.auter.hcorp:attr/thumbIconTint = 0x7f030456
co.auter.hcorp:color/design_default_color_primary = 0x7f050058
co.auter.hcorp:style/TextAppearance.MaterialComponents.Headline6 = 0x7f110218
co.auter.hcorp:style/Base.TextAppearance.AppCompat.Menu = 0x7f11002a
co.auter.hcorp:bool/windowLightSystemBars = 0x7f040003
co.auter.hcorp:dimen/m3_navigation_menu_divider_horizontal_padding = 0x7f0601ca
co.auter.hcorp:color/design_default_color_secondary_variant = 0x7f05005c
co.auter.hcorp:color/design_dark_default_color_surface = 0x7f050050
co.auter.hcorp:attr/boxCornerRadiusBottomEnd = 0x7f030084
co.auter.hcorp:color/design_dark_default_color_on_surface = 0x7f05004a
co.auter.hcorp:styleable/AppCompatSeekBar = 0x7f12000f
co.auter.hcorp:color/m3_efab_ripple_color_selector = 0x7f050099
co.auter.hcorp:layout/mtrl_alert_dialog_title = 0x7f0b0049
co.auter.hcorp:color/design_dark_default_color_on_primary = 0x7f050048
co.auter.hcorp:attr/layout_goneMarginStart = 0x7f03029a
co.auter.hcorp:attr/yearTodayStyle = 0x7f0304bf
co.auter.hcorp:dimen/m3_comp_switch_track_height = 0x7f06019a
co.auter.hcorp:id/action_bar_subtitle = 0x7f080040
co.auter.hcorp:color/m3_ref_palette_neutral80 = 0x7f050124
co.auter.hcorp:color/common_google_signin_btn_text_light_focused = 0x7f05003f
co.auter.hcorp:attr/tabStyle = 0x7f030414
co.auter.hcorp:macro/m3_comp_elevated_button_container_color = 0x7f0c0029
co.auter.hcorp:attr/iconStartPadding = 0x7f030222
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral22 = 0x7f0500b7
co.auter.hcorp:attr/removeEmbeddedFabElevation = 0x7f030380
co.auter.hcorp:color/common_google_signin_btn_text_dark = 0x7f050037
co.auter.hcorp:style/ShapeAppearanceOverlay.MaterialComponents.Chip = 0x7f11019b
co.auter.hcorp:dimen/m3_carousel_gone_size = 0x7f0600f2
co.auter.hcorp:color/colorPrimaryDark = 0x7f050036
co.auter.hcorp:color/colorPrimary = 0x7f050035
co.auter.hcorp:macro/m3_comp_checkbox_selected_disabled_icon_color = 0x7f0c0008
co.auter.hcorp:color/material_dynamic_secondary0 = 0x7f05025c
co.auter.hcorp:anim/abc_slide_out_bottom = 0x7f010008
co.auter.hcorp:anim/rns_ios_from_left_background_close = 0x7f01003a
co.auter.hcorp:attr/textAppearanceBodySmall = 0x7f030421
co.auter.hcorp:style/TextAppearance.Widget.AppCompat.Toolbar.Title = 0x7f110220
co.auter.hcorp:color/catalyst_redbox_background = 0x7f050034
co.auter.hcorp:string/spinbutton_description = 0x7f1000f2
co.auter.hcorp:attr/submitBackground = 0x7f0303eb
co.auter.hcorp:attr/collapsingToolbarLayoutLargeStyle = 0x7f0300e3
co.auter.hcorp:color/cardview_dark_background = 0x7f05002f
co.auter.hcorp:color/background_material_dark = 0x7f05001f
co.auter.hcorp:dimen/m3_comp_badge_size = 0x7f060103
co.auter.hcorp:dimen/abc_action_bar_content_inset_material = 0x7f060000
co.auter.hcorp:style/TextAppearance.M3.Sys.Typescale.TitleLarge = 0x7f1101f5
co.auter.hcorp:color/call_notification_decline_color = 0x7f05002e
co.auter.hcorp:attr/cardCornerRadius = 0x7f03009f
co.auter.hcorp:color/button_material_light = 0x7f05002c
co.auter.hcorp:color/bright_foreground_material_light = 0x7f050026
co.auter.hcorp:style/Base.Theme.AppCompat.Light.DarkActionBar = 0x7f110056
co.auter.hcorp:color/bright_foreground_material_dark = 0x7f050025
co.auter.hcorp:drawable/ic_clock_black_24dp = 0x7f0700a6
co.auter.hcorp:color/bright_foreground_inverse_material_light = 0x7f050024
co.auter.hcorp:dimen/m3_btn_padding_left = 0x7f0600dd
co.auter.hcorp:attr/css = 0x7f030152
co.auter.hcorp:dimen/design_fab_elevation = 0x7f060072
co.auter.hcorp:color/material_blue_grey_950 = 0x7f050228
co.auter.hcorp:color/bright_foreground_inverse_material_dark = 0x7f050023
co.auter.hcorp:dimen/m3_bottom_nav_item_padding_bottom = 0x7f0600c2
co.auter.hcorp:drawable/abc_tab_indicator_mtrl_alpha = 0x7f07006c
co.auter.hcorp:color/m3_sys_color_dark_surface_container_high = 0x7f050187
co.auter.hcorp:color/bright_foreground_disabled_material_light = 0x7f050022
co.auter.hcorp:anim/abc_shrink_fade_out_from_bottom = 0x7f010005
co.auter.hcorp:macro/m3_comp_outlined_card_container_shape = 0x7f0c00a9
co.auter.hcorp:attr/path_percent = 0x7f030356
co.auter.hcorp:macro/m3_comp_menu_container_color = 0x7f0c005d
co.auter.hcorp:color/bright_foreground_disabled_material_dark = 0x7f050021
co.auter.hcorp:color/background_floating_material_light = 0x7f05001e
co.auter.hcorp:attr/backgroundOverlayColorAlpha = 0x7f030052
co.auter.hcorp:attr/checkedIconVisible = 0x7f0300b5
co.auter.hcorp:attr/layout_insetEdge = 0x7f03029c
co.auter.hcorp:color/background_floating_material_dark = 0x7f05001d
co.auter.hcorp:style/RtlOverlay.DialogWindowTitle.AppCompat = 0x7f110153
co.auter.hcorp:attr/itemBackground = 0x7f030239
co.auter.hcorp:color/androidx_core_ripple_material_light = 0x7f05001b
co.auter.hcorp:drawable/abc_btn_default_mtrl_shape = 0x7f070030
co.auter.hcorp:dimen/m3_comp_extended_fab_primary_pressed_state_layer_opacity = 0x7f060117
co.auter.hcorp:dimen/abc_control_corner_material = 0x7f060018
co.auter.hcorp:id/material_clock_level = 0x7f080103
co.auter.hcorp:color/accent_material_dark = 0x7f050019
co.auter.hcorp:color/abc_tint_switch_track = 0x7f050018
co.auter.hcorp:macro/m3_comp_fab_tertiary_icon_color = 0x7f0c0040
co.auter.hcorp:dimen/mtrl_btn_max_width = 0x7f060267
co.auter.hcorp:anim/abc_popup_enter = 0x7f010003
co.auter.hcorp:attr/shapeAppearanceCornerSmall = 0x7f0303a8
co.auter.hcorp:color/abc_search_url_text_selected = 0x7f050010
co.auter.hcorp:drawable/mtrl_checkbox_button_icon_indeterminate_checked = 0x7f0700d2
co.auter.hcorp:id/view_tag_native_id = 0x7f0801f5
co.auter.hcorp:color/abc_tint_edittext = 0x7f050015
co.auter.hcorp:color/material_dynamic_neutral60 = 0x7f05023c
co.auter.hcorp:color/m3_sys_color_dynamic_light_inverse_on_surface = 0x7f0501b4
co.auter.hcorp:style/ShapeAppearance.Material3.LargeComponent = 0x7f110184
co.auter.hcorp:attr/thumbStrokeColor = 0x7f030459
co.auter.hcorp:color/m3_ref_palette_primary80 = 0x7f050143
co.auter.hcorp:integer/google_play_services_version = 0x7f090009
co.auter.hcorp:color/abc_search_url_text_pressed = 0x7f05000f
co.auter.hcorp:color/abc_search_url_text_normal = 0x7f05000e
co.auter.hcorp:integer/material_motion_duration_short_2 = 0x7f09002d
co.auter.hcorp:id/progress_horizontal = 0x7f080169
co.auter.hcorp:dimen/mtrl_calendar_month_horizontal_padding = 0x7f06028c
co.auter.hcorp:color/m3_sys_color_dark_on_surface_variant = 0x7f05017b
co.auter.hcorp:color/design_fab_shadow_end_color = 0x7f05005f
co.auter.hcorp:attr/tickColorActive = 0x7f030461
co.auter.hcorp:color/abc_search_url_text = 0x7f05000d
co.auter.hcorp:color/abc_primary_text_disable_only_material_light = 0x7f05000a
co.auter.hcorp:color/abc_hint_foreground_material_light = 0x7f050008
co.auter.hcorp:attr/currentState = 0x7f030153
co.auter.hcorp:styleable/ExtendedFloatingActionButton_Behavior_Layout = 0x7f120032
co.auter.hcorp:color/abc_decor_view_status_guard = 0x7f050005
co.auter.hcorp:color/cardview_shadow_end_color = 0x7f050031
co.auter.hcorp:macro/m3_comp_text_button_label_text_type = 0x7f0c0145
co.auter.hcorp:macro/m3_comp_radio_button_disabled_selected_icon_color = 0x7f0c00d6
co.auter.hcorp:color/abc_background_cache_hint_selector_material_light = 0x7f050001
co.auter.hcorp:color/m3_ref_palette_neutral10 = 0x7f050116
co.auter.hcorp:color/m3_navigation_bar_item_with_indicator_label_tint = 0x7f0500a3
co.auter.hcorp:style/TextAppearance.Material3.BodySmall = 0x7f1101fc
co.auter.hcorp:attr/tabTextAppearance = 0x7f030415
co.auter.hcorp:bool/mtrl_btn_textappearance_all_caps = 0x7f040002
co.auter.hcorp:bool/abc_action_bar_embed_tabs = 0x7f040000
co.auter.hcorp:color/material_personalized_color_error = 0x7f050291
co.auter.hcorp:attr/shapeAppearance = 0x7f0303a3
co.auter.hcorp:id/touch_outside = 0x7f0801de
co.auter.hcorp:attr/motion_postLayoutCollision = 0x7f03032b
co.auter.hcorp:color/m3_ref_palette_black = 0x7f0500b0
co.auter.hcorp:attr/homeLayout = 0x7f030219
co.auter.hcorp:dimen/m3_sys_motion_easing_standard_decelerate_control_y2 = 0x7f06021c
co.auter.hcorp:attr/windowSplashScreenIconBackgroundColor = 0x7f0304bc
co.auter.hcorp:attr/endIconTintMode = 0x7f03019d
co.auter.hcorp:attr/windowNoTitle = 0x7f0304b8
co.auter.hcorp:string/fab_transformation_sheet_behavior = 0x7f10006a
co.auter.hcorp:drawable/abc_ab_share_pack_mtrl_alpha = 0x7f070028
co.auter.hcorp:dimen/mtrl_extended_fab_elevation = 0x7f0602ac
co.auter.hcorp:attr/windowMinWidthMinor = 0x7f0304b7
co.auter.hcorp:attr/windowFixedWidthMinor = 0x7f0304b5
co.auter.hcorp:attr/editTextBackground = 0x7f03018d
co.auter.hcorp:attr/windowFixedWidthMajor = 0x7f0304b4
co.auter.hcorp:attr/windowFixedHeightMinor = 0x7f0304b3
co.auter.hcorp:attr/limitBoundsTo = 0x7f0302a5
co.auter.hcorp:dimen/m3_btn_elevation = 0x7f0600d3
co.auter.hcorp:attr/reverseLayout = 0x7f030383
co.auter.hcorp:color/m3_ref_palette_dynamic_primary80 = 0x7f0500ea
co.auter.hcorp:attr/windowFixedHeightMajor = 0x7f0304b2
co.auter.hcorp:style/Widget.Material3.MaterialDivider.Heavy = 0x7f1103ed
co.auter.hcorp:macro/m3_comp_primary_navigation_tab_with_icon_inactive_icon_color = 0x7f0c00d0
co.auter.hcorp:attr/behavior_halfExpandedRatio = 0x7f030071
co.auter.hcorp:attr/marginTopSystemWindowInsets = 0x7f0302bf
co.auter.hcorp:attr/windowActionBarOverlay = 0x7f0304b0
co.auter.hcorp:attr/windowActionBar = 0x7f0304af
co.auter.hcorp:attr/waveOffset = 0x7f0304ab
co.auter.hcorp:dimen/m3_comp_fab_primary_large_icon_size = 0x7f06011f
co.auter.hcorp:style/Base.TextAppearance.AppCompat.Widget.ActionBar.Title = 0x7f110038
co.auter.hcorp:attr/waveDecay = 0x7f0304aa
co.auter.hcorp:id/filter = 0x7f0800c2
co.auter.hcorp:anim/rns_ios_from_right_foreground_open = 0x7f010041
co.auter.hcorp:drawable/ic_call_answer_low = 0x7f0700a0
co.auter.hcorp:macro/m3_comp_secondary_navigation_tab_pressed_state_layer_color = 0x7f0c0101
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral_variant96 = 0x7f0500de
co.auter.hcorp:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f110043
co.auter.hcorp:color/abc_tint_spinner = 0x7f050017
co.auter.hcorp:attr/touchRegionId = 0x7f030487
co.auter.hcorp:attr/warmth = 0x7f0304a9
co.auter.hcorp:attr/visibilityMode = 0x7f0304a7
co.auter.hcorp:color/material_dynamic_neutral20 = 0x7f050238
co.auter.hcorp:attr/colorPrimaryDark = 0x7f03010a
co.auter.hcorp:style/Widget.MaterialComponents.NavigationView = 0x7f110478
co.auter.hcorp:macro/m3_comp_date_picker_modal_container_shape = 0x7f0c000e
co.auter.hcorp:animator/mtrl_chip_state_list_anim = 0x7f020018
co.auter.hcorp:attr/viewInflaterClass = 0x7f0304a6
co.auter.hcorp:attr/colorTertiaryContainer = 0x7f030122
co.auter.hcorp:attr/trackColorInactive = 0x7f03048b
co.auter.hcorp:attr/viewAspectRatio = 0x7f0304a5
co.auter.hcorp:style/Widget.MaterialComponents.TabLayout = 0x7f110483
co.auter.hcorp:attr/verticalOffsetWithText = 0x7f0304a4
co.auter.hcorp:style/Theme.AppCompat.Light.Dialog = 0x7f110233
co.auter.hcorp:dimen/m3_btn_padding_top = 0x7f0600df
co.auter.hcorp:color/material_dynamic_tertiary90 = 0x7f050273
co.auter.hcorp:dimen/m3_btn_max_width = 0x7f0600db
co.auter.hcorp:attr/flow_lastVerticalBias = 0x7f0301eb
co.auter.hcorp:attr/colorOnTertiary = 0x7f030102
co.auter.hcorp:attr/startIconScaleType = 0x7f0303d5
co.auter.hcorp:style/Widget.Material3.MaterialCalendar.Fullscreen = 0x7f1103dc
co.auter.hcorp:attr/triggerSlack = 0x7f03049d
co.auter.hcorp:color/material_personalized_color_on_surface_variant = 0x7f05029c
co.auter.hcorp:string/mtrl_picker_announce_current_range_selection = 0x7f1000b5
co.auter.hcorp:attr/triggerReceiver = 0x7f03049c
co.auter.hcorp:string/call_notification_hang_up_action = 0x7f10002a
co.auter.hcorp:attr/triggerId = 0x7f03049b
co.auter.hcorp:dimen/abc_dialog_fixed_width_minor = 0x7f06001f
co.auter.hcorp:attr/chipIconTint = 0x7f0300bf
co.auter.hcorp:attr/titleEnabled = 0x7f03046f
co.auter.hcorp:color/material_personalized_color_text_primary_inverse_disable_only = 0x7f0502b8
co.auter.hcorp:color/m3_sys_color_dynamic_light_background = 0x7f0501b1
co.auter.hcorp:attr/transitionPathRotate = 0x7f030499
co.auter.hcorp:anim/rns_ios_from_right_background_open = 0x7f01003f
co.auter.hcorp:attr/itemActiveIndicatorStyle = 0x7f030238
co.auter.hcorp:dimen/mtrl_chip_pressed_translation_z = 0x7f0602a4
co.auter.hcorp:attr/transitionDisable = 0x7f030496
co.auter.hcorp:color/m3_ref_palette_dynamic_primary50 = 0x7f0500e7
co.auter.hcorp:color/m3_sys_color_dynamic_light_error = 0x7f0501b2
co.auter.hcorp:attr/trackTintMode = 0x7f030495
co.auter.hcorp:anim/fragment_fast_out_extra_slow_in = 0x7f010022
co.auter.hcorp:dimen/mtrl_calendar_bottom_padding = 0x7f060277
co.auter.hcorp:attr/hideMotionSpec = 0x7f030210
co.auter.hcorp:attr/trackTint = 0x7f030494
co.auter.hcorp:dimen/m3_appbar_scrim_height_trigger = 0x7f0600a9
co.auter.hcorp:anim/btn_radio_to_on_mtrl_ring_outer_animation = 0x7f010016
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral6 = 0x7f0500bd
co.auter.hcorp:macro/m3_comp_navigation_drawer_inactive_label_text_color = 0x7f0c008d
co.auter.hcorp:dimen/abc_switch_padding = 0x7f06003e
co.auter.hcorp:macro/m3_comp_fab_secondary_icon_color = 0x7f0c003c
co.auter.hcorp:attr/trackHeight = 0x7f030490
co.auter.hcorp:color/material_personalized_color_text_secondary_and_tertiary_inverse_disabled = 0x7f0502ba
co.auter.hcorp:attr/subheaderInsetStart = 0x7f0303e9
co.auter.hcorp:id/role = 0x7f08017d
co.auter.hcorp:attr/chipStrokeColor = 0x7f0300c8
co.auter.hcorp:attr/trackDecorationTintMode = 0x7f03048f
co.auter.hcorp:style/Base.V21.ThemeOverlay.Material3.BottomSheetDialog = 0x7f1100b0
co.auter.hcorp:drawable/redbox_top_border_background = 0x7f070101
co.auter.hcorp:attr/trackDecorationTint = 0x7f03048e
co.auter.hcorp:attr/colorOnPrimaryFixed = 0x7f0300f8
co.auter.hcorp:attr/colorTertiaryFixed = 0x7f030123
co.auter.hcorp:dimen/m3_comp_outlined_button_disabled_outline_opacity = 0x7f060151
co.auter.hcorp:attr/colorOnTertiaryFixedVariant = 0x7f030105
co.auter.hcorp:attr/flow_horizontalBias = 0x7f0301e6
co.auter.hcorp:macro/m3_comp_switch_disabled_unselected_track_color = 0x7f0c011e
co.auter.hcorp:color/button_material_dark = 0x7f05002b
co.auter.hcorp:attr/touchAnchorId = 0x7f030485
co.auter.hcorp:color/material_personalized_color_control_normal = 0x7f050290
co.auter.hcorp:drawable/common_google_signin_btn_icon_dark_focused = 0x7f070084
co.auter.hcorp:id/material_timepicker_view = 0x7f080111
co.auter.hcorp:dimen/m3_comp_filter_chip_with_icon_icon_size = 0x7f060132
co.auter.hcorp:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle.Inverse = 0x7f1101c3
co.auter.hcorp:attr/tooltipStyle = 0x7f030482
co.auter.hcorp:id/skipCollapsed = 0x7f08019c
co.auter.hcorp:color/abc_decor_view_status_guard_light = 0x7f050006
co.auter.hcorp:attr/toolbarSurfaceStyle = 0x7f03047f
co.auter.hcorp:attr/motionTarget = 0x7f03032a
co.auter.hcorp:color/design_snackbar_background_color = 0x7f050067
co.auter.hcorp:dimen/design_navigation_icon_size = 0x7f06007a
co.auter.hcorp:attr/toggleCheckedStateOnClick = 0x7f03047b
co.auter.hcorp:attr/titleMargin = 0x7f030470
co.auter.hcorp:attr/listPreferredItemPaddingLeft = 0x7f0302b5
co.auter.hcorp:drawable/btn_radio_off_mtrl = 0x7f07007e
co.auter.hcorp:animator/fragment_close_exit = 0x7f020004
co.auter.hcorp:id/edit_query = 0x7f0800b0
co.auter.hcorp:dimen/abc_dialog_list_padding_top_no_title = 0x7f060021
co.auter.hcorp:attr/titleCentered = 0x7f03046d
co.auter.hcorp:id/arc = 0x7f080058
co.auter.hcorp:attr/tickVisible = 0x7f030468
co.auter.hcorp:color/m3_sys_color_light_surface_container_low = 0x7f0501fb
co.auter.hcorp:color/m3_navigation_rail_item_with_indicator_label_tint = 0x7f0500aa
co.auter.hcorp:id/split_action_bar = 0x7f0801a6
co.auter.hcorp:attr/buttonIcon = 0x7f030094
co.auter.hcorp:color/material_deep_teal_500 = 0x7f05022b
co.auter.hcorp:attr/chipMinHeight = 0x7f0300c1
co.auter.hcorp:dimen/m3_comp_primary_navigation_tab_active_focus_state_layer_opacity = 0x7f06015d
co.auter.hcorp:color/m3_ref_palette_neutral_variant40 = 0x7f050132
co.auter.hcorp:attr/tickColor = 0x7f030460
co.auter.hcorp:macro/m3_comp_navigation_rail_inactive_focus_state_layer_color = 0x7f0c009a
co.auter.hcorp:attr/thumbWidth = 0x7f03045f
co.auter.hcorp:attr/thumbTintMode = 0x7f03045d
co.auter.hcorp:styleable/ActionBarLayout = 0x7f120001
co.auter.hcorp:attr/colorSurfaceContainerLow = 0x7f03011b
co.auter.hcorp:attr/thumbTint = 0x7f03045c
co.auter.hcorp:id/disableScroll = 0x7f0800a3
co.auter.hcorp:attr/region_heightLessThan = 0x7f03037c
co.auter.hcorp:style/Theme.MaterialComponents.Light.Dialog.FixedSize = 0x7f11029b
co.auter.hcorp:attr/thumbStrokeWidth = 0x7f03045a
co.auter.hcorp:attr/materialIconButtonFilledStyle = 0x7f0302e1
co.auter.hcorp:color/m3_ref_palette_error10 = 0x7f050109
co.auter.hcorp:id/action_context_bar = 0x7f080043
co.auter.hcorp:color/material_timepicker_button_stroke = 0x7f0502c6
co.auter.hcorp:color/m3_ref_palette_dynamic_secondary70 = 0x7f0500f6
co.auter.hcorp:attr/layout_scrollEffect = 0x7f03029f
co.auter.hcorp:attr/drawerArrowStyle = 0x7f030185
co.auter.hcorp:color/m3_ref_palette_neutral24 = 0x7f05011c
co.auter.hcorp:color/m3_ref_palette_dynamic_secondary90 = 0x7f0500f8
co.auter.hcorp:attr/thumbRadius = 0x7f030458
co.auter.hcorp:id/m3_side_sheet = 0x7f0800fb
co.auter.hcorp:id/item_touch_helper_previous_elevation = 0x7f0800ec
co.auter.hcorp:dimen/mtrl_calendar_navigation_bottom_padding = 0x7f06028e
co.auter.hcorp:color/mtrl_choice_chip_text_color = 0x7f0502dc
co.auter.hcorp:id/textTop = 0x7f0801cd
co.auter.hcorp:color/material_dynamic_neutral_variant10 = 0x7f050243
co.auter.hcorp:attr/thumbIcon = 0x7f030454
co.auter.hcorp:attr/materialCalendarDay = 0x7f0302c9
co.auter.hcorp:attr/lStar = 0x7f030259
co.auter.hcorp:attr/motion_triggerOnCollision = 0x7f03032c
co.auter.hcorp:id/indeterminate = 0x7f0800e6
co.auter.hcorp:attr/thumbElevation = 0x7f030452
co.auter.hcorp:attr/thickness = 0x7f030450
co.auter.hcorp:dimen/mtrl_progress_circular_inset = 0x7f0602d7
co.auter.hcorp:color/m3_ref_palette_dynamic_secondary50 = 0x7f0500f4
co.auter.hcorp:color/material_on_background_emphasis_high_type = 0x7f050282
co.auter.hcorp:color/m3_dark_highlighted_text = 0x7f05008a
co.auter.hcorp:macro/m3_comp_sheet_bottom_docked_container_color = 0x7f0c0104
co.auter.hcorp:attr/theme = 0x7f03044f
co.auter.hcorp:attr/errorAccessibilityLiveRegion = 0x7f0301a4
co.auter.hcorp:attr/tabRippleColor = 0x7f030410
co.auter.hcorp:id/custom = 0x7f08008f
co.auter.hcorp:attr/textInputStyle = 0x7f03044c
co.auter.hcorp:id/SHIFT = 0x7f080007
co.auter.hcorp:attr/thumbColor = 0x7f030451
co.auter.hcorp:attr/textInputLayoutFocusedRectEnabled = 0x7f030448
co.auter.hcorp:attr/itemPadding = 0x7f030242
co.auter.hcorp:style/Widget.MaterialComponents.FloatingActionButton = 0x7f110456
co.auter.hcorp:attr/selectorSize = 0x7f0303a2
co.auter.hcorp:attr/textInputFilledStyle = 0x7f030447
co.auter.hcorp:attr/textInputFilledDenseStyle = 0x7f030445
co.auter.hcorp:style/Base.AlertDialog.AppCompat = 0x7f11000e
co.auter.hcorp:color/design_fab_shadow_start_color = 0x7f050061
co.auter.hcorp:style/Base.Theme.MaterialComponents.Dialog.MinWidth = 0x7f11006f
co.auter.hcorp:attr/round = 0x7f030385
co.auter.hcorp:attr/textEndPadding = 0x7f030444
co.auter.hcorp:style/ThemeOverlay.Material3.SideSheetDialog = 0x7f1102eb
co.auter.hcorp:attr/subtitleTextColor = 0x7f0303ef
co.auter.hcorp:attr/textColorAlertDialogListItem = 0x7f030442
co.auter.hcorp:style/Base.v27.Theme.SplashScreen = 0x7f110127
co.auter.hcorp:drawable/abc_list_selector_holo_light = 0x7f070057
co.auter.hcorp:attr/sizePercent = 0x7f0303c3
co.auter.hcorp:attr/textAppearanceTitleMedium = 0x7f030440
co.auter.hcorp:style/ThemeOverlay.MaterialComponents.AutoCompleteTextView.FilledBox.Dense = 0x7f1102fb
co.auter.hcorp:attr/panelMenuListWidth = 0x7f03034f
co.auter.hcorp:attr/badgeHeight = 0x7f030058
co.auter.hcorp:attr/tickMarkTintMode = 0x7f030465
co.auter.hcorp:color/background_material_light = 0x7f050020
co.auter.hcorp:style/Widget.MaterialComponents.MaterialCalendar.HeaderConfirmButton = 0x7f110463
co.auter.hcorp:string/clear_text_end_icon_content_description = 0x7f10004b
co.auter.hcorp:attr/textAppearanceSubtitle2 = 0x7f03043e
co.auter.hcorp:style/Widget.AppCompat.ListMenuView = 0x7f11034d
co.auter.hcorp:attr/dragDirection = 0x7f030178
co.auter.hcorp:attr/dropDownBackgroundTint = 0x7f030188
co.auter.hcorp:color/material_dynamic_tertiary80 = 0x7f050272
co.auter.hcorp:attr/textAppearanceSubtitle1 = 0x7f03043d
co.auter.hcorp:string/mtrl_exceed_max_badge_number_content_description = 0x7f1000b1
co.auter.hcorp:attr/mock_showDiagonals = 0x7f030306
co.auter.hcorp:dimen/mtrl_navigation_item_icon_padding = 0x7f0602ca
co.auter.hcorp:attr/searchIcon = 0x7f03039b
co.auter.hcorp:macro/m3_comp_primary_navigation_tab_active_pressed_state_layer_color = 0x7f0c00ca
co.auter.hcorp:dimen/abc_text_size_caption_material = 0x7f060042
co.auter.hcorp:color/design_default_color_on_primary = 0x7f050055
co.auter.hcorp:attr/textAppearanceLineHeightEnabled = 0x7f030434
co.auter.hcorp:attr/textAppearanceTitleLarge = 0x7f03043f
co.auter.hcorp:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_1 = 0x7f0a0001
co.auter.hcorp:attr/itemShapeInsetTop = 0x7f03024c
co.auter.hcorp:attr/textAppearanceLabelSmall = 0x7f030432
co.auter.hcorp:style/Widget.MaterialComponents.Chip.Action = 0x7f110447
co.auter.hcorp:style/Widget.MaterialComponents.BottomAppBar = 0x7f110431
co.auter.hcorp:style/Widget.Material3.BottomSheet.Modal = 0x7f11038a
co.auter.hcorp:id/checked = 0x7f08007d
co.auter.hcorp:attr/textAppearanceLabelMedium = 0x7f030431
co.auter.hcorp:id/material_minute_tv = 0x7f08010b
co.auter.hcorp:attr/textAppearanceHeadlineSmall = 0x7f03042f
co.auter.hcorp:layout/mtrl_picker_header_dialog = 0x7f0b005e
co.auter.hcorp:color/material_personalized_color_control_highlight = 0x7f05028f
co.auter.hcorp:anim/m3_side_sheet_exit_to_right = 0x7f01002e
co.auter.hcorp:color/design_default_color_on_error = 0x7f050054
co.auter.hcorp:macro/m3_comp_sheet_side_docked_standard_container_color = 0x7f0c010a
co.auter.hcorp:id/beginning = 0x7f080066
co.auter.hcorp:attr/textAppearanceHeadline6 = 0x7f03042c
co.auter.hcorp:color/material_cursor_color = 0x7f050229
co.auter.hcorp:attr/helperText = 0x7f03020b
co.auter.hcorp:style/Theme.EdgeToEdge.DayNight.Common = 0x7f110244
co.auter.hcorp:attr/contentPaddingRight = 0x7f030138
co.auter.hcorp:attr/textAppearanceHeadline4 = 0x7f03042a
co.auter.hcorp:style/Base.Widget.AppCompat.Light.PopupMenu.Overflow = 0x7f1100ea
co.auter.hcorp:attr/mock_labelBackgroundColor = 0x7f030304
co.auter.hcorp:animator/m3_extended_fab_state_list_animator = 0x7f020014
co.auter.hcorp:attr/floatingActionButtonTertiaryStyle = 0x7f0301e0
co.auter.hcorp:style/Widget.AppCompat.RatingBar = 0x7f110357
co.auter.hcorp:attr/textAppearanceButton = 0x7f030422
co.auter.hcorp:styleable/SwitchCompat = 0x7f120089
co.auter.hcorp:attr/actionViewClass = 0x7f030023
co.auter.hcorp:layout/mtrl_picker_text_input_date_range = 0x7f0b0064
co.auter.hcorp:attr/telltales_tailColor = 0x7f030419
co.auter.hcorp:attr/textAppearanceHeadline5 = 0x7f03042b
co.auter.hcorp:attr/popupMenuBackground = 0x7f030363
co.auter.hcorp:attr/targetId = 0x7f030418
co.auter.hcorp:color/material_dynamic_neutral80 = 0x7f05023e
co.auter.hcorp:color/material_blue_grey_900 = 0x7f050227
co.auter.hcorp:color/m3_navigation_rail_item_with_indicator_icon_tint = 0x7f0500a9
co.auter.hcorp:attr/singleLine = 0x7f0303c1
co.auter.hcorp:style/Widget.Material3.AppBarLayout = 0x7f11037b
co.auter.hcorp:id/glide_custom_view_target_tag = 0x7f0800d3
co.auter.hcorp:color/iconBackground = 0x7f050072
co.auter.hcorp:dimen/m3_large_text_vertical_offset_adjustment = 0x7f0601be
co.auter.hcorp:dimen/m3_comp_time_picker_container_elevation = 0x7f0601a3
co.auter.hcorp:style/ShapeAppearance.Material3.Corner.Large = 0x7f110180
co.auter.hcorp:attr/tabPaddingBottom = 0x7f03040c
co.auter.hcorp:color/m3_sys_color_dynamic_dark_inverse_primary = 0x7f050193
co.auter.hcorp:attr/tabMode = 0x7f03040a
co.auter.hcorp:macro/m3_comp_time_picker_period_selector_selected_pressed_state_layer_color = 0x7f0c0159
co.auter.hcorp:attr/tabInlineLabel = 0x7f030407
co.auter.hcorp:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox = 0x7f11048e
co.auter.hcorp:color/m3_ref_palette_neutral95 = 0x7f050129
co.auter.hcorp:attr/textAllCaps = 0x7f03041c
co.auter.hcorp:attr/helperTextEnabled = 0x7f03020c
co.auter.hcorp:macro/m3_comp_outlined_card_pressed_outline_color = 0x7f0c00af
co.auter.hcorp:attr/tabIndicatorColor = 0x7f030403
co.auter.hcorp:style/Widget.Material3.Button.IconButton.Filled.Tonal = 0x7f110391
co.auter.hcorp:color/m3_sys_color_light_background = 0x7f0501df
co.auter.hcorp:attr/goIcon = 0x7f030206
co.auter.hcorp:attr/labelBehavior = 0x7f03025a
co.auter.hcorp:attr/materialSearchViewToolbarStyle = 0x7f0302e9
co.auter.hcorp:drawable/tooltip_frame_light = 0x7f070108
co.auter.hcorp:color/design_dark_default_color_primary_variant = 0x7f05004d
co.auter.hcorp:style/Widget.AppCompat.ActionBar.TabBar = 0x7f11031f
co.auter.hcorp:attr/errorIconTintMode = 0x7f0301a9
co.auter.hcorp:color/m3_sys_color_dynamic_on_tertiary_fixed = 0x7f0501d7
co.auter.hcorp:style/Base.V14.ThemeOverlay.MaterialComponents.Dialog = 0x7f1100a3
co.auter.hcorp:attr/layout_constraintBaseline_creator = 0x7f03026b
co.auter.hcorp:styleable/AppCompatTextView = 0x7f120011
co.auter.hcorp:attr/tabIndicatorAnimationMode = 0x7f030402
co.auter.hcorp:style/Widget.MaterialComponents.Button.TextButton.Snackbar = 0x7f110442
co.auter.hcorp:drawable/abc_textfield_search_default_mtrl_alpha = 0x7f070074
co.auter.hcorp:drawable/abc_switch_track_mtrl_alpha = 0x7f07006a
co.auter.hcorp:style/ThemeOverlay.Material3.Button.IconButton = 0x7f1102c2
co.auter.hcorp:macro/m3_comp_search_bar_hover_state_layer_color = 0x7f0c00e7
co.auter.hcorp:attr/actualImageUri = 0x7f030028
co.auter.hcorp:attr/tabIconTint = 0x7f0303fe
co.auter.hcorp:dimen/mtrl_calendar_selection_baseline_to_top_fullscreen = 0x7f060292
co.auter.hcorp:attr/allowStacking = 0x7f03002e
co.auter.hcorp:dimen/mtrl_extended_fab_start_padding = 0x7f0602b3
co.auter.hcorp:dimen/m3_sys_motion_easing_linear_control_x2 = 0x7f06020e
co.auter.hcorp:style/ThemeOverlay.MaterialComponents.Dialog = 0x7f110304
co.auter.hcorp:attr/dialogTheme = 0x7f03016e
co.auter.hcorp:attr/tabMaxWidth = 0x7f030408
co.auter.hcorp:drawable/mtrl_switch_thumb_pressed = 0x7f0700e8
co.auter.hcorp:attr/tabContentStart = 0x7f0303fc
co.auter.hcorp:style/TextAppearance.AppCompat.Widget.Button.Borderless.Colored = 0x7f1101cb
co.auter.hcorp:style/AlertDialog.AppCompat.Light = 0x7f110001
co.auter.hcorp:attr/tabBackground = 0x7f0303fb
co.auter.hcorp:attr/switchTextAppearance = 0x7f0303fa
co.auter.hcorp:attr/switchMinWidth = 0x7f0303f7
co.auter.hcorp:style/Base.Widget.Material3.TabLayout = 0x7f110114
co.auter.hcorp:attr/cornerSizeBottomRight = 0x7f030148
co.auter.hcorp:attr/waveVariesBy = 0x7f0304ae
co.auter.hcorp:attr/buttonBarStyle = 0x7f030091
co.auter.hcorp:drawable/mtrl_ic_check_mark = 0x7f0700dc
co.auter.hcorp:attr/swipeRefreshLayoutProgressSpinnerBackgroundColor = 0x7f0303f6
co.auter.hcorp:style/Base.Theme.Material3.Light.BottomSheetDialog = 0x7f110063
co.auter.hcorp:macro/m3_comp_time_picker_headline_type = 0x7f0c0151
co.auter.hcorp:dimen/mtrl_extended_fab_translation_z_hovered_focused = 0x7f0602b7
co.auter.hcorp:color/m3_ref_palette_tertiary40 = 0x7f050159
co.auter.hcorp:attr/textColorSearchUrl = 0x7f030443
co.auter.hcorp:attr/layout_constraintHeight_percent = 0x7f03027c
co.auter.hcorp:id/left = 0x7f0800f2
co.auter.hcorp:attr/textInputOutlinedStyle = 0x7f03044b
co.auter.hcorp:attr/suggestionRowLayout = 0x7f0303f4
co.auter.hcorp:attr/suffixTextColor = 0x7f0303f3
co.auter.hcorp:macro/m3_comp_elevated_card_container_shape = 0x7f0c002b
co.auter.hcorp:id/mtrl_calendar_frame = 0x7f080123
co.auter.hcorp:attr/onShow = 0x7f03033f
co.auter.hcorp:id/src_atop = 0x7f0801aa
co.auter.hcorp:attr/subtitleTextStyle = 0x7f0303f0
co.auter.hcorp:string/catalyst_debug_open_disabled = 0x7f100033
co.auter.hcorp:id/progress_circular = 0x7f080168
co.auter.hcorp:dimen/mtrl_slider_label_square_side = 0x7f0602eb
co.auter.hcorp:style/TextAppearance.AppCompat.Small.Inverse = 0x7f1101bb
co.auter.hcorp:style/ShapeAppearance.MaterialComponents = 0x7f110189
co.auter.hcorp:attr/telltales_tailScale = 0x7f03041a
co.auter.hcorp:attr/autofillInlineSuggestionTitle = 0x7f030049
co.auter.hcorp:attr/subtitle = 0x7f0303ec
co.auter.hcorp:style/ThemeOverlay.Material3.DayNight.SideSheetDialog = 0x7f1102cd
co.auter.hcorp:id/mtrl_picker_header_title_and_selection = 0x7f080130
co.auter.hcorp:color/dim_foreground_disabled_material_light = 0x7f050069
co.auter.hcorp:attr/chipStartPadding = 0x7f0300c7
co.auter.hcorp:animator/m3_elevated_chip_state_list_anim = 0x7f02000f
co.auter.hcorp:attr/motionDurationExtraLong4 = 0x7f03030c
co.auter.hcorp:attr/subheaderTextAppearance = 0x7f0303ea
co.auter.hcorp:attr/deltaPolarAngle = 0x7f030169
co.auter.hcorp:attr/materialCardViewOutlinedStyle = 0x7f0302da
co.auter.hcorp:dimen/mtrl_navigation_rail_active_text_size = 0x7f0602ce
co.auter.hcorp:color/abc_tint_btn_checkable = 0x7f050013
co.auter.hcorp:attr/cornerFamilyBottomLeft = 0x7f030141
co.auter.hcorp:attr/subheaderColor = 0x7f0303e7
co.auter.hcorp:attr/strokeWidth = 0x7f0303e5
co.auter.hcorp:id/fade = 0x7f0800bd
co.auter.hcorp:attr/strokeColor = 0x7f0303e4
co.auter.hcorp:drawable/abc_list_selector_background_transition_holo_light = 0x7f070053
co.auter.hcorp:style/Widget.Material3.CircularProgressIndicator.Medium = 0x7f1103b3
co.auter.hcorp:attr/statusBarScrim = 0x7f0303e3
co.auter.hcorp:drawable/navigation_empty_icon = 0x7f0700f1
co.auter.hcorp:style/Base.Widget.AppCompat.ListView.DropDown = 0x7f1100ee
co.auter.hcorp:attr/statusBarBackground = 0x7f0303e1
co.auter.hcorp:style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout.Fullscreen = 0x7f110466
co.auter.hcorp:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog = 0x7f110309
co.auter.hcorp:raw/firebase_common_keep = 0x7f0f0000
co.auter.hcorp:drawable/mtrl_dialog_background = 0x7f0700d7
co.auter.hcorp:color/mtrl_text_btn_text_color_selector = 0x7f0502fb
co.auter.hcorp:id/view_tree_view_model_store_owner = 0x7f0801f9
co.auter.hcorp:attr/tabIndicatorGravity = 0x7f030405
co.auter.hcorp:animator/m3_btn_state_list_anim = 0x7f02000b
co.auter.hcorp:anim/design_snackbar_in = 0x7f010020
co.auter.hcorp:attr/singleSelection = 0x7f0303c2
co.auter.hcorp:attr/state_lifted = 0x7f0303df
co.auter.hcorp:id/action_divider = 0x7f080044
co.auter.hcorp:color/m3_ref_palette_primary90 = 0x7f050144
co.auter.hcorp:attr/cardMaxElevation = 0x7f0300a2
co.auter.hcorp:macro/m3_comp_date_picker_modal_date_selected_container_color = 0x7f0c0010
co.auter.hcorp:id/transition_pause_alpha = 0x7f0801e7
co.auter.hcorp:color/m3_sys_color_dynamic_dark_tertiary_container = 0x7f0501b0
co.auter.hcorp:attr/yearSelectedStyle = 0x7f0304bd
co.auter.hcorp:style/Widget.Material3.CheckedTextView = 0x7f1103a1
co.auter.hcorp:color/m3_simple_item_ripple_color = 0x7f050163
co.auter.hcorp:attr/state_liftable = 0x7f0303de
co.auter.hcorp:style/Widget.MaterialComponents.TimePicker.Display.TextInputLayout = 0x7f11049a
co.auter.hcorp:dimen/m3_comp_text_button_focus_state_layer_opacity = 0x7f06019f
co.auter.hcorp:color/m3_card_foreground_color = 0x7f05007e
co.auter.hcorp:styleable/ConstraintSet = 0x7f12002b
co.auter.hcorp:attr/state_error = 0x7f0303dc
co.auter.hcorp:attr/titleTextEllipsize = 0x7f030479
co.auter.hcorp:style/Widget.Material3.LinearProgressIndicator = 0x7f1103d2
co.auter.hcorp:dimen/m3_comp_secondary_navigation_tab_active_indicator_height = 0x7f060179
co.auter.hcorp:attr/cornerSizeTopRight = 0x7f03014a
co.auter.hcorp:style/TextAppearance.AppCompat.Widget.Button.Inverse = 0x7f1101cd
co.auter.hcorp:id/outward = 0x7f080157
co.auter.hcorp:drawable/$m3_avd_hide_password__0 = 0x7f070006
co.auter.hcorp:attr/chipIconEnabled = 0x7f0300bd
co.auter.hcorp:color/material_dynamic_primary40 = 0x7f050254
co.auter.hcorp:attr/autoSizeTextType = 0x7f030043
co.auter.hcorp:attr/startIconTint = 0x7f0303d6
co.auter.hcorp:drawable/mtrl_navigation_bar_item_background = 0x7f0700e1
co.auter.hcorp:attr/listLayout = 0x7f0302ae
co.auter.hcorp:styleable/AppCompatImageView = 0x7f12000e
co.auter.hcorp:attr/staggered = 0x7f0303d0
co.auter.hcorp:color/primary_dark_material_dark = 0x7f050305
co.auter.hcorp:attr/splitTrack = 0x7f0303cd
co.auter.hcorp:attr/actionBarTabBarStyle = 0x7f030006
co.auter.hcorp:attr/seekBarStyle = 0x7f03039e
co.auter.hcorp:id/mtrl_internal_children_alpha_tag = 0x7f08012b
co.auter.hcorp:attr/spinBars = 0x7f0303c9
co.auter.hcorp:style/Base.Widget.AppCompat.AutoCompleteTextView = 0x7f1100d2
co.auter.hcorp:attr/drawableLeftCompat = 0x7f03017e
co.auter.hcorp:attr/spanCount = 0x7f0303c8
co.auter.hcorp:attr/flow_maxElementsWrap = 0x7f0301ed
co.auter.hcorp:id/design_menu_item_action_area = 0x7f08009a
co.auter.hcorp:attr/singleChoiceItemLayout = 0x7f0303c0
co.auter.hcorp:attr/simpleItems = 0x7f0303bf
co.auter.hcorp:integer/m3_card_anim_duration_ms = 0x7f09000f
co.auter.hcorp:dimen/mtrl_btn_z = 0x7f060273
co.auter.hcorp:color/m3_navigation_rail_ripple_color_selector = 0x7f0500ab
co.auter.hcorp:color/browser_actions_divider_color = 0x7f050028
co.auter.hcorp:attr/sideSheetModalStyle = 0x7f0303bb
co.auter.hcorp:attr/showAsAction = 0x7f0303b1
co.auter.hcorp:string/bottomsheet_drag_handle_clicked = 0x7f100025
co.auter.hcorp:macro/m3_comp_radio_button_disabled_unselected_icon_color = 0x7f0c00d7
co.auter.hcorp:attr/colorSecondaryFixedDim = 0x7f030114
co.auter.hcorp:attr/autofillInlineSuggestionEndIconStyle = 0x7f030046
co.auter.hcorp:styleable/ViewStubCompat = 0x7f120099
co.auter.hcorp:attr/shapeAppearanceSmallComponent = 0x7f0303ac
co.auter.hcorp:dimen/m3_back_progress_main_container_min_edge_gap = 0x7f0600b2
co.auter.hcorp:attr/shapeAppearanceCornerExtraSmall = 0x7f0303a5
co.auter.hcorp:attr/shapeAppearanceCornerExtraLarge = 0x7f0303a4
co.auter.hcorp:styleable/RecyclerView = 0x7f120075
co.auter.hcorp:style/Widget.Material3.LinearProgressIndicator.Legacy = 0x7f1103d3
co.auter.hcorp:dimen/notification_large_icon_height = 0x7f060313
co.auter.hcorp:dimen/abc_text_size_button_material = 0x7f060041
co.auter.hcorp:attr/selectionRequired = 0x7f0303a1
co.auter.hcorp:attr/searchViewStyle = 0x7f03039d
co.auter.hcorp:style/ThemeOverlay.MaterialComponents.TextInputEditText.OutlinedBox.Dense = 0x7f110316
co.auter.hcorp:color/abc_primary_text_material_light = 0x7f05000c
co.auter.hcorp:color/m3_ref_palette_secondary60 = 0x7f05014e
co.auter.hcorp:attr/scrimVisibleHeightTrigger = 0x7f030399
co.auter.hcorp:style/Widget.Autofill.InlineSuggestionChip = 0x7f110367
co.auter.hcorp:attr/scrimBackground = 0x7f030398
co.auter.hcorp:layout/notification_template_part_time = 0x7f0b0075
co.auter.hcorp:drawable/material_ic_keyboard_arrow_right_black_24dp = 0x7f0700c9
co.auter.hcorp:attr/textAppearanceCaption = 0x7f030423
co.auter.hcorp:color/material_slider_halo_color = 0x7f0502c1
co.auter.hcorp:attr/textAppearanceDisplayMedium = 0x7f030425
co.auter.hcorp:attr/checkedIconSize = 0x7f0300b3
co.auter.hcorp:attr/actionModeCopyDrawable = 0x7f030014
co.auter.hcorp:attr/scrimAnimationDuration = 0x7f030397
co.auter.hcorp:attr/saturation = 0x7f030395
co.auter.hcorp:string/common_google_play_services_enable_title = 0x7f100051
co.auter.hcorp:attr/iconPadding = 0x7f030220
co.auter.hcorp:color/m3_ref_palette_dynamic_tertiary30 = 0x7f0500ff
co.auter.hcorp:attr/boxStrokeErrorColor = 0x7f030089
co.auter.hcorp:attr/roundedCornerRadius = 0x7f030391
co.auter.hcorp:style/Widget.Material3.MaterialCalendar.Year = 0x7f1103e8
co.auter.hcorp:color/common_google_signin_btn_text_light = 0x7f05003c
co.auter.hcorp:style/Widget.Material3.MaterialCalendar.Year.Selected = 0x7f1103e9
co.auter.hcorp:color/m3_popupmenu_overlay_color = 0x7f0500ac
co.auter.hcorp:color/m3_sys_color_dynamic_light_primary_container = 0x7f0501c5
co.auter.hcorp:attr/roundTopRight = 0x7f03038e
co.auter.hcorp:layout/ime_base_split_test_activity = 0x7f0b0032
co.auter.hcorp:attr/roundTopLeft = 0x7f03038d
co.auter.hcorp:attr/roundBottomRight = 0x7f030389
co.auter.hcorp:animator/mtrl_fab_transformation_sheet_collapse_spec = 0x7f020020
co.auter.hcorp:dimen/mtrl_calendar_days_of_week_height = 0x7f06027f
co.auter.hcorp:attr/roundBottomLeft = 0x7f030388
co.auter.hcorp:macro/m3_comp_sheet_side_docked_modal_container_shape = 0x7f0c0109
co.auter.hcorp:attr/forceApplySystemWindowInsetTop = 0x7f030200
co.auter.hcorp:attr/region_widthMoreThan = 0x7f03037f
co.auter.hcorp:attr/thumbHeight = 0x7f030453
co.auter.hcorp:drawable/material_ic_calendar_black_24dp = 0x7f0700c3
co.auter.hcorp:attr/colorOnPrimary = 0x7f0300f6
co.auter.hcorp:attr/region_heightMoreThan = 0x7f03037d
co.auter.hcorp:style/Platform.ThemeOverlay.AppCompat.Light = 0x7f11014d
co.auter.hcorp:color/m3_radiobutton_button_tint = 0x7f0500ae
co.auter.hcorp:dimen/m3_navigation_item_shape_inset_bottom = 0x7f0601c5
co.auter.hcorp:style/Widget.MaterialComponents.PopupMenu.ContextMenu = 0x7f11047a
co.auter.hcorp:dimen/m3_bottom_nav_item_active_indicator_height = 0x7f0600bf
co.auter.hcorp:attr/recyclerViewStyle = 0x7f03037b
co.auter.hcorp:attr/ratingBarStyleSmall = 0x7f03037a
co.auter.hcorp:macro/m3_comp_search_bar_container_color = 0x7f0c00e6
co.auter.hcorp:dimen/m3_sys_motion_easing_emphasized_decelerate_control_x2 = 0x7f0601fe
co.auter.hcorp:dimen/m3_comp_switch_unselected_focus_state_layer_opacity = 0x7f06019c
co.auter.hcorp:attr/rangeFillColor = 0x7f030377
co.auter.hcorp:string/call_notification_screening_text = 0x7f10002d
co.auter.hcorp:attr/radioButtonStyle = 0x7f030376
co.auter.hcorp:layout/abc_activity_chooser_view_list_item = 0x7f0b0007
co.auter.hcorp:attr/queryPatterns = 0x7f030375
co.auter.hcorp:color/m3_sys_color_light_inverse_on_surface = 0x7f0501e2
co.auter.hcorp:color/m3_button_foreground_color_selector = 0x7f050078
co.auter.hcorp:attr/queryHint = 0x7f030374
co.auter.hcorp:dimen/m3_badge_with_text_horizontal_offset = 0x7f0600ba
co.auter.hcorp:layout/notification_template_big_media_narrow_custom = 0x7f0b006e
co.auter.hcorp:color/common_google_signin_btn_text_dark_pressed = 0x7f05003b
co.auter.hcorp:color/m3_sys_color_dynamic_dark_inverse_on_surface = 0x7f050192
co.auter.hcorp:styleable/ShapeableImageView = 0x7f12007c
co.auter.hcorp:styleable/Constraint = 0x7f120028
co.auter.hcorp:macro/m3_comp_dialog_headline_type = 0x7f0c0025
co.auter.hcorp:integer/cancel_button_image_alpha = 0x7f090004
co.auter.hcorp:attr/motionDurationLong1 = 0x7f03030d
co.auter.hcorp:attr/colorOnSurfaceVariant = 0x7f030101
co.auter.hcorp:attr/progressBarAutoRotateInterval = 0x7f03036e
co.auter.hcorp:string/mtrl_picker_invalid_format_example = 0x7f1000c0
co.auter.hcorp:dimen/material_helper_text_default_padding_top = 0x7f060240
co.auter.hcorp:attr/placeholderText = 0x7f03035f
co.auter.hcorp:style/Widget.MaterialComponents.Button.TextButton.Icon = 0x7f110441
co.auter.hcorp:color/m3_sys_color_dynamic_dark_primary_container = 0x7f0501a3
co.auter.hcorp:attr/pressedStateOverlayImage = 0x7f03036c
co.auter.hcorp:id/TOP_END = 0x7f08000c
co.auter.hcorp:color/material_personalized_color_secondary = 0x7f0502a6
co.auter.hcorp:style/Widget.AppCompat.Button.Small = 0x7f11032d
co.auter.hcorp:attr/preserveIconSpacing = 0x7f03036b
co.auter.hcorp:macro/m3_comp_time_picker_period_selector_container_shape = 0x7f0c0152
co.auter.hcorp:dimen/material_font_2_0_box_collapsed_padding_top = 0x7f06023f
co.auter.hcorp:color/m3_sys_color_secondary_fixed_dim = 0x7f05020a
co.auter.hcorp:drawable/mtrl_ic_arrow_drop_up = 0x7f0700da
co.auter.hcorp:style/ShapeAppearance.M3.Comp.Switch.StateLayer.Shape = 0x7f110173
co.auter.hcorp:id/open_search_view_search_prefix = 0x7f080152
co.auter.hcorp:attr/pressedTranslationZ = 0x7f03036d
co.auter.hcorp:attr/popupWindowStyle = 0x7f030366
co.auter.hcorp:attr/itemMinHeight = 0x7f030241
co.auter.hcorp:style/TextAppearance.MaterialComponents.Headline3 = 0x7f110215
co.auter.hcorp:macro/m3_comp_time_input_time_input_field_label_text_color = 0x7f0c0149
co.auter.hcorp:attr/popupTheme = 0x7f030365
co.auter.hcorp:style/Widget.MaterialComponents.ShapeableImageView = 0x7f11047e
co.auter.hcorp:attr/popupMenuStyle = 0x7f030364
co.auter.hcorp:attr/placeholderTextAppearance = 0x7f030360
co.auter.hcorp:attr/pivotAnchor = 0x7f03035c
co.auter.hcorp:attr/perpendicularPath_percent = 0x7f03035b
co.auter.hcorp:attr/percentX = 0x7f030359
co.auter.hcorp:attr/itemFillColor = 0x7f03023a
co.auter.hcorp:attr/contentDescription = 0x7f03012d
co.auter.hcorp:attr/percentWidth = 0x7f030358
co.auter.hcorp:attr/fabAnimationMode = 0x7f0301c3
co.auter.hcorp:attr/coordinatorLayoutStyle = 0x7f03013e
co.auter.hcorp:attr/layout_constraintLeft_toRightOf = 0x7f030282
co.auter.hcorp:string/common_open_on_phone = 0x7f10005e
co.auter.hcorp:attr/pathMotionArc = 0x7f030355
co.auter.hcorp:attr/percentY = 0x7f03035a
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral_variant80 = 0x7f0500d8
co.auter.hcorp:attr/passwordToggleTint = 0x7f030353
co.auter.hcorp:attr/roundTopEnd = 0x7f03038c
co.auter.hcorp:attr/passwordToggleDrawable = 0x7f030351
co.auter.hcorp:attr/menu = 0x7f0302fa
co.auter.hcorp:anim/btn_checkbox_to_unchecked_box_inner_merged_animation = 0x7f01000f
co.auter.hcorp:attr/showMotionSpec = 0x7f0303b5
co.auter.hcorp:id/mtrl_picker_fullscreen = 0x7f08012d
co.auter.hcorp:drawable/abc_item_background_holo_light = 0x7f07004b
co.auter.hcorp:attr/panelBackground = 0x7f03034d
co.auter.hcorp:macro/m3_comp_switch_disabled_selected_icon_color = 0x7f0c011a
co.auter.hcorp:id/view_tree_saved_state_registry_owner = 0x7f0801f8
co.auter.hcorp:dimen/m3_comp_time_picker_period_selector_hover_state_layer_opacity = 0x7f0601a5
co.auter.hcorp:attr/tabSelectedTextColor = 0x7f030413
co.auter.hcorp:style/ShapeAppearance.M3.Comp.Sheet.Side.Docked.Container.Shape = 0x7f110171
co.auter.hcorp:attr/paddingTopNoTitle = 0x7f03034b
co.auter.hcorp:color/material_deep_teal_200 = 0x7f05022a
co.auter.hcorp:color/m3_assist_chip_icon_tint_color = 0x7f050074
co.auter.hcorp:attr/paddingStart = 0x7f030349
co.auter.hcorp:color/material_personalized_hint_foreground_inverse = 0x7f0502bc
co.auter.hcorp:attr/paddingRightSystemWindowInsets = 0x7f030348
co.auter.hcorp:attr/verticalOffset = 0x7f0304a3
co.auter.hcorp:macro/m3_comp_secondary_navigation_tab_active_label_text_color = 0x7f0c00fb
co.auter.hcorp:attr/paddingLeftSystemWindowInsets = 0x7f030347
co.auter.hcorp:attr/duration = 0x7f03018b
co.auter.hcorp:dimen/m3_searchbar_text_size = 0x7f0601e4
co.auter.hcorp:color/m3_ref_palette_dynamic_secondary80 = 0x7f0500f7
co.auter.hcorp:attr/paddingEnd = 0x7f030346
co.auter.hcorp:style/Base.ThemeOverlay.Material3.SideSheetDialog = 0x7f110088
co.auter.hcorp:attr/elevationOverlayAccentColor = 0x7f030191
co.auter.hcorp:dimen/m3_comp_elevated_button_container_elevation = 0x7f06010b
co.auter.hcorp:string/mtrl_exceed_max_badge_number_suffix = 0x7f1000b2
co.auter.hcorp:anim/m3_motion_fade_exit = 0x7f01002a
co.auter.hcorp:attr/maxLines = 0x7f0302f5
co.auter.hcorp:style/MaterialAlertDialog.Material3.Title.Panel.CenterStacked = 0x7f110137
co.auter.hcorp:attr/boxBackgroundMode = 0x7f030082
co.auter.hcorp:anim/catalyst_slide_up = 0x7f01001d
co.auter.hcorp:attr/contentInsetEnd = 0x7f03012e
co.auter.hcorp:attr/tabIconTintMode = 0x7f0303ff
co.auter.hcorp:attr/layout_constraintHeight_default = 0x7f030279
co.auter.hcorp:attr/layout_constraintWidth_default = 0x7f03028f
co.auter.hcorp:attr/paddingBottomSystemWindowInsets = 0x7f030345
co.auter.hcorp:anim/abc_slide_out_top = 0x7f010009
co.auter.hcorp:attr/colorControlHighlight = 0x7f0300ed
co.auter.hcorp:color/m3_calendar_item_disabled_text = 0x7f05007c
co.auter.hcorp:style/Base.Theme.Material3.Light.SideSheetDialog = 0x7f110067
co.auter.hcorp:color/mtrl_btn_text_btn_bg_color_selector = 0x7f0502cd
co.auter.hcorp:attr/overlayImage = 0x7f030343
co.auter.hcorp:style/Theme.MaterialComponents.Light.Dialog.MinWidth = 0x7f11029d
co.auter.hcorp:attr/onTouchUp = 0x7f030340
co.auter.hcorp:color/mtrl_btn_bg_color_selector = 0x7f0502ca
co.auter.hcorp:color/m3_appbar_overlay_color = 0x7f050073
co.auter.hcorp:color/m3_ref_palette_dynamic_secondary60 = 0x7f0500f5
co.auter.hcorp:attr/extendMotionSpec = 0x7f0301b8
co.auter.hcorp:attr/onHide = 0x7f03033c
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral100 = 0x7f0500b3
co.auter.hcorp:style/Base.TextAppearance.AppCompat.Widget.ActionMode.Title = 0x7f11003b
co.auter.hcorp:attr/nestedScrollable = 0x7f030337
co.auter.hcorp:color/browser_actions_text_color = 0x7f050029
co.auter.hcorp:attr/navigationViewStyle = 0x7f030334
co.auter.hcorp:styleable/LinearLayoutCompat_Layout = 0x7f12004a
co.auter.hcorp:attr/navigationRailStyle = 0x7f030333
co.auter.hcorp:color/m3_navigation_item_icon_tint = 0x7f0500a6
co.auter.hcorp:style/Base.Theme.MaterialComponents = 0x7f110068
co.auter.hcorp:dimen/mtrl_calendar_year_corner = 0x7f060299
co.auter.hcorp:style/Theme.MaterialComponents.DayNight.DarkActionBar.Bridge = 0x7f11027d
co.auter.hcorp:drawable/abc_cab_background_internal_bg = 0x7f070037
co.auter.hcorp:attr/multiChoiceItemLayout = 0x7f03032e
co.auter.hcorp:attr/cardPreventCornerOverlap = 0x7f0300a3
co.auter.hcorp:drawable/common_google_signin_btn_icon_dark_normal = 0x7f070085
co.auter.hcorp:dimen/mtrl_navigation_rail_default_width = 0x7f0602d0
co.auter.hcorp:dimen/m3_card_elevated_disabled_z = 0x7f0600e9
co.auter.hcorp:style/Widget.MaterialComponents.Snackbar = 0x7f110480
co.auter.hcorp:attr/helperTextTextColor = 0x7f03020e
co.auter.hcorp:attr/moveWhenScrollAtTop = 0x7f03032d
co.auter.hcorp:color/m3_ref_palette_dynamic_primary90 = 0x7f0500eb
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral_variant50 = 0x7f0500d4
co.auter.hcorp:attr/switchStyle = 0x7f0303f9
co.auter.hcorp:attr/motionStagger = 0x7f030329
co.auter.hcorp:anim/linear_indeterminate_line2_head_interpolator = 0x7f010025
co.auter.hcorp:style/ShapeAppearance.M3.Comp.TextButton.Container.Shape = 0x7f110175
co.auter.hcorp:attr/motionPathRotate = 0x7f030327
co.auter.hcorp:attr/mock_labelColor = 0x7f030305
co.auter.hcorp:style/ThemeOverlay.MaterialComponents.ActionBar = 0x7f1102f6
co.auter.hcorp:attr/motionPath = 0x7f030326
co.auter.hcorp:styleable/ActionMenuItemView = 0x7f120002
co.auter.hcorp:macro/m3_comp_time_picker_period_selector_unselected_hover_state_layer_color = 0x7f0c015b
co.auter.hcorp:drawable/m3_avd_show_password = 0x7f0700b8
co.auter.hcorp:attr/navigationIcon = 0x7f030330
co.auter.hcorp:attr/fastScrollHorizontalThumbDrawable = 0x7f0301cd
co.auter.hcorp:macro/m3_comp_navigation_drawer_active_hover_state_layer_color = 0x7f0c007d
co.auter.hcorp:color/m3_dynamic_dark_default_color_secondary_text = 0x7f050090
co.auter.hcorp:attr/customStringValue = 0x7f03015f
co.auter.hcorp:dimen/m3_comp_navigation_rail_icon_size = 0x7f06014e
co.auter.hcorp:xml/file_system_provider_paths = 0x7f130001
co.auter.hcorp:attr/textAppearanceHeadline1 = 0x7f030427
co.auter.hcorp:attr/tabIndicatorAnimationDuration = 0x7f030401
co.auter.hcorp:attr/motionEasingStandardDecelerateInterpolator = 0x7f030323
co.auter.hcorp:style/TextAppearance.Design.Placeholder = 0x7f1101e4
co.auter.hcorp:dimen/m3_comp_switch_unselected_pressed_state_layer_opacity = 0x7f06019e
co.auter.hcorp:color/abc_secondary_text_material_light = 0x7f050012
co.auter.hcorp:macro/m3_comp_outlined_text_field_outline_color = 0x7f0c00c3
co.auter.hcorp:color/m3_sys_color_dark_inverse_on_surface = 0x7f050170
co.auter.hcorp:attr/motionEasingLinearInterpolator = 0x7f030320
co.auter.hcorp:attr/itemIconPadding = 0x7f03023d
co.auter.hcorp:attr/homeAsUpIndicator = 0x7f030218
co.auter.hcorp:styleable/Autofill.InlineSuggestion = 0x7f120013
co.auter.hcorp:attr/motionEasingLinear = 0x7f03031f
co.auter.hcorp:attr/motionEasingEmphasizedInterpolator = 0x7f03031e
co.auter.hcorp:attr/motionEasingEmphasizedDecelerateInterpolator = 0x7f03031d
co.auter.hcorp:attr/maxHeight = 0x7f0302f3
co.auter.hcorp:dimen/mtrl_high_ripple_focused_alpha = 0x7f0602be
co.auter.hcorp:attr/actionBarSize = 0x7f030003
co.auter.hcorp:drawable/ic_mtrl_chip_checked_circle = 0x7f0700ae
co.auter.hcorp:attr/flow_verticalBias = 0x7f0301f0
co.auter.hcorp:dimen/mtrl_textinput_box_corner_radius_small = 0x7f060300
co.auter.hcorp:attr/motionEasingEmphasizedAccelerateInterpolator = 0x7f03031c
co.auter.hcorp:attr/motionEasingEmphasized = 0x7f03031b
co.auter.hcorp:id/sawtooth = 0x7f080182
co.auter.hcorp:color/m3_sys_color_tertiary_fixed = 0x7f05020b
co.auter.hcorp:attr/buttonBarNeutralButtonStyle = 0x7f03008f
co.auter.hcorp:attr/motionDurationShort4 = 0x7f030318
co.auter.hcorp:animator/fragment_close_enter = 0x7f020003
co.auter.hcorp:drawable/$m3_avd_show_password__0 = 0x7f070009
co.auter.hcorp:color/mtrl_navigation_bar_colored_item_tint = 0x7f0502e5
co.auter.hcorp:attr/itemMaxLines = 0x7f030240
co.auter.hcorp:attr/motionDurationShort2 = 0x7f030316
co.auter.hcorp:macro/m3_comp_filled_tonal_button_label_text_color = 0x7f0c0053
co.auter.hcorp:attr/startIconDrawable = 0x7f0303d3
co.auter.hcorp:attr/layout_dodgeInsetEdges = 0x7f030293
co.auter.hcorp:attr/tickRadiusInactive = 0x7f030467
co.auter.hcorp:dimen/abc_control_inset_material = 0x7f060019
co.auter.hcorp:attr/endIconContentDescription = 0x7f030197
co.auter.hcorp:attr/queryBackground = 0x7f030373
co.auter.hcorp:dimen/m3_sys_motion_easing_legacy_control_x1 = 0x7f060205
co.auter.hcorp:attr/motionDurationShort1 = 0x7f030315
co.auter.hcorp:dimen/m3_btn_stroke_size = 0x7f0600e0
co.auter.hcorp:animator/mtrl_btn_state_list_anim = 0x7f020015
co.auter.hcorp:string/common_signin_button_text = 0x7f10005f
co.auter.hcorp:attr/tooltipText = 0x7f030483
co.auter.hcorp:attr/motionDurationMedium2 = 0x7f030312
co.auter.hcorp:attr/endIconMode = 0x7f03019a
co.auter.hcorp:attr/motionDurationLong3 = 0x7f03030f
co.auter.hcorp:macro/m3_comp_navigation_drawer_headline_type = 0x7f0c0085
co.auter.hcorp:attr/motionDurationLong2 = 0x7f03030e
co.auter.hcorp:attr/indeterminateProgressStyle = 0x7f03022a
co.auter.hcorp:attr/spinnerStyle = 0x7f0303cb
co.auter.hcorp:attr/motionDurationExtraLong3 = 0x7f03030b
co.auter.hcorp:string/mtrl_switch_track_decoration_path = 0x7f1000de
co.auter.hcorp:attr/roundAsCircle = 0x7f030386
co.auter.hcorp:dimen/m3_comp_extended_fab_primary_focus_container_elevation = 0x7f060111
co.auter.hcorp:attr/motionDurationExtraLong2 = 0x7f03030a
co.auter.hcorp:anim/mtrl_card_lowers_interpolator = 0x7f010031
co.auter.hcorp:color/m3_sys_color_dynamic_tertiary_fixed = 0x7f0501dd
co.auter.hcorp:macro/m3_comp_primary_navigation_tab_with_icon_active_icon_color = 0x7f0c00cf
co.auter.hcorp:color/abc_btn_colored_text_material = 0x7f050003
co.auter.hcorp:style/Base.V14.Theme.Material3.Dark.SideSheetDialog = 0x7f110092
co.auter.hcorp:attr/titleCollapseMode = 0x7f03046e
co.auter.hcorp:dimen/m3_comp_filter_chip_container_height = 0x7f06012e
co.auter.hcorp:attr/motionEasingAccelerated = 0x7f030319
co.auter.hcorp:dimen/design_navigation_padding_bottom = 0x7f06007f
co.auter.hcorp:attr/motionDurationExtraLong1 = 0x7f030309
co.auter.hcorp:attr/progressBarPadding = 0x7f030371
co.auter.hcorp:attr/motionDebug = 0x7f030308
co.auter.hcorp:style/Base.Widget.AppCompat.SearchView.ActionBar = 0x7f1100f9
co.auter.hcorp:attr/snackbarTextViewStyle = 0x7f0303c7
co.auter.hcorp:style/Widget.Material3.ExtendedFloatingActionButton.Primary = 0x7f1103c1
co.auter.hcorp:attr/chipStyle = 0x7f0300ca
co.auter.hcorp:attr/collapsedSize = 0x7f0300de
co.auter.hcorp:attr/tabPaddingTop = 0x7f03040f
co.auter.hcorp:attr/nestedScrollFlags = 0x7f030335
co.auter.hcorp:macro/m3_comp_outlined_button_disabled_outline_color = 0x7f0c00a3
co.auter.hcorp:attr/navigationIconTint = 0x7f030331
co.auter.hcorp:style/Base.Theme.MaterialComponents.Light.Dialog.Alert = 0x7f110076
co.auter.hcorp:dimen/material_filled_edittext_font_2_0_padding_bottom = 0x7f06023c
co.auter.hcorp:attr/mock_label = 0x7f030303
co.auter.hcorp:attr/mock_diagonalsColor = 0x7f030302
co.auter.hcorp:attr/maxWidth = 0x7f0302f8
co.auter.hcorp:attr/minWidth = 0x7f030301
co.auter.hcorp:macro/m3_comp_navigation_bar_active_pressed_label_text_color = 0x7f0c0069
co.auter.hcorp:attr/minSeparation = 0x7f0302ff
co.auter.hcorp:styleable/ForegroundLinearLayout = 0x7f120038
co.auter.hcorp:style/Base.TextAppearance.AppCompat.SearchResult.Title = 0x7f11002d
co.auter.hcorp:attr/minHeight = 0x7f0302fd
co.auter.hcorp:attr/menuGravity = 0x7f0302fc
co.auter.hcorp:style/Widget.Material3.Badge = 0x7f110380
co.auter.hcorp:attr/contentInsetEndWithActions = 0x7f03012f
co.auter.hcorp:style/Widget.Material3.MaterialCalendar.Day.Today = 0x7f1103d9
co.auter.hcorp:attr/maxImageSize = 0x7f0302f4
co.auter.hcorp:styleable/KeyAttribute = 0x7f120040
co.auter.hcorp:macro/m3_comp_navigation_rail_inactive_label_text_color = 0x7f0c009d
co.auter.hcorp:color/design_dark_default_color_secondary = 0x7f05004e
co.auter.hcorp:integer/mtrl_card_anim_delay_ms = 0x7f090035
co.auter.hcorp:attr/shapeCornerFamily = 0x7f0303ad
co.auter.hcorp:color/m3_sys_color_dynamic_dark_surface_container_high = 0x7f0501a9
co.auter.hcorp:attr/transitionEasing = 0x7f030497
co.auter.hcorp:attr/actionBarPopupTheme = 0x7f030002
co.auter.hcorp:attr/layout_collapseMode = 0x7f030267
co.auter.hcorp:attr/maxAcceleration = 0x7f0302ef
co.auter.hcorp:attr/materialTimePickerStyle = 0x7f0302ec
co.auter.hcorp:attr/textAppearanceHeadlineMedium = 0x7f03042e
co.auter.hcorp:style/Base.Widget.AppCompat.ListView = 0x7f1100ed
co.auter.hcorp:dimen/m3_comp_assist_chip_flat_container_elevation = 0x7f0600ff
co.auter.hcorp:styleable/Fragment = 0x7f120039
co.auter.hcorp:style/Widget.Material3.Snackbar.FullWidth = 0x7f11040f
co.auter.hcorp:id/dialog_button = 0x7f08009e
co.auter.hcorp:attr/materialSearchViewToolbarHeight = 0x7f0302e8
co.auter.hcorp:color/material_personalized_color_surface_variant = 0x7f0502b3
co.auter.hcorp:attr/materialSearchViewStyle = 0x7f0302e7
co.auter.hcorp:attr/materialIconButtonOutlinedStyle = 0x7f0302e3
co.auter.hcorp:style/Widget.Material3.FloatingActionButton.Tertiary = 0x7f1103d0
co.auter.hcorp:dimen/m3_comp_suggestion_chip_elevated_container_elevation = 0x7f06018e
co.auter.hcorp:color/design_fab_stroke_end_outer_color = 0x7f050063
co.auter.hcorp:macro/m3_comp_radio_button_selected_pressed_state_layer_color = 0x7f0c00de
co.auter.hcorp:dimen/m3_navigation_rail_item_padding_bottom_with_large_font = 0x7f0601d4
co.auter.hcorp:attr/stackFromEnd = 0x7f0303cf
co.auter.hcorp:attr/materialDividerHeavyStyle = 0x7f0302df
co.auter.hcorp:attr/materialClockStyle = 0x7f0302dd
co.auter.hcorp:macro/m3_comp_checkbox_selected_error_icon_color = 0x7f0c000a
co.auter.hcorp:layout/abc_list_menu_item_checkbox = 0x7f0b000e
co.auter.hcorp:attr/autoSizeStepGranularity = 0x7f030042
co.auter.hcorp:layout/browser_actions_context_menu_row = 0x7f0b001f
co.auter.hcorp:attr/materialCalendarHeaderTitle = 0x7f0302d1
co.auter.hcorp:style/ShapeAppearance.MaterialComponents.Tooltip = 0x7f11018e
co.auter.hcorp:color/m3_ref_palette_primary95 = 0x7f050145
co.auter.hcorp:attr/checkedIconEnabled = 0x7f0300b0
co.auter.hcorp:attr/materialCalendarHeaderLayout = 0x7f0302cf
co.auter.hcorp:attr/materialCalendarHeaderCancelButton = 0x7f0302cc
co.auter.hcorp:style/Widget.AppCompat.ButtonBar.AlertDialog = 0x7f11032f
co.auter.hcorp:color/call_notification_answer_color = 0x7f05002d
co.auter.hcorp:attr/materialButtonStyle = 0x7f0302c7
co.auter.hcorp:attr/materialCalendarStyle = 0x7f0302d5
co.auter.hcorp:style/MaterialAlertDialog.MaterialComponents.Title.Panel = 0x7f110140
co.auter.hcorp:color/switch_thumb_normal_material_dark = 0x7f050318
co.auter.hcorp:attr/materialAlertDialogTitleTextStyle = 0x7f0302c5
co.auter.hcorp:dimen/mtrl_tooltip_arrowSize = 0x7f060309
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral96 = 0x7f0500c6
co.auter.hcorp:dimen/m3_navigation_item_shape_inset_end = 0x7f0601c6
co.auter.hcorp:attr/materialAlertDialogTheme = 0x7f0302c2
co.auter.hcorp:attr/materialAlertDialogButtonSpacerVisibility = 0x7f0302c1
co.auter.hcorp:interpolator/mtrl_fast_out_linear_in = 0x7f0a000e
co.auter.hcorp:color/m3_sys_color_dynamic_light_on_secondary = 0x7f0501bc
co.auter.hcorp:string/bottomsheet_action_expand_halfway = 0x7f100024
co.auter.hcorp:attr/nestedScrollViewStyle = 0x7f030336
co.auter.hcorp:attr/marginRightSystemWindowInsets = 0x7f0302be
co.auter.hcorp:attr/placeholderTextColor = 0x7f030361
co.auter.hcorp:id/accessibility_custom_action_30 = 0x7f08002b
co.auter.hcorp:attr/materialThemeOverlay = 0x7f0302eb
co.auter.hcorp:attr/marginLeftSystemWindowInsets = 0x7f0302bd
co.auter.hcorp:drawable/common_google_signin_btn_icon_disabled = 0x7f070087
co.auter.hcorp:attr/fontFamily = 0x7f0301f5
co.auter.hcorp:attr/telltales_velocityMode = 0x7f03041b
co.auter.hcorp:attr/logoScaleType = 0x7f0302bb
co.auter.hcorp:attr/logoAdjustViewBounds = 0x7f0302b9
co.auter.hcorp:dimen/mtrl_snackbar_padding_horizontal = 0x7f0602f8
co.auter.hcorp:attr/listPreferredItemPaddingRight = 0x7f0302b6
co.auter.hcorp:attr/iconTint = 0x7f030223
co.auter.hcorp:dimen/m3_comp_top_app_bar_medium_container_height = 0x7f0601ac
co.auter.hcorp:attr/panelMenuListTheme = 0x7f03034e
co.auter.hcorp:attr/toolbarStyle = 0x7f03047e
co.auter.hcorp:style/Widget.Material3.SideSheet = 0x7f110406
co.auter.hcorp:color/m3_sys_color_dynamic_primary_fixed_dim = 0x7f0501da
co.auter.hcorp:style/Widget.AppCompat.CompoundButton.CheckBox = 0x7f110330
co.auter.hcorp:attr/textAppearanceHeadline2 = 0x7f030428
co.auter.hcorp:attr/cornerSizeTopLeft = 0x7f030149
co.auter.hcorp:attr/colorButtonNormal = 0x7f0300ea
co.auter.hcorp:attr/listPreferredItemPaddingEnd = 0x7f0302b4
co.auter.hcorp:attr/buttonTint = 0x7f03009c
co.auter.hcorp:attr/boxCornerRadiusTopEnd = 0x7f030086
co.auter.hcorp:id/design_navigation_view = 0x7f08009d
co.auter.hcorp:color/m3_ref_palette_dynamic_secondary40 = 0x7f0500f3
co.auter.hcorp:attr/listPreferredItemHeightLarge = 0x7f0302b2
co.auter.hcorp:style/Base.V14.Theme.MaterialComponents.Bridge = 0x7f110098
co.auter.hcorp:attr/counterTextAppearance = 0x7f03014f
co.auter.hcorp:attr/textInputOutlinedExposedDropdownMenuStyle = 0x7f03044a
co.auter.hcorp:attr/behavior_skipCollapsed = 0x7f030077
co.auter.hcorp:dimen/m3_comp_outlined_text_field_focus_outline_width = 0x7f06015b
co.auter.hcorp:color/m3_ref_palette_primary99 = 0x7f050146
co.auter.hcorp:attr/listPopupWindowStyle = 0x7f0302b0
co.auter.hcorp:anim/linear_indeterminate_line2_tail_interpolator = 0x7f010026
co.auter.hcorp:color/m3_ref_palette_tertiary60 = 0x7f05015b
co.auter.hcorp:style/Widget.AppCompat.ImageButton = 0x7f110336
co.auter.hcorp:attr/customPixelDimension = 0x7f03015e
co.auter.hcorp:attr/listMenuViewStyle = 0x7f0302af
co.auter.hcorp:string/abc_searchview_description_query = 0x7f100014
co.auter.hcorp:attr/simpleItemSelectedRippleColor = 0x7f0303be
co.auter.hcorp:attr/tabIndicatorHeight = 0x7f030406
co.auter.hcorp:attr/listChoiceIndicatorSingleAnimated = 0x7f0302ab
co.auter.hcorp:dimen/m3_sys_motion_easing_emphasized_accelerate_control_y1 = 0x7f0601fb
co.auter.hcorp:dimen/m3_sys_elevation_level2 = 0x7f0601f5
co.auter.hcorp:attr/listChoiceBackgroundIndicator = 0x7f0302a9
co.auter.hcorp:dimen/tooltip_precise_anchor_extra_offset = 0x7f06032c
co.auter.hcorp:style/TextAppearance.M3.Sys.Typescale.DisplayMedium = 0x7f1101ed
co.auter.hcorp:layout/mtrl_calendar_day_of_week = 0x7f0b004f
co.auter.hcorp:attr/fontProviderFetchTimeout = 0x7f0301f9
co.auter.hcorp:color/m3_sys_color_dynamic_light_error_container = 0x7f0501b3
co.auter.hcorp:attr/largeFontVerticalOffsetAdjustment = 0x7f03025d
co.auter.hcorp:attr/linearProgressIndicatorStyle = 0x7f0302a8
co.auter.hcorp:style/Platform.V25.AppCompat = 0x7f110150
co.auter.hcorp:drawable/ic_m3_chip_check = 0x7f0700a9
co.auter.hcorp:attr/listChoiceIndicatorMultipleAnimated = 0x7f0302aa
co.auter.hcorp:macro/m3_comp_time_picker_time_selector_label_text_type = 0x7f0c015f
co.auter.hcorp:color/m3_sys_color_dynamic_dark_on_primary_container = 0x7f050199
co.auter.hcorp:attr/liftOnScrollColor = 0x7f0302a3
co.auter.hcorp:attr/carousel_alignment = 0x7f0300a6
co.auter.hcorp:style/Base.ThemeOverlay.AppCompat.Dark.ActionBar = 0x7f110081
co.auter.hcorp:attr/layout_constraintBottom_creator = 0x7f03026d
co.auter.hcorp:dimen/m3_comp_search_bar_pressed_state_layer_opacity = 0x7f060175
co.auter.hcorp:layout/notification_action_tombstone = 0x7f0b0068
co.auter.hcorp:attr/layout_goneMarginTop = 0x7f03029b
co.auter.hcorp:style/MaterialAlertDialog.MaterialComponents.Body.Text = 0x7f11013b
co.auter.hcorp:style/Base.TextAppearance.MaterialComponents.Button = 0x7f110048
co.auter.hcorp:attr/autoTransition = 0x7f030044
co.auter.hcorp:attr/materialAlertDialogBodyTextStyle = 0x7f0302c0
co.auter.hcorp:attr/clickAction = 0x7f0300cf
co.auter.hcorp:attr/dialogPreferredPadding = 0x7f03016d
co.auter.hcorp:color/material_dynamic_neutral_variant0 = 0x7f050242
co.auter.hcorp:color/m3_ref_palette_secondary20 = 0x7f05014a
co.auter.hcorp:attr/layout_goneMarginLeft = 0x7f030298
co.auter.hcorp:styleable/TextInputEditText = 0x7f12008e
co.auter.hcorp:anim/linear_indeterminate_line1_head_interpolator = 0x7f010023
co.auter.hcorp:color/mtrl_on_primary_text_btn_text_color_selector = 0x7f0502ec
co.auter.hcorp:attr/layout_goneMarginEnd = 0x7f030297
co.auter.hcorp:attr/chipBackgroundColor = 0x7f0300b8
co.auter.hcorp:dimen/notification_large_icon_width = 0x7f060314
co.auter.hcorp:id/outline = 0x7f080156
co.auter.hcorp:attr/sideSheetDialogTheme = 0x7f0303ba
co.auter.hcorp:attr/layout_constraintWidth_min = 0x7f030291
co.auter.hcorp:attr/circularProgressIndicatorStyle = 0x7f0300ce
co.auter.hcorp:drawable/mtrl_ic_indeterminate = 0x7f0700e0
co.auter.hcorp:style/ThemeOverlay.Material3.Dialog.Alert.Framework = 0x7f1102d0
co.auter.hcorp:id/radio = 0x7f08016a
co.auter.hcorp:attr/colorOnError = 0x7f0300f4
co.auter.hcorp:style/Widget.Material3.MaterialCalendar = 0x7f1103d5
co.auter.hcorp:drawable/$avd_show_password__0 = 0x7f070003
co.auter.hcorp:style/Widget.Material3.SideSheet.Modal = 0x7f110408
co.auter.hcorp:color/accent_material_light = 0x7f05001a
co.auter.hcorp:color/m3_tabs_ripple_color = 0x7f05020f
co.auter.hcorp:attr/layout_constraintVertical_weight = 0x7f03028e
co.auter.hcorp:style/Widget.MaterialComponents.MaterialCalendar.Day.Invalid = 0x7f11045c
co.auter.hcorp:attr/state_indeterminate = 0x7f0303dd
co.auter.hcorp:styleable/Snackbar = 0x7f120081
co.auter.hcorp:style/Widget.MaterialComponents.MaterialCalendar.HeaderToggleButton = 0x7f11046a
co.auter.hcorp:attr/layout_constraintTop_creator = 0x7f030289
co.auter.hcorp:color/m3_navigation_item_background_color = 0x7f0500a5
co.auter.hcorp:dimen/tooltip_y_offset_non_touch = 0x7f06032f
co.auter.hcorp:attr/extraMultilineHeightEnabled = 0x7f0301bf
co.auter.hcorp:style/Base.Theme.MaterialComponents.Light.Dialog.MinWidth = 0x7f110079
co.auter.hcorp:attr/layout_scrollFlags = 0x7f0302a0
co.auter.hcorp:attr/layout_constraintTag = 0x7f030288
co.auter.hcorp:attr/layout_constraintStart_toEndOf = 0x7f030286
co.auter.hcorp:string/abc_searchview_description_clear = 0x7f100013
co.auter.hcorp:id/mtrl_picker_title_text = 0x7f080135
co.auter.hcorp:attr/tooltipForegroundColor = 0x7f030480
co.auter.hcorp:attr/chipIconVisible = 0x7f0300c0
co.auter.hcorp:dimen/mtrl_bottomappbar_fab_cradle_rounded_corner_radius = 0x7f060259
co.auter.hcorp:attr/chipIcon = 0x7f0300bc
co.auter.hcorp:dimen/mtrl_calendar_landscape_header_width = 0x7f06028a
co.auter.hcorp:attr/actualImageScaleType = 0x7f030027
co.auter.hcorp:attr/textAppearanceOverline = 0x7f030438
co.auter.hcorp:attr/colorControlNormal = 0x7f0300ee
co.auter.hcorp:color/material_dynamic_tertiary30 = 0x7f05026d
co.auter.hcorp:color/m3_ref_palette_primary40 = 0x7f05013f
co.auter.hcorp:attr/colorOnSecondaryFixed = 0x7f0300fd
co.auter.hcorp:attr/collapsingToolbarLayoutLargeSize = 0x7f0300e2
co.auter.hcorp:attr/enforceSystemBarsLightTheme = 0x7f0301a0
co.auter.hcorp:attr/layout_constraintRight_creator = 0x7f030283
co.auter.hcorp:attr/textLocale = 0x7f03044d
co.auter.hcorp:attr/layout_constraintLeft_creator = 0x7f030280
co.auter.hcorp:drawable/abc_btn_radio_material_anim = 0x7f070032
co.auter.hcorp:color/primary_material_light = 0x7f050308
co.auter.hcorp:attr/waveShape = 0x7f0304ad
co.auter.hcorp:attr/closeIconVisible = 0x7f0300da
co.auter.hcorp:attr/layout_constraintHorizontal_weight = 0x7f03027f
co.auter.hcorp:attr/svg = 0x7f0303f5
co.auter.hcorp:integer/m3_sys_motion_duration_short4 = 0x7f090020
co.auter.hcorp:dimen/m3_timepicker_window_elevation = 0x7f060222
co.auter.hcorp:attr/region_widthLessThan = 0x7f03037e
co.auter.hcorp:attr/layout_constraintHeight_min = 0x7f03027b
co.auter.hcorp:attr/checkboxStyle = 0x7f0300ac
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral_variant30 = 0x7f0500d1
co.auter.hcorp:integer/mtrl_calendar_selection_text_lines = 0x7f090033
co.auter.hcorp:attr/colorOnPrimaryFixedVariant = 0x7f0300f9
co.auter.hcorp:macro/m3_comp_fab_tertiary_container_color = 0x7f0c003f
co.auter.hcorp:id/open_search_bar_text_view = 0x7f080148
co.auter.hcorp:color/m3_ref_palette_dynamic_primary30 = 0x7f0500e5
co.auter.hcorp:attr/layout_constraintVertical_chainStyle = 0x7f03028d
co.auter.hcorp:dimen/m3_sys_motion_easing_legacy_control_y1 = 0x7f060207
co.auter.hcorp:styleable/Layout = 0x7f120048
co.auter.hcorp:style/Widget.Material3.ActionMode = 0x7f11037a
co.auter.hcorp:string/mtrl_picker_invalid_range = 0x7f1000c2
co.auter.hcorp:color/abc_secondary_text_material_dark = 0x7f050011
co.auter.hcorp:attr/layout_constraintHeight_max = 0x7f03027a
co.auter.hcorp:animator/design_fab_show_motion_spec = 0x7f020002
co.auter.hcorp:attr/animate_relativeTo = 0x7f030034
co.auter.hcorp:style/TextAppearance.AppCompat.Display4 = 0x7f1101ac
co.auter.hcorp:style/Base.V14.Theme.MaterialComponents = 0x7f110097
co.auter.hcorp:drawable/abc_list_pressed_holo_dark = 0x7f070050
co.auter.hcorp:string/menubar_description = 0x7f1000a2
co.auter.hcorp:attr/layout_editor_absoluteY = 0x7f030295
co.auter.hcorp:style/Widget.MaterialComponents.NavigationRailView.Colored.Compact = 0x7f110475
co.auter.hcorp:attr/autofillInlineSuggestionSubtitle = 0x7f030048
co.auter.hcorp:style/Theme.MaterialComponents.DayNight.Dialog.FixedSize = 0x7f110282
co.auter.hcorp:color/m3_navigation_bar_ripple_color_selector = 0x7f0500a4
co.auter.hcorp:drawable/tooltip_frame_dark = 0x7f070107
co.auter.hcorp:attr/layout_constraintEnd_toEndOf = 0x7f030274
co.auter.hcorp:style/CardView = 0x7f11012b
co.auter.hcorp:attr/snackbarStyle = 0x7f0303c6
co.auter.hcorp:color/m3_ref_palette_secondary100 = 0x7f050149
co.auter.hcorp:macro/m3_comp_navigation_rail_container_color = 0x7f0c0099
co.auter.hcorp:id/action_menu_divider = 0x7f080046
co.auter.hcorp:attr/thumbIconSize = 0x7f030455
co.auter.hcorp:attr/dividerInsetEnd = 0x7f030173
co.auter.hcorp:styleable/MaterialSwitch = 0x7f12005b
co.auter.hcorp:attr/layout_constraintTop_toTopOf = 0x7f03028b
co.auter.hcorp:macro/m3_comp_radio_button_selected_hover_icon_color = 0x7f0c00da
co.auter.hcorp:id/search_edit_frame = 0x7f08018e
co.auter.hcorp:attr/layout_constraintBaseline_toBaselineOf = 0x7f03026c
co.auter.hcorp:attr/motionDurationMedium1 = 0x7f030311
co.auter.hcorp:color/mtrl_card_view_ripple = 0x7f0502d5
co.auter.hcorp:attr/actionOverflowMenuStyle = 0x7f030020
co.auter.hcorp:color/m3_bottom_sheet_drag_handle_color = 0x7f050076
co.auter.hcorp:attr/layout_constrainedWidth = 0x7f03026a
co.auter.hcorp:drawable/$avd_show_password__2 = 0x7f070005
co.auter.hcorp:attr/layout_collapseParallaxMultiplier = 0x7f030268
co.auter.hcorp:layout/notification_template_lines_media = 0x7f0b0071
co.auter.hcorp:attr/expandedTitleMargin = 0x7f0301b1
co.auter.hcorp:attr/selectableItemBackground = 0x7f03039f
co.auter.hcorp:styleable/MenuGroup = 0x7f120060
co.auter.hcorp:string/mtrl_badge_numberless_content_description = 0x7f1000a4
co.auter.hcorp:color/highlighted_text_material_dark = 0x7f050070
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral40 = 0x7f0500bb
co.auter.hcorp:attr/lastBaselineToBottomHeight = 0x7f03025e
co.auter.hcorp:string/catalyst_hot_reloading_auto_disable = 0x7f100039
co.auter.hcorp:attr/defaultDuration = 0x7f030164
co.auter.hcorp:attr/collapsingToolbarLayoutStyle = 0x7f0300e6
co.auter.hcorp:attr/cornerFamilyBottomRight = 0x7f030142
co.auter.hcorp:dimen/m3_comp_top_app_bar_small_container_height = 0x7f0601ae
co.auter.hcorp:color/m3_sys_color_dynamic_primary_fixed = 0x7f0501d9
co.auter.hcorp:color/m3_sys_color_dynamic_dark_on_background = 0x7f050195
co.auter.hcorp:style/Animation.Design.BottomSheetDialog = 0x7f110007
co.auter.hcorp:color/design_dark_default_color_on_secondary = 0x7f050049
co.auter.hcorp:color/mtrl_choice_chip_ripple_color = 0x7f0502db
co.auter.hcorp:macro/m3_comp_time_picker_container_color = 0x7f0c014e
co.auter.hcorp:attr/textAppearanceDisplayLarge = 0x7f030424
co.auter.hcorp:dimen/mtrl_badge_toolbar_action_menu_item_vertical_offset = 0x7f060254
co.auter.hcorp:attr/closeIconEnabled = 0x7f0300d5
co.auter.hcorp:attr/labelStyle = 0x7f03025b
co.auter.hcorp:style/Widget.Material3.MaterialTimePicker.Clock = 0x7f1103f0
co.auter.hcorp:dimen/m3_extended_fab_start_padding = 0x7f0601b6
co.auter.hcorp:attr/checkMarkCompat = 0x7f0300a9
co.auter.hcorp:attr/keylines = 0x7f030258
co.auter.hcorp:attr/showText = 0x7f0303b7
co.auter.hcorp:anim/catalyst_slide_down = 0x7f01001c
co.auter.hcorp:id/pin = 0x7f080163
co.auter.hcorp:attr/itemTextAppearanceInactive = 0x7f030253
co.auter.hcorp:attr/backgroundColor = 0x7f03004c
co.auter.hcorp:attr/titleMarginBottom = 0x7f030471
co.auter.hcorp:attr/itemTextAppearance = 0x7f030250
co.auter.hcorp:attr/extendedFloatingActionButtonTertiaryStyle = 0x7f0301be
co.auter.hcorp:attr/itemStrokeColor = 0x7f03024e
co.auter.hcorp:drawable/notification_bg_low_pressed = 0x7f0700f6
co.auter.hcorp:color/material_dynamic_color_light_on_error_container = 0x7f050234
co.auter.hcorp:dimen/m3_comp_fab_primary_pressed_container_elevation = 0x7f060120
co.auter.hcorp:attr/boxStrokeColor = 0x7f030088
co.auter.hcorp:attr/drawableBottomCompat = 0x7f03017c
co.auter.hcorp:string/mtrl_checkbox_button_path_group_name = 0x7f1000aa
co.auter.hcorp:color/material_personalized_color_secondary_text = 0x7f0502a8
co.auter.hcorp:style/Theme.Material3.Dark.NoActionBar = 0x7f11025d
co.auter.hcorp:attr/windowMinWidthMajor = 0x7f0304b6
co.auter.hcorp:string/m3_sys_motion_easing_emphasized_accelerate = 0x7f100081
co.auter.hcorp:attr/expandedTitleGravity = 0x7f0301b0
co.auter.hcorp:attr/fabCradleVerticalOffset = 0x7f0301c6
co.auter.hcorp:style/Theme.AppCompat = 0x7f110223
co.auter.hcorp:attr/itemShapeInsetBottom = 0x7f030249
co.auter.hcorp:color/primary_text_disabled_material_light = 0x7f05030c
co.auter.hcorp:style/ShapeAppearanceOverlay.MaterialComponents.MaterialCalendar.Window.Fullscreen = 0x7f11019f
co.auter.hcorp:color/m3_sys_color_dynamic_dark_on_secondary_container = 0x7f05019b
co.auter.hcorp:attr/tabSecondaryStyle = 0x7f030411
co.auter.hcorp:attr/materialCalendarDayOfWeekLabel = 0x7f0302ca
co.auter.hcorp:id/fitToContents = 0x7f0800c7
co.auter.hcorp:attr/floatingActionButtonSmallTertiaryStyle = 0x7f0301dd
co.auter.hcorp:color/mtrl_switch_thumb_icon_tint = 0x7f0502f2
co.auter.hcorp:attr/motionEasingStandardAccelerateInterpolator = 0x7f030322
co.auter.hcorp:attr/itemShapeAppearanceOverlay = 0x7f030247
co.auter.hcorp:attr/suffixText = 0x7f0303f1
co.auter.hcorp:attr/itemPaddingTop = 0x7f030244
co.auter.hcorp:color/m3_ref_palette_error60 = 0x7f05010f
co.auter.hcorp:attr/scopeUris = 0x7f030396
co.auter.hcorp:macro/m3_comp_time_picker_time_selector_container_shape = 0x7f0c015e
co.auter.hcorp:dimen/mtrl_navigation_bar_item_default_margin = 0x7f0602c7
co.auter.hcorp:attr/constraints = 0x7f03012b
co.auter.hcorp:attr/enforceTextAppearance = 0x7f0301a1
co.auter.hcorp:string/common_google_play_services_wear_update_text = 0x7f10005d
co.auter.hcorp:attr/itemIconTint = 0x7f03023f
co.auter.hcorp:style/Base.V14.Theme.Material3.Dark.BottomSheetDialog = 0x7f110090
co.auter.hcorp:color/m3_default_color_secondary_text = 0x7f05008e
co.auter.hcorp:attr/placeholderImageScaleType = 0x7f03035e
co.auter.hcorp:attr/itemHorizontalTranslationEnabled = 0x7f03023c
co.auter.hcorp:drawable/abc_list_divider_mtrl_alpha = 0x7f07004d
co.auter.hcorp:color/m3_default_color_primary_text = 0x7f05008d
co.auter.hcorp:attr/itemShapeInsetEnd = 0x7f03024a
co.auter.hcorp:dimen/m3_comp_sheet_bottom_docked_drag_handle_height = 0x7f06017d
co.auter.hcorp:attr/indicatorDirectionLinear = 0x7f03022d
co.auter.hcorp:dimen/m3_btn_icon_btn_padding_right = 0x7f0600d5
co.auter.hcorp:attr/badgeWithTextShapeAppearance = 0x7f030065
co.auter.hcorp:attr/isMaterialTheme = 0x7f030237
co.auter.hcorp:id/view_tag_instance_handle = 0x7f0801f4
co.auter.hcorp:dimen/abc_cascading_menus_min_smallest_width = 0x7f060016
co.auter.hcorp:attr/displayOptions = 0x7f03016f
co.auter.hcorp:attr/initialActivityCount = 0x7f030231
co.auter.hcorp:attr/imageAspectRatio = 0x7f030226
co.auter.hcorp:style/TextAppearance.AppCompat.Widget.PopupMenu.Small = 0x7f1101d1
co.auter.hcorp:color/m3_ref_palette_tertiary70 = 0x7f05015c
co.auter.hcorp:attr/showMarker = 0x7f0303b4
co.auter.hcorp:style/ThemeOverlay.Material3.TabLayout = 0x7f1102ed
co.auter.hcorp:interpolator/m3_sys_motion_easing_emphasized_decelerate = 0x7f0a0009
co.auter.hcorp:attr/indeterminateAnimationType = 0x7f030229
co.auter.hcorp:dimen/m3_toolbar_text_size_title = 0x7f060223
co.auter.hcorp:attr/actionTextColorAlpha = 0x7f030022
co.auter.hcorp:layout/abc_cascading_menu_item_layout = 0x7f0b000b
co.auter.hcorp:id/actions = 0x7f08004c
co.auter.hcorp:dimen/mtrl_calendar_header_height_fullscreen = 0x7f060285
co.auter.hcorp:dimen/m3_navigation_rail_elevation = 0x7f0601cd
co.auter.hcorp:attr/materialIconButtonStyle = 0x7f0302e4
co.auter.hcorp:style/Theme.SplashScreen = 0x7f1102a8
co.auter.hcorp:animator/fragment_fade_enter = 0x7f020005
co.auter.hcorp:attr/extendStrategy = 0x7f0301b9
co.auter.hcorp:attr/contentPaddingBottom = 0x7f030135
co.auter.hcorp:drawable/notification_icon_background = 0x7f0700f9
co.auter.hcorp:dimen/mtrl_calendar_header_selection_line_height = 0x7f060286
co.auter.hcorp:id/icon_group = 0x7f0800e0
co.auter.hcorp:attr/actionModeSelectAllDrawable = 0x7f030019
co.auter.hcorp:attr/autofillInlineSuggestionChip = 0x7f030045
co.auter.hcorp:attr/colorSurfaceVariant = 0x7f03011f
co.auter.hcorp:layout/design_navigation_item_separator = 0x7f0b002a
co.auter.hcorp:attr/itemRippleColor = 0x7f030245
co.auter.hcorp:color/m3_sys_color_light_primary = 0x7f0501f2
co.auter.hcorp:attr/iconSize = 0x7f030221
co.auter.hcorp:attr/itemTextColor = 0x7f030254
co.auter.hcorp:style/Base.Widget.AppCompat.Spinner = 0x7f1100fc
co.auter.hcorp:attr/textStartPadding = 0x7f03044e
co.auter.hcorp:attr/placeholder_emptyVisibility = 0x7f030362
co.auter.hcorp:style/ShapeAppearanceOverlay.MaterialComponents.FloatingActionButton = 0x7f11019d
co.auter.hcorp:color/m3_sys_color_dark_error_container = 0x7f05016f
co.auter.hcorp:attr/badgeVerticalPadding = 0x7f030060
co.auter.hcorp:style/Base.TextAppearance.AppCompat.Headline = 0x7f110022
co.auter.hcorp:string/material_timepicker_clock_mode_description = 0x7f10009b
co.auter.hcorp:color/material_personalized_hint_foreground = 0x7f0502bb
co.auter.hcorp:attr/spinnerDropDownItemStyle = 0x7f0303ca
co.auter.hcorp:dimen/mtrl_extended_fab_translation_z_pressed = 0x7f0602b8
co.auter.hcorp:attr/hintTextColor = 0x7f030217
co.auter.hcorp:style/Base.ThemeOverlay.AppCompat.Dialog = 0x7f110082
co.auter.hcorp:style/Base.TextAppearance.AppCompat.Large.Inverse = 0x7f110025
co.auter.hcorp:dimen/m3_nav_badge_with_text_vertical_offset = 0x7f0601c0
co.auter.hcorp:attr/insetForeground = 0x7f030232
co.auter.hcorp:style/Base.Theme.AppCompat.CompactMenu = 0x7f11004f
co.auter.hcorp:color/m3_ref_palette_neutral70 = 0x7f050123
co.auter.hcorp:attr/endIconScaleType = 0x7f03019b
co.auter.hcorp:color/m3_dynamic_primary_text_disable_only = 0x7f050098
co.auter.hcorp:color/m3_sys_color_light_outline_variant = 0x7f0501f1
co.auter.hcorp:attr/hintTextAppearance = 0x7f030216
co.auter.hcorp:attr/actionBarSplitStyle = 0x7f030004
co.auter.hcorp:attr/hideOnScroll = 0x7f030213
co.auter.hcorp:attr/hideOnContentScroll = 0x7f030212
co.auter.hcorp:id/flip = 0x7f0800ca
co.auter.hcorp:id/accessibility_custom_action_29 = 0x7f080029
co.auter.hcorp:dimen/abc_action_bar_stacked_tab_max_width = 0x7f06000a
co.auter.hcorp:attr/gestureInsetBottomIgnored = 0x7f030205
co.auter.hcorp:style/Base.TextAppearance.AppCompat.Caption = 0x7f11001d
co.auter.hcorp:attr/prefixText = 0x7f030368
co.auter.hcorp:attr/materialCalendarHeaderToggleButton = 0x7f0302d2
co.auter.hcorp:attr/startIconCheckable = 0x7f0303d1
co.auter.hcorp:attr/textAppearanceBody1 = 0x7f03041d
co.auter.hcorp:style/ThemeOverlay.Material3.Button.TextButton.Snackbar = 0x7f1102c6
co.auter.hcorp:attr/collapsedTitleTextColor = 0x7f0300e1
co.auter.hcorp:interpolator/m3_sys_motion_easing_emphasized = 0x7f0a0007
co.auter.hcorp:attr/itemTextAppearanceActive = 0x7f030251
co.auter.hcorp:style/Widget.AppCompat.Spinner.DropDown.ActionBar = 0x7f110360
co.auter.hcorp:style/TextAppearance.Material3.SearchView = 0x7f110208
co.auter.hcorp:dimen/subtitle_shadow_radius = 0x7f060328
co.auter.hcorp:attr/behavior_autoHide = 0x7f03006c
co.auter.hcorp:attr/framePosition = 0x7f030203
co.auter.hcorp:attr/tickMark = 0x7f030463
co.auter.hcorp:attr/colorScheme = 0x7f030110
co.auter.hcorp:attr/contentScrim = 0x7f03013b
co.auter.hcorp:attr/customIntegerValue = 0x7f03015c
co.auter.hcorp:attr/forceDefaultNavigationOnClickListener = 0x7f030201
co.auter.hcorp:attr/boxStrokeWidth = 0x7f03008a
co.auter.hcorp:dimen/m3_sys_motion_easing_standard_control_y1 = 0x7f060217
co.auter.hcorp:attr/colorSurfaceContainerHighest = 0x7f03011a
co.auter.hcorp:attr/fontWeight = 0x7f0301ff
co.auter.hcorp:style/Theme.MaterialComponents.DayNight.Dialog.FixedSize.Bridge = 0x7f110283
co.auter.hcorp:macro/m3_comp_outlined_text_field_label_text_color = 0x7f0c00c2
co.auter.hcorp:color/material_dynamic_primary99 = 0x7f05025b
co.auter.hcorp:color/design_dark_default_color_primary = 0x7f05004b
co.auter.hcorp:style/TextAppearance.MaterialComponents.Body1 = 0x7f11020e
co.auter.hcorp:style/Base.Widget.AppCompat.ButtonBar = 0x7f1100d9
co.auter.hcorp:attr/startIconMinSize = 0x7f0303d4
co.auter.hcorp:attr/bottomSheetStyle = 0x7f030080
co.auter.hcorp:integer/app_bar_elevation_anim_duration = 0x7f090002
co.auter.hcorp:attr/fontProviderCerts = 0x7f0301f7
co.auter.hcorp:macro/m3_comp_outlined_button_hover_outline_color = 0x7f0c00a5
co.auter.hcorp:dimen/mtrl_slider_track_height = 0x7f0602f0
co.auter.hcorp:macro/m3_comp_sheet_side_detached_container_shape = 0x7f0c0107
co.auter.hcorp:dimen/m3_alert_dialog_elevation = 0x7f0600a3
co.auter.hcorp:styleable/OnClick = 0x7f12006d
co.auter.hcorp:attr/arrowHeadLength = 0x7f030039
co.auter.hcorp:attr/floatingActionButtonLargeSurfaceStyle = 0x7f0301d5
co.auter.hcorp:attr/flow_wrapMode = 0x7f0301f3
co.auter.hcorp:dimen/mtrl_low_ripple_hovered_alpha = 0x7f0602c3
co.auter.hcorp:attr/drawerLayoutCornerSize = 0x7f030186
co.auter.hcorp:styleable/RadialViewGroup = 0x7f120072
co.auter.hcorp:style/Theme.AppCompat.Light = 0x7f110231
co.auter.hcorp:attr/applyMotionScene = 0x7f030037
co.auter.hcorp:attr/flow_verticalAlign = 0x7f0301ef
co.auter.hcorp:attr/circleCrop = 0x7f0300cc
co.auter.hcorp:dimen/m3_comp_navigation_bar_hover_state_layer_opacity = 0x7f06013e
co.auter.hcorp:color/m3_sys_color_dynamic_on_primary_fixed = 0x7f0501d3
co.auter.hcorp:attr/showDividers = 0x7f0303b3
co.auter.hcorp:attr/flow_lastVerticalStyle = 0x7f0301ec
co.auter.hcorp:macro/m3_comp_top_app_bar_medium_headline_color = 0x7f0c016d
co.auter.hcorp:layout/abc_alert_dialog_title_material = 0x7f0b000a
co.auter.hcorp:id/notification_background = 0x7f080143
co.auter.hcorp:attr/trackColorActive = 0x7f03048a
co.auter.hcorp:attr/cursorColor = 0x7f030154
co.auter.hcorp:attr/contentPadding = 0x7f030134
co.auter.hcorp:attr/flow_lastHorizontalStyle = 0x7f0301ea
co.auter.hcorp:dimen/abc_action_bar_icon_vertical_padding_material = 0x7f060006
co.auter.hcorp:anim/abc_grow_fade_in_from_bottom = 0x7f010002
co.auter.hcorp:attr/useCompatPadding = 0x7f03049f
co.auter.hcorp:style/Theme.Material3.Light.Dialog.MinWidth = 0x7f110271
co.auter.hcorp:macro/m3_comp_sheet_bottom_docked_container_shape = 0x7f0c0105
co.auter.hcorp:dimen/design_navigation_separator_vertical_padding = 0x7f060080
co.auter.hcorp:style/MaterialAlertDialog.Material3 = 0x7f110130
co.auter.hcorp:macro/m3_comp_switch_selected_track_color = 0x7f0c012e
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral_variant90 = 0x7f0500da
co.auter.hcorp:string/google_storage_bucket = 0x7f100073
co.auter.hcorp:attr/colorSurfaceBright = 0x7f030117
co.auter.hcorp:string/abc_menu_enter_shortcut_label = 0x7f10000b
co.auter.hcorp:attr/flow_horizontalStyle = 0x7f0301e8
co.auter.hcorp:style/Widget.Material3.MaterialCalendar.HeaderLayout = 0x7f1103df
co.auter.hcorp:macro/m3_comp_navigation_rail_active_indicator_color = 0x7f0c0096
co.auter.hcorp:dimen/m3_comp_time_picker_time_selector_focus_state_layer_opacity = 0x7f0601a8
co.auter.hcorp:anim/rns_no_animation_medium = 0x7f010045
co.auter.hcorp:attr/flow_horizontalGap = 0x7f0301e7
co.auter.hcorp:macro/m3_comp_navigation_bar_inactive_icon_color = 0x7f0c0072
co.auter.hcorp:dimen/mtrl_exposed_dropdown_menu_popup_elevation = 0x7f0602a6
co.auter.hcorp:macro/m3_comp_switch_selected_icon_color = 0x7f0c0129
co.auter.hcorp:attr/defaultQueryHint = 0x7f030166
co.auter.hcorp:attr/isLightTheme = 0x7f030234
co.auter.hcorp:attr/flow_horizontalAlign = 0x7f0301e5
co.auter.hcorp:drawable/notify_panel_notification_icon_bg = 0x7f0700fe
co.auter.hcorp:attr/flow_firstVerticalBias = 0x7f0301e3
co.auter.hcorp:attr/flow_firstHorizontalStyle = 0x7f0301e2
co.auter.hcorp:macro/m3_comp_navigation_drawer_active_label_text_color = 0x7f0c0080
co.auter.hcorp:attr/flow_firstHorizontalBias = 0x7f0301e1
co.auter.hcorp:attr/extendedFloatingActionButtonStyle = 0x7f0301bc
co.auter.hcorp:attr/floatingActionButtonStyle = 0x7f0301de
co.auter.hcorp:color/design_default_color_primary_dark = 0x7f050059
co.auter.hcorp:style/MaterialAlertDialog.Material3.Title.Panel = 0x7f110136
co.auter.hcorp:attr/errorTextAppearance = 0x7f0301ab
co.auter.hcorp:attr/customDimension = 0x7f03015a
co.auter.hcorp:attr/hoveredFocusedTranslationZ = 0x7f03021c
co.auter.hcorp:attr/customNavigationLayout = 0x7f03015d
co.auter.hcorp:attr/floatingActionButtonSecondaryStyle = 0x7f0301d8
co.auter.hcorp:attr/tickColorInactive = 0x7f030462
co.auter.hcorp:attr/floatingActionButtonPrimaryStyle = 0x7f0301d7
co.auter.hcorp:style/Base.Widget.AppCompat.CompoundButton.RadioButton = 0x7f1100dc
co.auter.hcorp:attr/customColorValue = 0x7f030159
co.auter.hcorp:dimen/m3_sys_motion_easing_standard_decelerate_control_y1 = 0x7f06021b
co.auter.hcorp:attr/autofillInlineSuggestionStartIconStyle = 0x7f030047
co.auter.hcorp:attr/floatingActionButtonLargeTertiaryStyle = 0x7f0301d6
co.auter.hcorp:attr/gapBetweenBars = 0x7f030204
co.auter.hcorp:dimen/m3_comp_outlined_icon_button_unselected_outline_width = 0x7f060157
co.auter.hcorp:attr/layout_constraintCircleAngle = 0x7f030271
co.auter.hcorp:styleable/MaterialCalendarItem = 0x7f120054
co.auter.hcorp:attr/floatingActionButtonLargeStyle = 0x7f0301d4
co.auter.hcorp:id/selected = 0x7f080195
co.auter.hcorp:attr/splashScreenIconSize = 0x7f0303cc
co.auter.hcorp:drawable/common_google_signin_btn_text_light_normal = 0x7f070093
co.auter.hcorp:color/mtrl_tabs_icon_color_selector = 0x7f0502f7
co.auter.hcorp:attr/altSrc = 0x7f030031
co.auter.hcorp:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Centered = 0x7f11030a
co.auter.hcorp:attr/floatingActionButtonLargePrimaryStyle = 0x7f0301d2
co.auter.hcorp:attr/logoDescription = 0x7f0302ba
co.auter.hcorp:style/Platform.AppCompat = 0x7f110145
co.auter.hcorp:attr/postSplashScreenTheme = 0x7f030367
co.auter.hcorp:drawable/ic_m3_chip_checked_circle = 0x7f0700aa
co.auter.hcorp:mipmap/ic_launcher_foreground = 0x7f0d0001
co.auter.hcorp:attr/firstBaselineToTopHeight = 0x7f0301d1
co.auter.hcorp:style/Widget.AppCompat.PopupMenu.Overflow = 0x7f110353
co.auter.hcorp:color/m3_sys_color_light_error_container = 0x7f0501e1
co.auter.hcorp:string/material_hour_24h_suffix = 0x7f10008d
co.auter.hcorp:macro/m3_comp_navigation_drawer_inactive_hover_label_text_color = 0x7f0c008a
co.auter.hcorp:attr/fastScrollVerticalTrackDrawable = 0x7f0301d0
co.auter.hcorp:drawable/compat_splash_screen = 0x7f070095
co.auter.hcorp:integer/m3_btn_anim_delay_ms = 0x7f09000c
co.auter.hcorp:attr/fastScrollVerticalThumbDrawable = 0x7f0301cf
co.auter.hcorp:attr/fastScrollEnabled = 0x7f0301cc
co.auter.hcorp:attr/fabCradleMargin = 0x7f0301c4
co.auter.hcorp:style/Base.Theme.MaterialComponents.Dialog = 0x7f11006b
co.auter.hcorp:anim/catalyst_push_up_in = 0x7f01001a
co.auter.hcorp:id/SHOW_PATH = 0x7f080009
co.auter.hcorp:attr/actionModePopupWindowStyle = 0x7f030018
co.auter.hcorp:color/m3_sys_color_light_inverse_surface = 0x7f0501e4
co.auter.hcorp:attr/fabAnchorMode = 0x7f0301c2
co.auter.hcorp:attr/actionModePasteDrawable = 0x7f030017
co.auter.hcorp:attr/fabAlignmentMode = 0x7f0301c0
co.auter.hcorp:attr/counterOverflowTextAppearance = 0x7f03014d
co.auter.hcorp:attr/expandedTitleMarginStart = 0x7f0301b4
co.auter.hcorp:style/Base.V14.Widget.MaterialComponents.AutoCompleteTextView = 0x7f1100a6
co.auter.hcorp:attr/fastScrollHorizontalTrackDrawable = 0x7f0301ce
co.auter.hcorp:anim/abc_fade_in = 0x7f010000
co.auter.hcorp:dimen/abc_button_padding_vertical_material = 0x7f060015
co.auter.hcorp:styleable/SearchView = 0x7f12007a
co.auter.hcorp:animator/m3_chip_state_list_anim = 0x7f02000e
co.auter.hcorp:color/material_dynamic_tertiary50 = 0x7f05026f
co.auter.hcorp:attr/expandedHintEnabled = 0x7f0301af
co.auter.hcorp:drawable/abc_cab_background_top_material = 0x7f070038
co.auter.hcorp:style/ShapeAppearance.M3.Comp.SearchBar.Container.Shape = 0x7f11016f
co.auter.hcorp:attr/borderWidth = 0x7f030079
co.auter.hcorp:string/mtrl_picker_text_input_month_abbr = 0x7f1000d1
co.auter.hcorp:dimen/mtrl_snackbar_background_overlay_color_alpha = 0x7f0602f5
co.auter.hcorp:attr/expanded = 0x7f0301ae
co.auter.hcorp:drawable/common_google_signin_btn_text_dark = 0x7f07008c
co.auter.hcorp:attr/errorShown = 0x7f0301aa
co.auter.hcorp:attr/fabSize = 0x7f0301c8
co.auter.hcorp:color/common_google_signin_btn_tint = 0x7f050041
co.auter.hcorp:attr/constraintSet = 0x7f030127
co.auter.hcorp:dimen/design_snackbar_padding_vertical_2lines = 0x7f06008a
co.auter.hcorp:attr/errorIconTint = 0x7f0301a8
co.auter.hcorp:color/design_dark_default_color_background = 0x7f050044
co.auter.hcorp:attr/checkMarkTintMode = 0x7f0300ab
co.auter.hcorp:attr/checkedIconTint = 0x7f0300b4
co.auter.hcorp:layout/abc_screen_content_include = 0x7f0b0014
co.auter.hcorp:color/m3_timepicker_button_background_color = 0x7f05021b
co.auter.hcorp:style/Base.Widget.AppCompat.CompoundButton.Switch = 0x7f1100dd
co.auter.hcorp:attr/errorEnabled = 0x7f0301a6
co.auter.hcorp:attr/mock_showLabel = 0x7f030307
co.auter.hcorp:dimen/material_clock_period_toggle_height = 0x7f06022e
co.auter.hcorp:style/Widget.MaterialComponents.TextInputLayout.OutlinedBox.Dense.ExposedDropdownMenu = 0x7f110490
co.auter.hcorp:macro/m3_comp_navigation_bar_inactive_pressed_icon_color = 0x7f0c0074
co.auter.hcorp:animator/design_fab_hide_motion_spec = 0x7f020001
co.auter.hcorp:attr/wavePeriod = 0x7f0304ac
co.auter.hcorp:id/match_parent = 0x7f0800fe
co.auter.hcorp:attr/contentInsetStart = 0x7f030132
co.auter.hcorp:attr/listItemLayout = 0x7f0302ad
co.auter.hcorp:attr/errorContentDescription = 0x7f0301a5
co.auter.hcorp:attr/trackThickness = 0x7f030493
co.auter.hcorp:color/material_on_surface_disabled = 0x7f050287
co.auter.hcorp:id/startVertical = 0x7f0801b1
co.auter.hcorp:drawable/mtrl_switch_thumb_checked_pressed = 0x7f0700e6
co.auter.hcorp:style/Widget.Material3.ExtendedFloatingActionButton.Icon.Primary = 0x7f1103bd
co.auter.hcorp:attr/itemStrokeWidth = 0x7f03024f
co.auter.hcorp:dimen/fastscroll_margin = 0x7f060094
co.auter.hcorp:attr/chipStandaloneStyle = 0x7f0300c6
co.auter.hcorp:attr/buttonCompat = 0x7f030092
co.auter.hcorp:style/ThemeOverlay.Material3.PersonalizedColors = 0x7f1102e9
co.auter.hcorp:attr/fadeDuration = 0x7f0301c9
co.auter.hcorp:id/startToEnd = 0x7f0801b0
co.auter.hcorp:id/accessibility_custom_action_11 = 0x7f080016
co.auter.hcorp:attr/textAppearanceLabelLarge = 0x7f030430
co.auter.hcorp:string/abc_activitychooserview_choose_application = 0x7f100005
co.auter.hcorp:attr/editTextStyle = 0x7f03018f
co.auter.hcorp:id/linear = 0x7f0800f8
co.auter.hcorp:attr/enforceMaterialTheme = 0x7f03019e
co.auter.hcorp:color/material_personalized_color_on_secondary_container = 0x7f050299
co.auter.hcorp:id/navigation_bar_item_icon_view = 0x7f08013a
co.auter.hcorp:id/fill_vertical = 0x7f0800c0
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral94 = 0x7f0500c4
co.auter.hcorp:drawable/$mtrl_switch_thumb_checked_pressed__0 = 0x7f070020
co.auter.hcorp:color/m3_hint_foreground = 0x7f0500a0
co.auter.hcorp:style/Theme.MaterialComponents.NoActionBar.Bridge = 0x7f1102a3
co.auter.hcorp:attr/endIconMinSize = 0x7f030199
co.auter.hcorp:drawable/m3_bottom_sheet_drag_handle = 0x7f0700b9
co.auter.hcorp:color/mtrl_textinput_disabled_color = 0x7f0502fd
co.auter.hcorp:attr/enableEdgeToEdge = 0x7f030195
co.auter.hcorp:drawable/m3_tabs_background = 0x7f0700be
co.auter.hcorp:styleable/GradientColorItem = 0x7f12003d
co.auter.hcorp:attr/defaultMarginsEnabled = 0x7f030165
co.auter.hcorp:drawable/common_google_signin_btn_icon_light_normal_background = 0x7f07008b
co.auter.hcorp:attr/emojiCompatEnabled = 0x7f030194
co.auter.hcorp:style/TextAppearance.Compat.Notification.Title.Media = 0x7f1101dd
co.auter.hcorp:integer/material_motion_duration_long_1 = 0x7f090028
co.auter.hcorp:attr/elevationOverlayEnabled = 0x7f030193
co.auter.hcorp:anim/btn_radio_to_on_mtrl_ring_outer_path_animation = 0x7f010017
co.auter.hcorp:attr/elevation = 0x7f030190
co.auter.hcorp:drawable/abc_btn_borderless_material = 0x7f07002a
co.auter.hcorp:dimen/m3_bottom_sheet_drag_handle_bottom_padding = 0x7f0600c5
co.auter.hcorp:attr/editTextColor = 0x7f03018e
co.auter.hcorp:attr/badgeTextColor = 0x7f03005f
co.auter.hcorp:attr/showAnimationBehavior = 0x7f0303b0
co.auter.hcorp:attr/dynamicColorThemeOverlay = 0x7f03018c
co.auter.hcorp:attr/chipSpacingVertical = 0x7f0300c5
co.auter.hcorp:attr/hideNavigationIcon = 0x7f030211
co.auter.hcorp:dimen/abc_config_prefDialogWidth = 0x7f060017
co.auter.hcorp:dimen/mtrl_progress_circular_inset_medium = 0x7f0602d9
co.auter.hcorp:macro/m3_comp_search_bar_pressed_supporting_text_color = 0x7f0c00ed
co.auter.hcorp:macro/m3_comp_navigation_bar_inactive_label_text_color = 0x7f0c0073
co.auter.hcorp:attr/drawableTintMode = 0x7f030183
co.auter.hcorp:attr/drawableTint = 0x7f030182
co.auter.hcorp:attr/drawableRightCompat = 0x7f03017f
co.auter.hcorp:attr/drawableEndCompat = 0x7f03017d
co.auter.hcorp:style/ShapeAppearance.Material3.NavigationBarView.ActiveIndicator = 0x7f110186
co.auter.hcorp:id/design_menu_item_text = 0x7f08009c
co.auter.hcorp:dimen/m3_comp_navigation_bar_active_indicator_width = 0x7f06013a
co.auter.hcorp:attr/layout_constraintStart_toStartOf = 0x7f030287
co.auter.hcorp:attr/colorOutline = 0x7f030106
co.auter.hcorp:style/ShapeAppearance.M3.Sys.Shape.Corner.ExtraLarge = 0x7f110176
co.auter.hcorp:animator/mtrl_btn_unelevated_state_list_anim = 0x7f020016
co.auter.hcorp:color/m3_sys_color_on_primary_fixed_variant = 0x7f050202
co.auter.hcorp:style/Widget.Material3.MaterialTimePicker.ImageButton = 0x7f1103f6
co.auter.hcorp:attr/dragScale = 0x7f030179
co.auter.hcorp:attr/dividerVertical = 0x7f030177
co.auter.hcorp:style/Widget.MaterialComponents.ActionBar.Solid = 0x7f110426
co.auter.hcorp:id/peekHeight = 0x7f080161
co.auter.hcorp:dimen/mtrl_switch_thumb_elevation = 0x7f0602fa
co.auter.hcorp:style/Widget.AppCompat.EditText = 0x7f110335
co.auter.hcorp:style/Widget.AppCompat.ActionButton.CloseMode = 0x7f110323
co.auter.hcorp:attr/dividerThickness = 0x7f030176
co.auter.hcorp:style/Widget.Autofill.InlineSuggestionEndIconStyle = 0x7f110368
co.auter.hcorp:drawable/ic_mtrl_chip_close_circle = 0x7f0700af
co.auter.hcorp:drawable/abc_ic_menu_share_mtrl_alpha = 0x7f070047
co.auter.hcorp:style/ShapeAppearanceOverlay.Material3.Corner.Top = 0x7f110194
co.auter.hcorp:attr/colorSurfaceContainerHigh = 0x7f030119
co.auter.hcorp:attr/offsetAlignmentMode = 0x7f03033a
co.auter.hcorp:attr/actionModeWebSearchDrawable = 0x7f03001e
co.auter.hcorp:attr/buttonIconTint = 0x7f030096
co.auter.hcorp:attr/dividerPadding = 0x7f030175
co.auter.hcorp:style/Base.Widget.Material3.TabLayout.OnSurface = 0x7f110115
co.auter.hcorp:style/Base.Theme.Material3.Light.Dialog = 0x7f110064
co.auter.hcorp:macro/m3_comp_snackbar_supporting_text_color = 0x7f0c0115
co.auter.hcorp:attr/headerLayout = 0x7f030209
co.auter.hcorp:attr/textAppearanceDisplaySmall = 0x7f030426
co.auter.hcorp:macro/m3_comp_navigation_drawer_inactive_hover_icon_color = 0x7f0c0089
co.auter.hcorp:drawable/abc_ic_search_api_material = 0x7f070048
co.auter.hcorp:attr/font = 0x7f0301f4
co.auter.hcorp:dimen/m3_searchbar_text_margin_start_no_navigation_icon = 0x7f0601e3
co.auter.hcorp:dimen/m3_comp_radio_button_selected_hover_state_layer_opacity = 0x7f06016b
co.auter.hcorp:id/accessibility_custom_action_19 = 0x7f08001e
co.auter.hcorp:dimen/m3_comp_navigation_rail_active_indicator_height = 0x7f060148
co.auter.hcorp:attr/dividerHorizontal = 0x7f030172
co.auter.hcorp:attr/constraint_referenced_ids = 0x7f03012a
co.auter.hcorp:color/m3_ref_palette_neutral_variant100 = 0x7f05012f
co.auter.hcorp:attr/dividerColor = 0x7f030171
co.auter.hcorp:attr/divider = 0x7f030170
co.auter.hcorp:style/Theme.MaterialComponents.Dialog.Bridge = 0x7f11028c
co.auter.hcorp:macro/m3_comp_switch_unselected_focus_icon_color = 0x7f0c0130
co.auter.hcorp:dimen/m3_comp_navigation_rail_container_width = 0x7f06014b
co.auter.hcorp:style/RtlOverlay.Widget.AppCompat.Search.DropDown.Text = 0x7f110160
co.auter.hcorp:id/wrap = 0x7f080200
co.auter.hcorp:id/withinBounds = 0x7f0801ff
co.auter.hcorp:color/common_google_signin_btn_text_light_pressed = 0x7f050040
co.auter.hcorp:dimen/m3_sys_motion_easing_linear_control_y2 = 0x7f060210
co.auter.hcorp:styleable/CardView = 0x7f12001c
co.auter.hcorp:string/catalyst_settings_title = 0x7f100047
co.auter.hcorp:attr/simpleItemSelectedColor = 0x7f0303bd
co.auter.hcorp:color/material_dynamic_tertiary10 = 0x7f05026a
co.auter.hcorp:style/ShapeAppearance.M3.Sys.Shape.Corner.None = 0x7f11017b
co.auter.hcorp:attr/deriveConstraintsFrom = 0x7f03016b
co.auter.hcorp:dimen/mtrl_high_ripple_default_alpha = 0x7f0602bd
co.auter.hcorp:anim/rns_slide_out_to_bottom = 0x7f010049
co.auter.hcorp:styleable/FontFamily = 0x7f120036
co.auter.hcorp:attr/colorSecondaryVariant = 0x7f030115
co.auter.hcorp:style/TextAppearance.Material3.SearchBar = 0x7f110207
co.auter.hcorp:attr/motionProgress = 0x7f030328
co.auter.hcorp:string/link_description = 0x7f10007a
co.auter.hcorp:attr/actionMenuTextColor = 0x7f03000f
co.auter.hcorp:style/Base.V14.Theme.MaterialComponents.Light = 0x7f11009b
co.auter.hcorp:layout/abc_action_mode_close_item_material = 0x7f0b0005
co.auter.hcorp:animator/m3_card_elevated_state_list_anim = 0x7f02000c
co.auter.hcorp:style/Theme.MaterialComponents.Dialog.Alert.Bridge = 0x7f11028b
co.auter.hcorp:attr/circleRadius = 0x7f0300cd
co.auter.hcorp:attr/badgeShapeAppearance = 0x7f03005a
co.auter.hcorp:attr/defaultScrollFlagsEnabled = 0x7f030167
co.auter.hcorp:style/ThemeOverlay.Material3.ExtendedFloatingActionButton.Secondary = 0x7f1102d5
co.auter.hcorp:color/catalyst_logbox_background = 0x7f050033
co.auter.hcorp:id/staticPostLayout = 0x7f0801b3
co.auter.hcorp:attr/layout_constraintEnd_toStartOf = 0x7f030275
co.auter.hcorp:attr/itemShapeInsetStart = 0x7f03024b
co.auter.hcorp:macro/m3_comp_date_picker_modal_range_selection_active_indicator_container_color = 0x7f0c0019
co.auter.hcorp:color/design_dark_default_color_on_background = 0x7f050046
co.auter.hcorp:attr/boxStrokeWidthFocused = 0x7f03008b
co.auter.hcorp:attr/daySelectedStyle = 0x7f030161
co.auter.hcorp:dimen/m3_comp_time_picker_period_selector_focus_state_layer_opacity = 0x7f0601a4
co.auter.hcorp:color/foreground_material_light = 0x7f05006f
co.auter.hcorp:attr/maxActionInlineWidth = 0x7f0302f0
co.auter.hcorp:style/Base.Theme.AppCompat.DialogWhenLarge = 0x7f110054
co.auter.hcorp:attr/layout_constraintLeft_toLeftOf = 0x7f030281
co.auter.hcorp:dimen/mtrl_shape_corner_size_medium_component = 0x7f0602e6
co.auter.hcorp:dimen/m3_comp_filled_card_icon_size = 0x7f06012b
co.auter.hcorp:style/Widget.AppCompat.AutoCompleteTextView = 0x7f110327
co.auter.hcorp:macro/m3_comp_navigation_bar_inactive_hover_label_text_color = 0x7f0c0070
co.auter.hcorp:id/open_search_view_scrim = 0x7f080151
co.auter.hcorp:color/design_dark_default_color_secondary_variant = 0x7f05004f
co.auter.hcorp:attr/fontProviderSystemFontFamily = 0x7f0301fc
co.auter.hcorp:string/rn_tab_description = 0x7f1000ea
co.auter.hcorp:dimen/m3_sys_motion_easing_legacy_decelerate_control_x2 = 0x7f06020a
co.auter.hcorp:color/m3_ref_palette_primary20 = 0x7f05013d
co.auter.hcorp:color/m3_ref_palette_dynamic_tertiary0 = 0x7f0500fb
co.auter.hcorp:style/Widget.MaterialComponents.MaterialCalendar.DayTextView = 0x7f110460
co.auter.hcorp:attr/extendedFloatingActionButtonSurfaceStyle = 0x7f0301bd
co.auter.hcorp:attr/customFloatValue = 0x7f03015b
co.auter.hcorp:style/ShapeAppearance.M3.Sys.Shape.Corner.Full = 0x7f110178
co.auter.hcorp:style/Base.Widget.AppCompat.Button.Small = 0x7f1100d8
co.auter.hcorp:attr/layout_goneMarginBottom = 0x7f030296
co.auter.hcorp:attr/curveFit = 0x7f030156
co.auter.hcorp:attr/backgroundInsetEnd = 0x7f03004f
co.auter.hcorp:dimen/mtrl_low_ripple_default_alpha = 0x7f0602c1
co.auter.hcorp:dimen/m3_btn_text_btn_icon_padding_right = 0x7f0600e2
co.auter.hcorp:style/Widget.Autofill.InlineSuggestionStartIconStyle = 0x7f110369
co.auter.hcorp:id/SHOW_ALL = 0x7f080008
co.auter.hcorp:dimen/item_touch_helper_swipe_escape_velocity = 0x7f06009f
co.auter.hcorp:attr/crossfade = 0x7f030151
co.auter.hcorp:string/state_on_description = 0x7f1000f8
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral60 = 0x7f0500be
co.auter.hcorp:macro/m3_comp_time_picker_time_selector_separator_color = 0x7f0c0165
co.auter.hcorp:attr/motionDurationLong4 = 0x7f030310
co.auter.hcorp:attr/colorError = 0x7f0300ef
co.auter.hcorp:style/Theme.Material3.Light.Dialog = 0x7f11026f
co.auter.hcorp:animator/mtrl_fab_show_motion_spec = 0x7f02001f
co.auter.hcorp:attr/iconGravity = 0x7f03021f
co.auter.hcorp:style/Widget.MaterialComponents.ExtendedFloatingActionButton.Icon = 0x7f110455
co.auter.hcorp:drawable/ic_arrow_back_black_24 = 0x7f07009e
co.auter.hcorp:styleable/ClockHandView = 0x7f120023
co.auter.hcorp:attr/materialIconButtonFilledTonalStyle = 0x7f0302e2
co.auter.hcorp:id/normal = 0x7f080142
co.auter.hcorp:attr/counterMaxLength = 0x7f03014c
co.auter.hcorp:dimen/design_snackbar_action_text_color_alpha = 0x7f060082
co.auter.hcorp:attr/cornerSizeBottomLeft = 0x7f030147
co.auter.hcorp:id/action_image = 0x7f080045
co.auter.hcorp:dimen/m3_comp_fab_primary_focus_state_layer_opacity = 0x7f06011a
co.auter.hcorp:macro/m3_comp_switch_unselected_focus_track_color = 0x7f0c0132
co.auter.hcorp:attr/cornerSize = 0x7f030146
co.auter.hcorp:color/m3_sys_color_dynamic_light_on_error_container = 0x7f0501b9
co.auter.hcorp:layout/mtrl_navigation_rail_item = 0x7f0b005a
co.auter.hcorp:attr/searchHintIcon = 0x7f03039a
co.auter.hcorp:attr/cornerRadius = 0x7f030145
co.auter.hcorp:attr/elevationOverlayColor = 0x7f030192
co.auter.hcorp:styleable/Transition = 0x7f120094
co.auter.hcorp:id/leftToRight = 0x7f0800f3
co.auter.hcorp:color/material_personalized_color_secondary_container = 0x7f0502a7
co.auter.hcorp:dimen/m3_comp_fab_primary_small_icon_size = 0x7f060123
co.auter.hcorp:attr/cornerFamilyTopRight = 0x7f030144
co.auter.hcorp:dimen/m3_sys_motion_easing_emphasized_decelerate_control_x1 = 0x7f0601fd
co.auter.hcorp:macro/m3_comp_switch_unselected_handle_color = 0x7f0c0134
co.auter.hcorp:color/m3_sys_color_dark_surface_variant = 0x7f05018c
co.auter.hcorp:styleable/MaterialToolbar = 0x7f12005f
co.auter.hcorp:style/Widget.MaterialComponents.AutoCompleteTextView.FilledBox = 0x7f11042c
co.auter.hcorp:attr/iconTintMode = 0x7f030224
co.auter.hcorp:attr/backgroundStacked = 0x7f030054
co.auter.hcorp:attr/paddingBottomNoButtons = 0x7f030344
co.auter.hcorp:attr/controlBackground = 0x7f03013d
co.auter.hcorp:style/Widget.Material3.NavigationRailView.ActiveIndicator = 0x7f1103f8
co.auter.hcorp:attr/colorSurface = 0x7f030116
co.auter.hcorp:id/start = 0x7f0801ae
co.auter.hcorp:id/accessibility_custom_action_18 = 0x7f08001d
co.auter.hcorp:attr/colorSwitchThumbNormal = 0x7f030120
co.auter.hcorp:attr/actionModeStyle = 0x7f03001c
co.auter.hcorp:animator/m3_extended_fab_change_size_expand_motion_spec = 0x7f020011
co.auter.hcorp:attr/overlay = 0x7f030342
co.auter.hcorp:macro/m3_comp_search_view_header_supporting_text_type = 0x7f0c00f8
co.auter.hcorp:attr/materialCalendarHeaderConfirmButton = 0x7f0302cd
co.auter.hcorp:macro/m3_comp_time_picker_period_selector_outline_color = 0x7f0c0154
co.auter.hcorp:attr/indicatorDirectionCircular = 0x7f03022c
co.auter.hcorp:drawable/mtrl_checkbox_button_icon_unchecked_indeterminate = 0x7f0700d5
co.auter.hcorp:anim/m3_side_sheet_enter_from_left = 0x7f01002b
co.auter.hcorp:attr/contrast = 0x7f03013c
co.auter.hcorp:attr/contentPaddingStart = 0x7f030139
co.auter.hcorp:dimen/m3_extended_fab_bottom_padding = 0x7f0601b2
co.auter.hcorp:layout/abc_list_menu_item_layout = 0x7f0b0010
co.auter.hcorp:attr/layout_constraintTop_toBottomOf = 0x7f03028a
co.auter.hcorp:color/m3_navigation_bar_item_with_indicator_icon_tint = 0x7f0500a2
co.auter.hcorp:dimen/m3_btn_translation_z_hovered = 0x7f0600e6
co.auter.hcorp:attr/contentPaddingEnd = 0x7f030136
co.auter.hcorp:id/mtrl_child_content_container = 0x7f08012a
co.auter.hcorp:dimen/m3_large_fab_size = 0x7f0601bd
co.auter.hcorp:macro/m3_comp_navigation_rail_inactive_icon_color = 0x7f0c009c
co.auter.hcorp:attr/cardBackgroundColor = 0x7f03009e
co.auter.hcorp:attr/useDrawerArrowDrawable = 0x7f0304a0
co.auter.hcorp:attr/contentInsetStartWithNavigation = 0x7f030133
co.auter.hcorp:drawable/abc_list_longpressed_holo = 0x7f07004f
co.auter.hcorp:integer/mtrl_switch_thumb_pre_morphing_duration = 0x7f09003a
co.auter.hcorp:dimen/mtrl_progress_circular_size = 0x7f0602dc
co.auter.hcorp:color/m3_sys_color_dynamic_on_secondary_fixed = 0x7f0501d5
co.auter.hcorp:attr/windowSplashScreenAnimationDuration = 0x7f0304ba
co.auter.hcorp:attr/lineHeight = 0x7f0302a6
co.auter.hcorp:id/open_search_view_content_container = 0x7f08014b
co.auter.hcorp:attr/layout_constraintCircleRadius = 0x7f030272
co.auter.hcorp:attr/state_dragged = 0x7f0303db
co.auter.hcorp:macro/m3_comp_outlined_text_field_hover_input_text_color = 0x7f0c00bd
co.auter.hcorp:attr/maxCharacterCount = 0x7f0302f2
co.auter.hcorp:attr/haloRadius = 0x7f030208
co.auter.hcorp:attr/failureImage = 0x7f0301ca
co.auter.hcorp:attr/actionBarWidgetTheme = 0x7f03000a
co.auter.hcorp:string/androidx_startup = 0x7f10001e
co.auter.hcorp:attr/lineSpacing = 0x7f0302a7
co.auter.hcorp:attr/switchPadding = 0x7f0303f8
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral_variant70 = 0x7f0500d7
co.auter.hcorp:color/notification_icon_bg_color = 0x7f050303
co.auter.hcorp:attr/customColorDrawableValue = 0x7f030158
co.auter.hcorp:attr/thumbIconTintMode = 0x7f030457
co.auter.hcorp:attr/constraintSetEnd = 0x7f030128
co.auter.hcorp:id/material_clock_period_toggle = 0x7f080106
co.auter.hcorp:attr/useMaterialThemeColors = 0x7f0304a1
co.auter.hcorp:styleable/ActivityChooserView = 0x7f120005
co.auter.hcorp:attr/actualImageResource = 0x7f030026
co.auter.hcorp:attr/materialCardViewElevatedStyle = 0x7f0302d8
co.auter.hcorp:attr/autoSizePresetSizes = 0x7f030041
co.auter.hcorp:dimen/m3_comp_primary_navigation_tab_active_pressed_state_layer_opacity = 0x7f060160
co.auter.hcorp:macro/m3_comp_slider_disabled_inactive_track_color = 0x7f0c010e
co.auter.hcorp:layout/notification_action = 0x7f0b0067
co.auter.hcorp:color/m3_sys_color_light_on_surface = 0x7f0501ec
co.auter.hcorp:attr/buttonStyle = 0x7f03009a
co.auter.hcorp:dimen/m3_chip_elevated_elevation = 0x7f0600fa
co.auter.hcorp:attr/checkedIconGravity = 0x7f0300b1
co.auter.hcorp:attr/layout_optimizationLevel = 0x7f03029e
co.auter.hcorp:macro/m3_comp_plain_tooltip_supporting_text_type = 0x7f0c00c6
co.auter.hcorp:dimen/m3_comp_outlined_card_outline_width = 0x7f060156
co.auter.hcorp:style/Base.TextAppearance.AppCompat.SearchResult = 0x7f11002b
co.auter.hcorp:dimen/m3_bottom_nav_item_padding_top = 0x7f0600c3
co.auter.hcorp:attr/floatingActionButtonSurfaceStyle = 0x7f0301df
co.auter.hcorp:attr/layout_goneMarginRight = 0x7f030299
co.auter.hcorp:attr/itemVerticalPadding = 0x7f030255
co.auter.hcorp:attr/chipIconSize = 0x7f0300be
co.auter.hcorp:attr/expandedTitleMarginEnd = 0x7f0301b3
co.auter.hcorp:color/m3_ref_palette_dynamic_tertiary99 = 0x7f050107
co.auter.hcorp:style/TextAppearance.M3.Sys.Typescale.TitleMedium = 0x7f1101f6
co.auter.hcorp:attr/yearStyle = 0x7f0304be
co.auter.hcorp:attr/actionBarTabStyle = 0x7f030007
co.auter.hcorp:drawable/mtrl_checkbox_button_unchecked_checked = 0x7f0700d6
co.auter.hcorp:style/Base.V21.Theme.MaterialComponents = 0x7f1100ab
co.auter.hcorp:color/m3_sys_color_light_surface_container_high = 0x7f0501f9
co.auter.hcorp:macro/m3_comp_date_picker_modal_header_supporting_text_color = 0x7f0c0017
co.auter.hcorp:attr/measureWithLargestChild = 0x7f0302f9
co.auter.hcorp:style/Base.V14.ThemeOverlay.Material3.SideSheetDialog = 0x7f1100a1
co.auter.hcorp:attr/state_collapsed = 0x7f0303d9
co.auter.hcorp:attr/barrierMargin = 0x7f03006b
co.auter.hcorp:attr/badgeWithTextWidth = 0x7f030067
co.auter.hcorp:id/accessibility_custom_action_14 = 0x7f080019
co.auter.hcorp:color/m3_tabs_icon_color = 0x7f05020d
co.auter.hcorp:attr/materialCardViewFilledStyle = 0x7f0302d9
co.auter.hcorp:attr/checkMarkTint = 0x7f0300aa
co.auter.hcorp:attr/colorSurfaceDim = 0x7f03011d
co.auter.hcorp:layout/autofill_inline_suggestion = 0x7f0b001d
co.auter.hcorp:color/m3_sys_color_dynamic_light_on_primary_container = 0x7f0501bb
co.auter.hcorp:attr/titleMarginStart = 0x7f030473
co.auter.hcorp:style/Widget.Material3.MaterialCalendar.HeaderLayout.Fullscreen = 0x7f1103e0
co.auter.hcorp:attr/layout_constraintGuide_begin = 0x7f030276
co.auter.hcorp:macro/m3_comp_date_picker_modal_year_selection_year_selected_label_text_color = 0x7f0c0020
co.auter.hcorp:attr/tabPaddingStart = 0x7f03040e
co.auter.hcorp:dimen/m3_comp_date_picker_modal_date_today_container_outline_width = 0x7f060107
co.auter.hcorp:color/material_dynamic_color_dark_on_error_container = 0x7f050230
co.auter.hcorp:string/expo_splash_screen_status_bar_translucent = 0x7f100066
co.auter.hcorp:attr/fontVariationSettings = 0x7f0301fe
co.auter.hcorp:dimen/mtrl_btn_elevation = 0x7f060260
co.auter.hcorp:color/abc_primary_text_disable_only_material_dark = 0x7f050009
co.auter.hcorp:attr/badgeWithTextRadius = 0x7f030064
co.auter.hcorp:attr/barrierDirection = 0x7f03006a
co.auter.hcorp:drawable/mtrl_checkbox_button_icon_checked_indeterminate = 0x7f0700d0
co.auter.hcorp:attr/colorSecondaryContainer = 0x7f030112
co.auter.hcorp:style/Widget.MaterialComponents.TextInputLayout.FilledBox.Dense = 0x7f11048b
co.auter.hcorp:attr/badgeShapeAppearanceOverlay = 0x7f03005b
co.auter.hcorp:attr/colorPrimaryContainer = 0x7f030109
co.auter.hcorp:color/mtrl_tabs_ripple_color = 0x7f0502fa
co.auter.hcorp:attr/contentPaddingTop = 0x7f03013a
co.auter.hcorp:attr/listDividerAlertDialog = 0x7f0302ac
co.auter.hcorp:color/material_dynamic_secondary90 = 0x7f050266
co.auter.hcorp:attr/colorTertiaryFixedDim = 0x7f030124
co.auter.hcorp:id/clear_text = 0x7f080080
co.auter.hcorp:anim/design_bottom_sheet_slide_out = 0x7f01001f
co.auter.hcorp:id/autoComplete = 0x7f08005c
co.auter.hcorp:anim/rns_fade_in = 0x7f010037
co.auter.hcorp:macro/m3_comp_date_picker_modal_year_selection_year_unselected_label_text_color = 0x7f0c0021
co.auter.hcorp:attr/collapsingToolbarLayoutMediumSize = 0x7f0300e4
co.auter.hcorp:attr/colorOutlineVariant = 0x7f030107
co.auter.hcorp:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle.Inverse = 0x7f1101c7
co.auter.hcorp:dimen/m3_bottomappbar_fab_cradle_vertical_offset = 0x7f0600ca
co.auter.hcorp:id/mtrl_picker_header_toggle = 0x7f080131
co.auter.hcorp:color/m3_sys_color_dark_outline_variant = 0x7f05017f
co.auter.hcorp:attr/dividerInsetStart = 0x7f030174
co.auter.hcorp:style/Widget.Material3.AutoCompleteTextView.FilledBox.Dense = 0x7f11037d
co.auter.hcorp:dimen/material_divider_thickness = 0x7f060235
co.auter.hcorp:color/m3_slider_thumb_color_legacy = 0x7f05016a
co.auter.hcorp:attr/colorOnTertiaryFixed = 0x7f030104
co.auter.hcorp:dimen/mtrl_btn_text_btn_padding_left = 0x7f060270
co.auter.hcorp:attr/textAppearanceBodyMedium = 0x7f030420
co.auter.hcorp:style/Widget.MaterialComponents.TimePicker.ImageButton.ShapeAppearance = 0x7f11049c
co.auter.hcorp:attr/maxVelocity = 0x7f0302f7
co.auter.hcorp:style/Base.v21.Theme.SplashScreen.Light = 0x7f110126
co.auter.hcorp:string/abc_prepend_shortcut_label = 0x7f100011
co.auter.hcorp:attr/selectableItemBackgroundBorderless = 0x7f0303a0
co.auter.hcorp:style/Platform.V25.AppCompat.Light = 0x7f110151
co.auter.hcorp:attr/colorOnTertiaryContainer = 0x7f030103
co.auter.hcorp:attr/imageAspectRatioAdjust = 0x7f030227
co.auter.hcorp:anim/abc_slide_in_bottom = 0x7f010006
co.auter.hcorp:attr/defaultState = 0x7f030168
co.auter.hcorp:attr/colorOnContainer = 0x7f0300f2
co.auter.hcorp:color/design_default_color_on_background = 0x7f050053
co.auter.hcorp:attr/srcCompat = 0x7f0303ce
co.auter.hcorp:id/month_grid = 0x7f080119
co.auter.hcorp:attr/errorTextColor = 0x7f0301ac
co.auter.hcorp:id/floating = 0x7f0800cb
co.auter.hcorp:attr/liftOnScrollTargetViewId = 0x7f0302a4
co.auter.hcorp:style/Theme.Material3.Light.DialogWhenLarge = 0x7f110272
co.auter.hcorp:style/ShapeAppearance.Material3.Tooltip = 0x7f110188
co.auter.hcorp:id/sin = 0x7f08019b
co.auter.hcorp:id/accessibility_custom_action_16 = 0x7f08001b
co.auter.hcorp:attr/colorOnBackground = 0x7f0300f1
co.auter.hcorp:attr/shrinkMotionSpec = 0x7f0303b9
co.auter.hcorp:style/Base.Widget.MaterialComponents.Slider = 0x7f110120
co.auter.hcorp:attr/colorErrorContainer = 0x7f0300f0
co.auter.hcorp:dimen/m3_chip_corner_size = 0x7f0600f7
co.auter.hcorp:color/abc_hint_foreground_material_dark = 0x7f050007
co.auter.hcorp:attr/badgeText = 0x7f03005d
co.auter.hcorp:attr/compatShadowEnabled = 0x7f030126
co.auter.hcorp:attr/colorSecondary = 0x7f030111
co.auter.hcorp:style/Widget.MaterialComponents.MaterialCalendar.Year = 0x7f11046e
co.auter.hcorp:attr/textAppearanceBody2 = 0x7f03041e
co.auter.hcorp:dimen/material_clock_display_padding = 0x7f060226
co.auter.hcorp:color/m3_navigation_item_text_color = 0x7f0500a8
co.auter.hcorp:layout/material_clock_display = 0x7f0b003a
co.auter.hcorp:attr/onPositiveCross = 0x7f03033e
co.auter.hcorp:attr/buttonSize = 0x7f030099
co.auter.hcorp:dimen/m3_comp_filled_card_hover_state_layer_opacity = 0x7f06012a
co.auter.hcorp:style/Base.Widget.AppCompat.ActionButton = 0x7f1100cd
co.auter.hcorp:color/m3_sys_color_dynamic_light_inverse_primary = 0x7f0501b5
co.auter.hcorp:styleable/TabLayout = 0x7f12008c
co.auter.hcorp:drawable/notification_tile_bg = 0x7f0700fd
co.auter.hcorp:attr/color = 0x7f0300e7
co.auter.hcorp:style/ThemeOverlay.Material3.Dialog = 0x7f1102ce
co.auter.hcorp:color/design_fab_stroke_end_inner_color = 0x7f050062
co.auter.hcorp:id/cos = 0x7f08008c
co.auter.hcorp:attr/shouldRemoveExpandedCorners = 0x7f0303af
co.auter.hcorp:dimen/mtrl_navigation_item_shape_vertical_margin = 0x7f0602cd
co.auter.hcorp:color/m3_sys_color_dynamic_light_on_tertiary = 0x7f0501c0
co.auter.hcorp:attr/number = 0x7f030338
co.auter.hcorp:color/material_dynamic_tertiary99 = 0x7f050275
co.auter.hcorp:macro/m3_comp_time_picker_time_selector_selected_label_text_color = 0x7f0c0163
co.auter.hcorp:attr/shortcutMatchRequired = 0x7f0303ae
co.auter.hcorp:attr/trackColor = 0x7f030489
co.auter.hcorp:attr/titleTextColor = 0x7f030478
co.auter.hcorp:attr/counterTextColor = 0x7f030150
co.auter.hcorp:style/Widget.MaterialComponents.AutoCompleteTextView.OutlinedBox.Dense = 0x7f11042f
co.auter.hcorp:color/m3_fab_efab_background_color_selector = 0x7f05009b
co.auter.hcorp:attr/toolbarId = 0x7f03047c
co.auter.hcorp:attr/closeIconSize = 0x7f0300d7
co.auter.hcorp:attr/passwordToggleContentDescription = 0x7f030350
co.auter.hcorp:macro/m3_comp_extended_fab_tertiary_icon_color = 0x7f0c0035
co.auter.hcorp:layout/paused_in_debugger_view = 0x7f0b0076
co.auter.hcorp:attr/collapsedTitleTextAppearance = 0x7f0300e0
co.auter.hcorp:style/Widget.AppCompat.Toolbar = 0x7f110364
co.auter.hcorp:layout/material_time_input = 0x7f0b0043
co.auter.hcorp:animator/mtrl_card_state_list_anim = 0x7f020017
co.auter.hcorp:style/Base.Widget.AppCompat.ActionButton.CloseMode = 0x7f1100ce
co.auter.hcorp:macro/m3_comp_text_button_focus_state_layer_color = 0x7f0c0142
co.auter.hcorp:color/browser_actions_title_color = 0x7f05002a
co.auter.hcorp:drawable/abc_btn_check_to_on_mtrl_015 = 0x7f07002e
co.auter.hcorp:layout/abc_search_view = 0x7f0b0019
co.auter.hcorp:attr/thumbTrackGapSize = 0x7f03045e
co.auter.hcorp:dimen/highlight_alpha_material_colored = 0x7f060096
co.auter.hcorp:color/material_dynamic_neutral_variant100 = 0x7f050244
co.auter.hcorp:string/searchview_clear_text_content_description = 0x7f1000ee
co.auter.hcorp:anim/rns_standard_accelerate_interpolator = 0x7f01004c
co.auter.hcorp:attr/closeItemLayout = 0x7f0300db
co.auter.hcorp:color/androidx_core_secondary_text_default_material_light = 0x7f05001c
co.auter.hcorp:macro/m3_comp_outlined_text_field_focus_outline_color = 0x7f0c00bb
co.auter.hcorp:attr/trackDecoration = 0x7f03048d
co.auter.hcorp:attr/roundingBorderColor = 0x7f030392
co.auter.hcorp:attr/buttonBarPositiveButtonStyle = 0x7f030090
co.auter.hcorp:attr/itemPaddingBottom = 0x7f030243
co.auter.hcorp:dimen/m3_btn_disabled_elevation = 0x7f0600d0
co.auter.hcorp:dimen/abc_text_size_title_material_toolbar = 0x7f060050
co.auter.hcorp:attr/subheaderInsetEnd = 0x7f0303e8
co.auter.hcorp:style/Widget.Autofill = 0x7f110366
co.auter.hcorp:attr/clockFaceBackgroundColor = 0x7f0300d0
co.auter.hcorp:attr/layout_constraintHorizontal_bias = 0x7f03027d
co.auter.hcorp:attr/materialCalendarHeaderDivider = 0x7f0302ce
co.auter.hcorp:attr/itemHorizontalPadding = 0x7f03023b
co.auter.hcorp:style/Base.Widget.MaterialComponents.TextInputLayout = 0x7f110123
co.auter.hcorp:layout/m3_auto_complete_simple_item = 0x7f0b0037
co.auter.hcorp:attr/listPreferredItemHeightSmall = 0x7f0302b3
co.auter.hcorp:attr/actionBarStyle = 0x7f030005
co.auter.hcorp:animator/m3_extended_fab_change_size_collapse_motion_spec = 0x7f020010
co.auter.hcorp:string/toolbar_description = 0x7f1000fe
co.auter.hcorp:attr/chipSurfaceColor = 0x7f0300cb
co.auter.hcorp:color/material_personalized_color_on_background = 0x7f050293
co.auter.hcorp:attr/actionModeCloseContentDescription = 0x7f030012
co.auter.hcorp:id/rn_redbox_dismiss_button = 0x7f080176
co.auter.hcorp:attr/drawerLayoutStyle = 0x7f030187
co.auter.hcorp:interpolator/fast_out_slow_in = 0x7f0a0006
co.auter.hcorp:attr/onNegativeCross = 0x7f03033d
co.auter.hcorp:attr/flow_verticalStyle = 0x7f0301f2
co.auter.hcorp:attr/attributeName = 0x7f03003b
co.auter.hcorp:drawable/material_ic_keyboard_arrow_left_black_24dp = 0x7f0700c6
co.auter.hcorp:dimen/m3_comp_sheet_side_docked_container_width = 0x7f060181
co.auter.hcorp:attr/constraintSetStart = 0x7f030129
co.auter.hcorp:macro/m3_comp_outlined_button_focus_outline_color = 0x7f0c00a4
co.auter.hcorp:attr/badgeStyle = 0x7f03005c
co.auter.hcorp:id/slide = 0x7f08019d
co.auter.hcorp:attr/chipSpacingHorizontal = 0x7f0300c4
co.auter.hcorp:attr/chipCornerRadius = 0x7f0300b9
co.auter.hcorp:styleable/LoadingImageView = 0x7f12004d
co.auter.hcorp:drawable/common_google_signin_btn_text_light = 0x7f070091
co.auter.hcorp:color/m3_ref_palette_neutral0 = 0x7f050115
co.auter.hcorp:attr/actionModeCloseDrawable = 0x7f030013
co.auter.hcorp:attr/collapsedTitleGravity = 0x7f0300df
co.auter.hcorp:attr/motionInterpolator = 0x7f030325
co.auter.hcorp:attr/behavior_significantVelocityThreshold = 0x7f030076
co.auter.hcorp:color/abc_background_cache_hint_selector_material_dark = 0x7f050000
co.auter.hcorp:attr/tabPadding = 0x7f03040b
co.auter.hcorp:dimen/m3_comp_fab_primary_container_elevation = 0x7f060118
co.auter.hcorp:drawable/ic_clear_black_24 = 0x7f0700a5
co.auter.hcorp:attr/boxCornerRadiusTopStart = 0x7f030087
co.auter.hcorp:drawable/$m3_avd_hide_password__1 = 0x7f070007
co.auter.hcorp:dimen/m3_snackbar_margin = 0x7f0601f2
co.auter.hcorp:attr/checkedIconMargin = 0x7f0300b2
co.auter.hcorp:styleable/AppBarLayoutStates = 0x7f12000b
co.auter.hcorp:color/material_personalized_color_primary_text = 0x7f0502a4
co.auter.hcorp:macro/m3_comp_secondary_navigation_tab_container_color = 0x7f0c00fc
co.auter.hcorp:attr/backgroundInsetStart = 0x7f030050
co.auter.hcorp:drawable/abc_cab_background_top_mtrl_alpha = 0x7f070039
co.auter.hcorp:attr/checkedChip = 0x7f0300ae
co.auter.hcorp:color/m3_sys_color_dark_on_background = 0x7f050173
co.auter.hcorp:attr/clockHandColor = 0x7f0300d1
co.auter.hcorp:style/TextAppearance.AppCompat.Large.Inverse = 0x7f1101b0
co.auter.hcorp:dimen/m3_comp_time_picker_time_selector_pressed_state_layer_opacity = 0x7f0601aa
co.auter.hcorp:dimen/m3_comp_input_chip_container_elevation = 0x7f060133
co.auter.hcorp:color/m3_chip_background_color = 0x7f050084
co.auter.hcorp:style/Widget.Material3.MaterialDivider = 0x7f1103ec
co.auter.hcorp:attr/addElevationShadow = 0x7f030029
co.auter.hcorp:dimen/m3_comp_primary_navigation_tab_with_icon_icon_size = 0x7f060164
co.auter.hcorp:layout/mtrl_calendar_horizontal = 0x7f0b0051
co.auter.hcorp:attr/navigationContentDescription = 0x7f03032f
co.auter.hcorp:attr/blurOverlayColor = 0x7f030078
co.auter.hcorp:dimen/subtitle_outline_width = 0x7f060326
co.auter.hcorp:style/Widget.Material3.MaterialButtonToggleGroup = 0x7f1103d4
co.auter.hcorp:attr/layout = 0x7f030260
co.auter.hcorp:color/ripple_material_light = 0x7f05030e
co.auter.hcorp:dimen/material_cursor_inset = 0x7f060233
co.auter.hcorp:style/ShapeAppearance.M3.Comp.Badge.Shape = 0x7f110165
co.auter.hcorp:attr/buttonTintMode = 0x7f03009d
co.auter.hcorp:attr/transitionFlags = 0x7f030498
co.auter.hcorp:style/Base.Theme.MaterialComponents.Light.Dialog.Bridge = 0x7f110077
co.auter.hcorp:style/Base.TextAppearance.MaterialComponents.Subtitle2 = 0x7f11004a
co.auter.hcorp:string/material_timepicker_text_input_mode_description = 0x7f1000a0
co.auter.hcorp:attr/expandedTitleTextAppearance = 0x7f0301b6
co.auter.hcorp:anim/mtrl_bottom_sheet_slide_in = 0x7f01002f
co.auter.hcorp:style/Base.Widget.AppCompat.SeekBar.Discrete = 0x7f1100fb
co.auter.hcorp:attr/indicatorTrackGapSize = 0x7f030230
co.auter.hcorp:style/Widget.Material3.AutoCompleteTextView.OutlinedBox = 0x7f11037e
co.auter.hcorp:attr/actionBarItemBackground = 0x7f030001
co.auter.hcorp:attr/deltaPolarRadius = 0x7f03016a
co.auter.hcorp:attr/state_with_icon = 0x7f0303e0
co.auter.hcorp:style/Widget.Material3.TextInputEditText.OutlinedBox = 0x7f110416
co.auter.hcorp:attr/colorOnPrimaryContainer = 0x7f0300f7
co.auter.hcorp:color/switch_thumb_material_light = 0x7f050317
co.auter.hcorp:anim/catalyst_fade_out = 0x7f010019
co.auter.hcorp:attr/icon = 0x7f03021d
co.auter.hcorp:attr/materialCalendarYearNavigationButton = 0x7f0302d7
co.auter.hcorp:attr/dayInvalidStyle = 0x7f030160
co.auter.hcorp:attr/colorControlActivated = 0x7f0300ec
co.auter.hcorp:macro/m3_comp_switch_selected_pressed_track_color = 0x7f0c012d
co.auter.hcorp:color/m3_sys_color_dark_tertiary = 0x7f05018d
co.auter.hcorp:attr/indicatorInset = 0x7f03022e
co.auter.hcorp:style/Widget.MaterialComponents.MaterialCalendar.HeaderLayout = 0x7f110465
co.auter.hcorp:attr/backHandlingEnabled = 0x7f03004a
co.auter.hcorp:color/dim_foreground_disabled_material_dark = 0x7f050068
co.auter.hcorp:animator/mtrl_fab_transformation_sheet_expand_spec = 0x7f020021
co.auter.hcorp:dimen/m3_btn_disabled_translation_z = 0x7f0600d1
co.auter.hcorp:attr/colorTertiary = 0x7f030121
co.auter.hcorp:dimen/mtrl_btn_focused_z = 0x7f060261
co.auter.hcorp:animator/m3_btn_elevated_btn_state_list_anim = 0x7f02000a
co.auter.hcorp:attr/buttonPanelSideLayout = 0x7f030098
co.auter.hcorp:dimen/m3_comp_outlined_text_field_disabled_label_text_opacity = 0x7f060159
co.auter.hcorp:style/Widget.AppCompat.Light.ListPopupWindow = 0x7f110347
co.auter.hcorp:id/mtrl_picker_text_input_range_start = 0x7f080134
co.auter.hcorp:anim/btn_checkbox_to_checked_box_inner_merged_animation = 0x7f01000c
co.auter.hcorp:style/Widget.MaterialComponents.Button.UnelevatedButton.Icon = 0x7f110444
co.auter.hcorp:color/mtrl_switch_track_tint = 0x7f0502f5
co.auter.hcorp:dimen/m3_badge_horizontal_offset = 0x7f0600b6
co.auter.hcorp:attr/tabIndicator = 0x7f030400
co.auter.hcorp:style/Widget.AppCompat.Button.Borderless = 0x7f110329
co.auter.hcorp:attr/buttonIconDimen = 0x7f030095
co.auter.hcorp:attr/colorOnSurface = 0x7f0300ff
co.auter.hcorp:string/catalyst_dismiss_button = 0x7f100036
co.auter.hcorp:attr/colorPrimary = 0x7f030108
co.auter.hcorp:color/material_dynamic_primary70 = 0x7f050257
co.auter.hcorp:string/common_google_play_services_notification_channel_name = 0x7f100055
co.auter.hcorp:id/textSpacerNoButtons = 0x7f0801ca
co.auter.hcorp:attr/buttonBarButtonStyle = 0x7f03008d
co.auter.hcorp:attr/layoutDuringTransition = 0x7f030262
co.auter.hcorp:drawable/abc_spinner_mtrl_am_alpha = 0x7f070065
co.auter.hcorp:string/expo_notifications_fallback_channel_name = 0x7f100064
co.auter.hcorp:animator/fragment_open_enter = 0x7f020007
co.auter.hcorp:style/ShapeAppearance.M3.Comp.NavigationDrawer.ActiveIndicator.Shape = 0x7f11016b
co.auter.hcorp:style/Base.Widget.AppCompat.Light.ActionBar.TabBar = 0x7f1100e5
co.auter.hcorp:anim/btn_checkbox_to_unchecked_icon_null_animation = 0x7f010011
co.auter.hcorp:color/m3_sys_color_dark_on_tertiary = 0x7f05017c
co.auter.hcorp:attr/chipMinTouchTargetSize = 0x7f0300c2
co.auter.hcorp:attr/listPreferredItemPaddingStart = 0x7f0302b7
co.auter.hcorp:dimen/mtrl_snackbar_margin = 0x7f0602f6
co.auter.hcorp:animator/mtrl_fab_hide_motion_spec = 0x7f02001e
co.auter.hcorp:id/transform = 0x7f0801df
co.auter.hcorp:dimen/material_clock_display_height = 0x7f060225
co.auter.hcorp:attr/motionEasingStandard = 0x7f030321
co.auter.hcorp:anim/btn_checkbox_to_checked_box_outer_merged_animation = 0x7f01000d
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral99 = 0x7f0500c8
co.auter.hcorp:anim/rns_no_animation_250 = 0x7f010043
co.auter.hcorp:id/accessibility_collection_item = 0x7f080012
co.auter.hcorp:attr/floatingActionButtonSmallStyle = 0x7f0301db
co.auter.hcorp:color/m3_sys_color_dynamic_dark_surface_dim = 0x7f0501ad
co.auter.hcorp:color/m3_ref_palette_white = 0x7f050161
co.auter.hcorp:color/common_google_signin_btn_text_dark_disabled = 0x7f050039
co.auter.hcorp:color/material_personalized_color_on_surface = 0x7f05029a
co.auter.hcorp:attr/boxCollapsedPaddingTop = 0x7f030083
co.auter.hcorp:attr/colorOnPrimarySurface = 0x7f0300fa
co.auter.hcorp:attr/badgeWithTextHeight = 0x7f030063
co.auter.hcorp:animator/design_appbar_state_list_animator = 0x7f020000
co.auter.hcorp:attr/percentHeight = 0x7f030357
co.auter.hcorp:dimen/m3_comp_input_chip_container_height = 0x7f060134
co.auter.hcorp:dimen/m3_alert_dialog_action_top_padding = 0x7f0600a1
co.auter.hcorp:id/mtrl_picker_text_input_range_end = 0x7f080133
co.auter.hcorp:attr/bottomSheetDragHandleStyle = 0x7f03007f
co.auter.hcorp:anim/rns_slide_in_from_left = 0x7f010047
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral50 = 0x7f0500bc
co.auter.hcorp:anim/rns_default_enter_in = 0x7f010032
co.auter.hcorp:dimen/abc_dialog_fixed_width_major = 0x7f06001e
co.auter.hcorp:color/m3_tabs_text_color = 0x7f050211
co.auter.hcorp:attr/bottomNavigationStyle = 0x7f03007d
co.auter.hcorp:style/Base.Theme.MaterialComponents.Light.DarkActionBar = 0x7f110073
co.auter.hcorp:attr/foregroundInsidePadding = 0x7f030202
co.auter.hcorp:dimen/mtrl_btn_padding_left = 0x7f060269
co.auter.hcorp:attr/isMaterial3DynamicColorApplied = 0x7f030235
co.auter.hcorp:integer/m3_sys_motion_duration_long3 = 0x7f090017
co.auter.hcorp:drawable/mtrl_switch_thumb_pressed_unchecked = 0x7f0700ea
co.auter.hcorp:style/Base.V22.Theme.AppCompat = 0x7f1100b3
co.auter.hcorp:attr/behavior_saveFlags = 0x7f030075
co.auter.hcorp:id/invisible = 0x7f0800e9
co.auter.hcorp:dimen/m3_comp_radio_button_disabled_selected_icon_opacity = 0x7f060168
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral_variant0 = 0x7f0500c9
co.auter.hcorp:integer/status_bar_notification_info_maxnum = 0x7f090046
co.auter.hcorp:attr/snackbarButtonStyle = 0x7f0303c5
co.auter.hcorp:style/Animation.AppCompat.Dialog = 0x7f110002
co.auter.hcorp:attr/progressBarImageScaleType = 0x7f030370
co.auter.hcorp:animator/m3_card_state_list_anim = 0x7f02000d
co.auter.hcorp:style/TextAppearance.M3.Sys.Typescale.BodyLarge = 0x7f1101e9
co.auter.hcorp:attr/behavior_expandedOffset = 0x7f03006f
co.auter.hcorp:style/Base.Theme.MaterialComponents.Dialog.FixedSize = 0x7f11006e
co.auter.hcorp:attr/autoCompleteTextViewStyle = 0x7f03003d
co.auter.hcorp:attr/boxCornerRadiusBottomStart = 0x7f030085
co.auter.hcorp:attr/textInputFilledExposedDropdownMenuStyle = 0x7f030446
co.auter.hcorp:attr/isMaterial3Theme = 0x7f030236
co.auter.hcorp:attr/collapseIcon = 0x7f0300dd
co.auter.hcorp:id/design_menu_item_action_area_stub = 0x7f08009b
co.auter.hcorp:attr/fabAlignmentModeEndMargin = 0x7f0301c1
co.auter.hcorp:attr/titleMarginEnd = 0x7f030472
co.auter.hcorp:dimen/m3_comp_fab_primary_pressed_state_layer_opacity = 0x7f060121
co.auter.hcorp:attr/materialTimePickerTheme = 0x7f0302ed
co.auter.hcorp:style/Widget.Material3.SearchBar = 0x7f110401
co.auter.hcorp:attr/collapsingToolbarLayoutMediumStyle = 0x7f0300e5
co.auter.hcorp:id/tag_screen_reader_focusable = 0x7f0801c1
co.auter.hcorp:attr/barrierAllowsGoneWidgets = 0x7f030069
co.auter.hcorp:attr/searchPrefixText = 0x7f03039c
co.auter.hcorp:macro/m3_comp_badge_large_label_text_type = 0x7f0c0004
co.auter.hcorp:id/inward = 0x7f0800ea
co.auter.hcorp:attr/layoutDescription = 0x7f030261
co.auter.hcorp:id/group_divider = 0x7f0800d7
co.auter.hcorp:attr/bottomInsetScrimEnabled = 0x7f03007c
co.auter.hcorp:attr/backgroundSplit = 0x7f030053
co.auter.hcorp:attr/badgeWidth = 0x7f030062
co.auter.hcorp:style/ThemeOverlay.Material3.Chip.Assist = 0x7f1102c9
co.auter.hcorp:attr/hideAnimationBehavior = 0x7f03020f
co.auter.hcorp:anim/abc_tooltip_enter = 0x7f01000a
co.auter.hcorp:styleable/RecycleListView = 0x7f120074
co.auter.hcorp:style/Base.V21.Theme.AppCompat.Dialog = 0x7f1100a8
co.auter.hcorp:color/material_personalized_color_background = 0x7f05028d
co.auter.hcorp:color/design_default_color_surface = 0x7f05005d
co.auter.hcorp:style/Base.ThemeOverlay.AppCompat.Dialog.Alert = 0x7f110083
co.auter.hcorp:color/material_personalized_color_surface = 0x7f0502aa
co.auter.hcorp:string/mtrl_picker_invalid_format = 0x7f1000bf
co.auter.hcorp:color/m3_ref_palette_error100 = 0x7f05010a
co.auter.hcorp:attr/touchAnchorSide = 0x7f030486
co.auter.hcorp:style/Base.Widget.AppCompat.ListView.Menu = 0x7f1100ef
co.auter.hcorp:attr/commitIcon = 0x7f030125
co.auter.hcorp:style/Widget.MaterialComponents.MaterialCalendar.MonthTextView = 0x7f11046d
co.auter.hcorp:style/Widget.Material3.Chip.Filter = 0x7f1103a4
co.auter.hcorp:attr/materialCalendarMonthNavigationButton = 0x7f0302d4
co.auter.hcorp:attr/alertDialogCenterButtons = 0x7f03002b
co.auter.hcorp:drawable/material_ic_clear_black_24dp = 0x7f0700c4
co.auter.hcorp:attr/behavior_fitToContents = 0x7f030070
co.auter.hcorp:dimen/mtrl_transition_shared_axis_slide_distance = 0x7f06030e
co.auter.hcorp:color/material_personalized_color_primary = 0x7f0502a1
co.auter.hcorp:attr/badgeTextAppearance = 0x7f03005e
co.auter.hcorp:attr/chipSpacing = 0x7f0300c3
co.auter.hcorp:dimen/m3_comp_secondary_navigation_tab_focus_state_layer_opacity = 0x7f06017a
co.auter.hcorp:style/ThemeOverlay.MaterialComponents.MaterialAlertDialog.Picker.Date = 0x7f11030b
co.auter.hcorp:style/ThemeOverlay.MaterialComponents.Light.Dialog.Alert.Framework = 0x7f110308
co.auter.hcorp:anim/rns_fade_to_bottom = 0x7f010039
co.auter.hcorp:attr/actionBarTheme = 0x7f030009
co.auter.hcorp:color/design_fab_stroke_top_inner_color = 0x7f050064
co.auter.hcorp:attr/cardForegroundColor = 0x7f0300a1
co.auter.hcorp:dimen/m3_navigation_rail_item_padding_top = 0x7f0601d5
co.auter.hcorp:attr/behavior_overlapTop = 0x7f030073
co.auter.hcorp:attr/actionModeTheme = 0x7f03001d
co.auter.hcorp:dimen/m3_comp_divider_thickness = 0x7f06010a
co.auter.hcorp:attr/badgeGravity = 0x7f030057
co.auter.hcorp:style/TextAppearance.AppCompat.Widget.ActionBar.Subtitle = 0x7f1101c2
co.auter.hcorp:attr/floatingActionButtonSmallSecondaryStyle = 0x7f0301da
co.auter.hcorp:string/state_busy_description = 0x7f1000f3
co.auter.hcorp:dimen/abc_button_inset_vertical_material = 0x7f060013
co.auter.hcorp:color/mtrl_chip_surface_color = 0x7f0502d8
co.auter.hcorp:attr/prefixTextAppearance = 0x7f030369
co.auter.hcorp:dimen/abc_action_bar_subtitle_top_margin_material = 0x7f06000c
co.auter.hcorp:layout/abc_search_dropdown_item_icons_2line = 0x7f0b0018
co.auter.hcorp:anim/rns_slide_in_from_bottom = 0x7f010046
co.auter.hcorp:style/MaterialAlertDialog.Material3.Title.Icon.CenterStacked = 0x7f110135
co.auter.hcorp:attr/actionOverflowButtonStyle = 0x7f03001f
co.auter.hcorp:id/on = 0x7f080147
co.auter.hcorp:dimen/design_navigation_item_vertical_padding = 0x7f06007d
co.auter.hcorp:color/mtrl_btn_transparent_bg_color = 0x7f0502d1
co.auter.hcorp:color/primary_text_default_material_dark = 0x7f050309
co.auter.hcorp:anim/design_snackbar_out = 0x7f010021
co.auter.hcorp:attr/counterEnabled = 0x7f03014b
co.auter.hcorp:dimen/m3_btn_icon_only_default_padding = 0x7f0600d6
co.auter.hcorp:dimen/m3_comp_search_bar_container_elevation = 0x7f060172
co.auter.hcorp:attr/titleTextAppearance = 0x7f030477
co.auter.hcorp:style/Theme.EdgeToEdge.Material3.Dynamic.Light = 0x7f11024f
co.auter.hcorp:color/m3_sys_color_dynamic_dark_error_container = 0x7f050191
co.auter.hcorp:style/TextAppearance.M3.Sys.Typescale.HeadlineLarge = 0x7f1101ef
co.auter.hcorp:attr/cardElevation = 0x7f0300a0
co.auter.hcorp:macro/m3_comp_navigation_drawer_active_icon_color = 0x7f0c007e
co.auter.hcorp:color/design_bottom_navigation_shadow_color = 0x7f050042
co.auter.hcorp:id/clip_vertical = 0x7f080082
co.auter.hcorp:dimen/mtrl_navigation_item_shape_horizontal_margin = 0x7f0602cc
co.auter.hcorp:attr/drawableTopCompat = 0x7f030184
co.auter.hcorp:dimen/mtrl_calendar_content_padding = 0x7f060278
co.auter.hcorp:attr/cardUseCompatPadding = 0x7f0300a4
co.auter.hcorp:id/italic = 0x7f0800eb
co.auter.hcorp:attr/backgroundImage = 0x7f03004d
co.auter.hcorp:macro/m3_comp_radio_button_unselected_icon_color = 0x7f0c00e3
co.auter.hcorp:anim/btn_radio_to_off_mtrl_ring_outer_animation = 0x7f010013
co.auter.hcorp:attr/chipGroupStyle = 0x7f0300bb
co.auter.hcorp:styleable/AnimatedStateListDrawableCompat = 0x7f120007
co.auter.hcorp:attr/maxButtonHeight = 0x7f0302f1
co.auter.hcorp:macro/m3_comp_outlined_text_field_input_text_color = 0x7f0c00c0
co.auter.hcorp:attr/chipStrokeWidth = 0x7f0300c9
co.auter.hcorp:color/common_google_signin_btn_text_light_default = 0x7f05003d
co.auter.hcorp:attr/autoAdjustToWithinGrandparentBounds = 0x7f03003c
co.auter.hcorp:attr/backgroundInsetTop = 0x7f030051
co.auter.hcorp:style/ThemeOverlay.Material3.HarmonizedColors = 0x7f1102dc
co.auter.hcorp:attr/colorPrimaryFixed = 0x7f03010b
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral_variant87 = 0x7f0500d9
co.auter.hcorp:dimen/m3_back_progress_side_container_max_scale_y_distance = 0x7f0600b5
co.auter.hcorp:dimen/notification_main_column_padding_top = 0x7f060315
co.auter.hcorp:attr/alertDialogTheme = 0x7f03002d
co.auter.hcorp:attr/checkedButton = 0x7f0300ad
co.auter.hcorp:anim/catalyst_fade_in = 0x7f010018
co.auter.hcorp:dimen/m3_sys_state_focus_state_layer_opacity = 0x7f06021e
co.auter.hcorp:macro/m3_comp_radio_button_unselected_hover_state_layer_color = 0x7f0c00e2
co.auter.hcorp:color/m3_ref_palette_dynamic_secondary0 = 0x7f0500ee
co.auter.hcorp:animator/m3_extended_fab_hide_motion_spec = 0x7f020012
co.auter.hcorp:plurals/mtrl_badge_content_description = 0x7f0e0000
co.auter.hcorp:attr/fontProviderPackage = 0x7f0301fa
co.auter.hcorp:attr/contentPaddingLeft = 0x7f030137
co.auter.hcorp:macro/m3_comp_dialog_supporting_text_color = 0x7f0c0026
co.auter.hcorp:id/with_icon = 0x7f0801fe
co.auter.hcorp:color/material_personalized_color_surface_container_lowest = 0x7f0502b0
co.auter.hcorp:attr/alpha = 0x7f03002f
co.auter.hcorp:color/m3_sys_color_dynamic_light_outline_variant = 0x7f0501c3
co.auter.hcorp:macro/m3_comp_primary_navigation_tab_active_indicator_color = 0x7f0c00c9
co.auter.hcorp:anim/linear_indeterminate_line1_tail_interpolator = 0x7f010024
co.auter.hcorp:attr/startIconTintMode = 0x7f0303d7
co.auter.hcorp:style/Widget.AppCompat.ActionBar.TabView = 0x7f110321
co.auter.hcorp:attr/retryImageScaleType = 0x7f030382
co.auter.hcorp:drawable/mtrl_switch_thumb_unchecked_checked = 0x7f0700ec
co.auter.hcorp:drawable/ic_call_answer = 0x7f07009f
co.auter.hcorp:styleable/GradientColor = 0x7f12003c
co.auter.hcorp:color/m3_ref_palette_neutral100 = 0x7f050117
co.auter.hcorp:attr/materialCalendarMonth = 0x7f0302d3
co.auter.hcorp:dimen/m3_btn_icon_only_default_size = 0x7f0600d7
co.auter.hcorp:animator/mtrl_extended_fab_change_size_collapse_motion_spec = 0x7f020019
co.auter.hcorp:string/mtrl_timepicker_cancel = 0x7f1000e0
co.auter.hcorp:anim/btn_radio_to_on_mtrl_dot_group_animation = 0x7f010015
co.auter.hcorp:attr/autoSizeMinTextSize = 0x7f030040
co.auter.hcorp:attr/layoutManager = 0x7f030263
co.auter.hcorp:dimen/mtrl_calendar_navigation_top_padding = 0x7f060290
co.auter.hcorp:integer/m3_sys_shape_corner_extra_small_corner_family = 0x7f090023
co.auter.hcorp:attr/backgroundTintMode = 0x7f030056
co.auter.hcorp:color/m3_dynamic_default_color_primary_text = 0x7f050094
co.auter.hcorp:drawable/abc_ratingbar_indicator_material = 0x7f07005a
co.auter.hcorp:string/state_collapsed_description = 0x7f1000f4
co.auter.hcorp:dimen/material_clock_size = 0x7f060232
co.auter.hcorp:attr/thumbTextPadding = 0x7f03045b
co.auter.hcorp:attr/actionModeShareDrawable = 0x7f03001a
co.auter.hcorp:attr/contentInsetRight = 0x7f030131
co.auter.hcorp:drawable/common_google_signin_btn_text_dark_focused = 0x7f07008d
co.auter.hcorp:style/Widget.Material3.Chip.Suggestion = 0x7f1103aa
co.auter.hcorp:macro/m3_comp_elevated_card_container_color = 0x7f0c002a
co.auter.hcorp:attr/flow_padding = 0x7f0301ee
co.auter.hcorp:attr/colorOnSecondary = 0x7f0300fb
co.auter.hcorp:style/Base.Widget.AppCompat.ListMenuView = 0x7f1100eb
co.auter.hcorp:drawable/abc_textfield_activated_mtrl_alpha = 0x7f070071
co.auter.hcorp:attr/actionModeFindDrawable = 0x7f030016
co.auter.hcorp:style/Widget.Material3.CardView.Elevated = 0x7f11039e
co.auter.hcorp:dimen/m3_badge_size = 0x7f0600b8
co.auter.hcorp:dimen/abc_list_item_padding_horizontal_material = 0x7f060033
co.auter.hcorp:attr/roundingBorderPadding = 0x7f030393
co.auter.hcorp:id/default_activity_button = 0x7f080097
co.auter.hcorp:color/material_dynamic_tertiary70 = 0x7f050271
co.auter.hcorp:anim/rns_default_exit_out = 0x7f010035
co.auter.hcorp:attr/actionBarDivider = 0x7f030000
co.auter.hcorp:attr/chipEndPadding = 0x7f0300ba
co.auter.hcorp:dimen/abc_action_bar_subtitle_bottom_margin_material = 0x7f06000b
co.auter.hcorp:animator/mtrl_extended_fab_hide_motion_spec = 0x7f02001b
co.auter.hcorp:style/Widget.MaterialComponents.Toolbar = 0x7f11049d
co.auter.hcorp:attr/dropdownListPreferredItemHeight = 0x7f03018a
co.auter.hcorp:color/design_box_stroke_color = 0x7f050043
co.auter.hcorp:style/Widget.MaterialComponents.MaterialCalendar.HeaderTitle = 0x7f110469
co.auter.hcorp:id/expanded_menu = 0x7f0800bc
co.auter.hcorp:id/META = 0x7f080005
co.auter.hcorp:attr/actionModeCloseButtonStyle = 0x7f030011
co.auter.hcorp:attr/tabMinWidth = 0x7f030409
co.auter.hcorp:attr/track = 0x7f030488
co.auter.hcorp:attr/floatingActionButtonLargeSecondaryStyle = 0x7f0301d3
co.auter.hcorp:dimen/m3_sys_motion_easing_emphasized_accelerate_control_y2 = 0x7f0601fc
co.auter.hcorp:dimen/m3_navigation_item_horizontal_padding = 0x7f0601c3
co.auter.hcorp:dimen/mtrl_card_corner_radius = 0x7f0602a0
co.auter.hcorp:color/mtrl_chip_background_color = 0x7f0502d6
co.auter.hcorp:styleable/KeyCycle = 0x7f120041
co.auter.hcorp:attr/layout_constrainedHeight = 0x7f030269
co.auter.hcorp:style/TextAppearance.Compat.Notification.Media = 0x7f1101d9
co.auter.hcorp:style/Animation.MaterialComponents.BottomSheetDialog = 0x7f11000c
co.auter.hcorp:macro/m3_comp_search_bar_trailing_icon_color = 0x7f0c00f0
co.auter.hcorp:attr/bottomAppBarStyle = 0x7f03007b
co.auter.hcorp:attr/paddingStartSystemWindowInsets = 0x7f03034a
co.auter.hcorp:attr/motionEasingDecelerated = 0x7f03031a
co.auter.hcorp:color/material_dynamic_primary100 = 0x7f050251
co.auter.hcorp:macro/m3_comp_radio_button_selected_focus_icon_color = 0x7f0c00d8
co.auter.hcorp:id/forever = 0x7f0800cd
co.auter.hcorp:anim/m3_bottom_sheet_slide_out = 0x7f010028
co.auter.hcorp:attr/materialButtonOutlinedStyle = 0x7f0302c6
co.auter.hcorp:color/m3_sys_color_dynamic_dark_on_primary = 0x7f050198
co.auter.hcorp:attr/menuAlignmentMode = 0x7f0302fb
co.auter.hcorp:dimen/abc_star_small = 0x7f06003d
co.auter.hcorp:style/Widget.Material3.SideSheet.Modal.Detached = 0x7f110409
co.auter.hcorp:style/TextAppearance.Material3.BodyMedium = 0x7f1101fb
co.auter.hcorp:attr/background = 0x7f03004b
co.auter.hcorp:attr/centerIfNoTextEnabled = 0x7f0300a7
co.auter.hcorp:anim/rns_ios_from_left_background_open = 0x7f01003b
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral_variant10 = 0x7f0500ca
co.auter.hcorp:id/CTRL = 0x7f080003
co.auter.hcorp:anim/rns_ios_from_right_foreground_close = 0x7f010040
co.auter.hcorp:attr/arrowShaftLength = 0x7f03003a
co.auter.hcorp:layout/mtrl_calendar_day = 0x7f0b004e
co.auter.hcorp:attr/layout_keyline = 0x7f03029d
co.auter.hcorp:style/Theme.Material3.DayNight.Dialog.MinWidth = 0x7f110263
co.auter.hcorp:integer/m3_chip_anim_duration = 0x7f090010
co.auter.hcorp:dimen/abc_panel_menu_list_width = 0x7f060034
co.auter.hcorp:style/Base.Widget.AppCompat.Light.ActionBar.TabView = 0x7f1100e8
co.auter.hcorp:attr/hintEnabled = 0x7f030215
co.auter.hcorp:id/parent = 0x7f08015a
co.auter.hcorp:attr/state_collapsible = 0x7f0303da
co.auter.hcorp:color/material_dynamic_neutral100 = 0x7f050237
co.auter.hcorp:attr/ttcIndex = 0x7f03049e
co.auter.hcorp:attr/autoShowKeyboard = 0x7f03003e
co.auter.hcorp:anim/rns_slide_out_to_right = 0x7f01004b
co.auter.hcorp:color/m3_sys_color_dynamic_dark_secondary_container = 0x7f0501a5
co.auter.hcorp:attr/actionDropDownStyle = 0x7f03000c
co.auter.hcorp:style/Base.Widget.AppCompat.PopupMenu.Overflow = 0x7f1100f1
co.auter.hcorp:attr/materialCalendarFullscreenTheme = 0x7f0302cb
co.auter.hcorp:style/Theme.MaterialComponents.DayNight.DarkActionBar = 0x7f11027c
co.auter.hcorp:attr/actionButtonStyle = 0x7f03000b
co.auter.hcorp:style/Base.ThemeOverlay.AppCompat.Light = 0x7f110084
co.auter.hcorp:dimen/m3_comp_outlined_card_container_elevation = 0x7f060153
co.auter.hcorp:style/Widget.Material3.BottomNavigation.Badge = 0x7f110385
co.auter.hcorp:attr/materialSearchBarStyle = 0x7f0302e5
co.auter.hcorp:dimen/m3_ripple_hovered_alpha = 0x7f0601da
co.auter.hcorp:attr/textAppearanceListItem = 0x7f030435
co.auter.hcorp:color/design_default_color_secondary = 0x7f05005b
co.auter.hcorp:id/mtrl_view_tag_bottom_padding = 0x7f080136
co.auter.hcorp:attr/windowSplashScreenAnimatedIcon = 0x7f0304b9
co.auter.hcorp:attr/titleTextStyle = 0x7f03047a
co.auter.hcorp:attr/behavior_peekHeight = 0x7f030074
co.auter.hcorp:attr/motionDurationMedium4 = 0x7f030314
co.auter.hcorp:attr/collapseContentDescription = 0x7f0300dc
co.auter.hcorp:color/m3_sys_color_dynamic_on_secondary_fixed_variant = 0x7f0501d6
co.auter.hcorp:attr/dialogCornerRadius = 0x7f03016c
co.auter.hcorp:attr/colorPrimarySurface = 0x7f03010e
co.auter.hcorp:interpolator/btn_checkbox_checked_mtrl_animation_interpolator_0 = 0x7f0a0000
co.auter.hcorp:drawable/abc_scrubber_track_mtrl_alpha = 0x7f070061
co.auter.hcorp:animator/mtrl_extended_fab_show_motion_spec = 0x7f02001c
co.auter.hcorp:attr/keyboardIcon = 0x7f030257
co.auter.hcorp:attr/horizontalOffsetWithText = 0x7f03021b
co.auter.hcorp:string/fallback_menu_item_copy_link = 0x7f10006b
co.auter.hcorp:attr/activityChooserViewStyle = 0x7f030025
co.auter.hcorp:attr/itemTextAppearanceActiveBoldEnabled = 0x7f030252
co.auter.hcorp:attr/startIconContentDescription = 0x7f0303d2
co.auter.hcorp:style/Widget.MaterialComponents.AppBarLayout.Primary = 0x7f110429
co.auter.hcorp:attr/fabCradleRoundedCornerRadius = 0x7f0301c5
co.auter.hcorp:attr/brightness = 0x7f03008c
co.auter.hcorp:color/design_default_color_background = 0x7f050051
co.auter.hcorp:string/common_google_play_services_notification_ticker = 0x7f100056
co.auter.hcorp:color/m3_sys_color_dynamic_dark_tertiary = 0x7f0501af
co.auter.hcorp:style/Widget.MaterialComponents.NavigationRailView.PrimarySurface = 0x7f110477
co.auter.hcorp:attr/tint = 0x7f030469
co.auter.hcorp:attr/alertDialogStyle = 0x7f03002c
co.auter.hcorp:style/Widget.MaterialComponents.CircularProgressIndicator = 0x7f11044c
co.auter.hcorp:style/Theme.Material3.DayNight.SideSheetDialog = 0x7f110266
co.auter.hcorp:attr/haloColor = 0x7f030207
co.auter.hcorp:dimen/m3_sys_motion_easing_emphasized_decelerate_control_y2 = 0x7f060200
co.auter.hcorp:anim/rns_default_enter_out = 0x7f010033
co.auter.hcorp:style/TextAppearance.AppCompat.Widget.ActionMode.Subtitle = 0x7f1101c6
co.auter.hcorp:layout/abc_action_bar_up_container = 0x7f0b0001
co.auter.hcorp:dimen/abc_dropdownitem_text_padding_left = 0x7f06002a
co.auter.hcorp:drawable/ripple_effect = 0x7f070102
co.auter.hcorp:id/center_horizontal = 0x7f080078
co.auter.hcorp:anim/btn_radio_to_off_mtrl_ring_outer_path_animation = 0x7f010014
co.auter.hcorp:drawable/$mtrl_checkbox_button_icon_unchecked_indeterminate__0 = 0x7f07001a
co.auter.hcorp:style/Base.V7.Theme.AppCompat = 0x7f1100c0
co.auter.hcorp:color/abc_primary_text_material_dark = 0x7f05000b
co.auter.hcorp:attr/colorSurfaceContainerLowest = 0x7f03011c
co.auter.hcorp:attr/indicatorColor = 0x7f03022b
co.auter.hcorp:attr/drawPath = 0x7f03017b
co.auter.hcorp:attr/trackInsideCornerSize = 0x7f030491
co.auter.hcorp:color/m3_sys_color_dark_on_tertiary_container = 0x7f05017d
co.auter.hcorp:anim/rns_slide_out_to_left = 0x7f01004a
co.auter.hcorp:anim/catalyst_push_up_out = 0x7f01001b
co.auter.hcorp:style/TextAppearance.MaterialComponents.Button = 0x7f110210
co.auter.hcorp:attr/textAppearancePopupMenuHeader = 0x7f030439
co.auter.hcorp:drawable/$m3_avd_hide_password__2 = 0x7f070008
co.auter.hcorp:dimen/mtrl_calendar_maximum_default_fullscreen_minor_axis = 0x7f06028b
co.auter.hcorp:attr/colorPrimaryInverse = 0x7f03010d
co.auter.hcorp:anim/rns_ios_from_left_foreground_open = 0x7f01003d
co.auter.hcorp:anim/rns_default_exit_in = 0x7f010034
co.auter.hcorp:style/Widget.MaterialComponents.MaterialCalendar.YearNavigationButton = 0x7f110471
co.auter.hcorp:color/m3_ref_palette_primary10 = 0x7f05013b
co.auter.hcorp:attr/materialSearchViewPrefixStyle = 0x7f0302e6
co.auter.hcorp:color/m3_sys_color_light_surface_bright = 0x7f0501f7
co.auter.hcorp:attr/showTitle = 0x7f0303b8
co.auter.hcorp:drawable/paused_in_debugger_dialog_background = 0x7f070100
co.auter.hcorp:dimen/abc_list_item_height_large_material = 0x7f060030
co.auter.hcorp:anim/mtrl_bottom_sheet_slide_out = 0x7f010030
co.auter.hcorp:style/Widget.AppCompat.ActionBar.Solid = 0x7f11031e
co.auter.hcorp:string/material_motion_easing_accelerated = 0x7f100092
co.auter.hcorp:color/material_personalized_color_primary_container = 0x7f0502a2
co.auter.hcorp:dimen/design_bottom_navigation_height = 0x7f060066
co.auter.hcorp:id/browser_actions_menu_items = 0x7f08006d
co.auter.hcorp:color/m3_ref_palette_tertiary0 = 0x7f050154
co.auter.hcorp:attr/layout_scrollInterpolator = 0x7f0302a1
co.auter.hcorp:style/ThemeOverlay.AppCompat.Dialog.Alert = 0x7f1102b2
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral_variant100 = 0x7f0500cb
co.auter.hcorp:dimen/m3_comp_navigation_drawer_container_width = 0x7f060141
co.auter.hcorp:style/Widget.Material3.CircularProgressIndicator.Small = 0x7f1103b4
co.auter.hcorp:color/design_fab_shadow_mid_color = 0x7f050060
co.auter.hcorp:style/ThemeOverlay.Material3.Snackbar = 0x7f1102ec
co.auter.hcorp:attr/extendedFloatingActionButtonPrimaryStyle = 0x7f0301ba
co.auter.hcorp:color/design_dark_default_color_on_error = 0x7f050047
co.auter.hcorp:drawable/abc_list_pressed_holo_light = 0x7f070051
co.auter.hcorp:attr/actionMenuTextAppearance = 0x7f03000e
co.auter.hcorp:attr/minHideDelay = 0x7f0302fe
co.auter.hcorp:drawable/common_google_signin_btn_icon_light_focused = 0x7f070089
co.auter.hcorp:anim/btn_checkbox_to_unchecked_check_path_merged_animation = 0x7f010010
co.auter.hcorp:attr/dayStyle = 0x7f030162
co.auter.hcorp:attr/arcMode = 0x7f030038
co.auter.hcorp:dimen/m3_sys_motion_easing_standard_control_x1 = 0x7f060215
co.auter.hcorp:anim/design_bottom_sheet_slide_in = 0x7f01001e
co.auter.hcorp:attr/layout_constraintDimensionRatio = 0x7f030273
co.auter.hcorp:style/TextAppearance.AppCompat.Inverse = 0x7f1101ae
co.auter.hcorp:color/mtrl_choice_chip_background_color = 0x7f0502da
co.auter.hcorp:attr/materialCardViewStyle = 0x7f0302db
co.auter.hcorp:style/Widget.MaterialComponents.BottomAppBar.PrimarySurface = 0x7f110433
co.auter.hcorp:macro/m3_comp_navigation_drawer_active_focus_icon_color = 0x7f0c0078
co.auter.hcorp:id/pathRelative = 0x7f080160
co.auter.hcorp:attr/motionDurationMedium3 = 0x7f030313
co.auter.hcorp:attr/expandedTitleMarginTop = 0x7f0301b5
co.auter.hcorp:attr/alphabeticModifiers = 0x7f030030
co.auter.hcorp:macro/m3_comp_time_picker_time_selector_unselected_hover_state_layer_color = 0x7f0c0169
co.auter.hcorp:layout/mtrl_picker_header_selection_text = 0x7f0b0060
co.auter.hcorp:dimen/m3_searchview_divider_size = 0x7f0601e5
co.auter.hcorp:attr/textAppearanceHeadlineLarge = 0x7f03042d
co.auter.hcorp:style/Widget.MaterialComponents.MaterialCalendar.Day.Selected = 0x7f11045d
co.auter.hcorp:color/material_on_surface_emphasis_medium = 0x7f050289
co.auter.hcorp:attr/layout_constraintWidth_max = 0x7f030290
co.auter.hcorp:anim/abc_fade_out = 0x7f010001
co.auter.hcorp:id/spacer = 0x7f0801a2
co.auter.hcorp:attr/imageButtonStyle = 0x7f030228
co.auter.hcorp:macro/m3_comp_date_picker_modal_header_headline_type = 0x7f0c0016
co.auter.hcorp:dimen/material_cursor_width = 0x7f060234
co.auter.hcorp:style/Base.TextAppearance.AppCompat.Widget.PopupMenu.Header = 0x7f110041
co.auter.hcorp:drawable/abc_tab_indicator_material = 0x7f07006b
co.auter.hcorp:color/material_dynamic_neutral90 = 0x7f05023f
co.auter.hcorp:attr/roundWithOverlayColor = 0x7f030390
co.auter.hcorp:style/Widget.Material3.MaterialCalendar.Day.Selected = 0x7f1103d8
co.auter.hcorp:attr/topInsetScrimEnabled = 0x7f030484
co.auter.hcorp:macro/m3_comp_radio_button_selected_pressed_icon_color = 0x7f0c00dd
co.auter.hcorp:attr/colorOnSecondaryFixedVariant = 0x7f0300fe
co.auter.hcorp:color/m3_ref_palette_dynamic_neutral_variant40 = 0x7f0500d3
co.auter.hcorp:integer/m3_sys_shape_corner_extra_large_corner_family = 0x7f090022
co.auter.hcorp:dimen/design_textinput_caption_translate_y = 0x7f060090
co.auter.hcorp:macro/m3_comp_switch_disabled_unselected_handle_color = 0x7f0c011c
co.auter.hcorp:anim/rns_no_animation_20 = 0x7f010042
co.auter.hcorp:color/cardview_shadow_start_color = 0x7f050032
co.auter.hcorp:id/transition_transform = 0x7f0801ea
co.auter.hcorp:id/tag_on_receive_content_listener = 0x7f0801bf
co.auter.hcorp:color/m3_timepicker_button_ripple_color = 0x7f05021c
co.auter.hcorp:attr/drawableSize = 0x7f030180
co.auter.hcorp:layout/mtrl_picker_header_toggle = 0x7f0b0062
co.auter.hcorp:attr/layout_constraintRight_toRightOf = 0x7f030285
co.auter.hcorp:attr/colorSurfaceContainer = 0x7f030118
co.auter.hcorp:id/ignoreRequest = 0x7f0800e4
co.auter.hcorp:drawable/$mtrl_switch_thumb_unchecked_pressed__0 = 0x7f070027
co.auter.hcorp:style/Widget.Material3.MaterialCalendar.MonthNavigationButton = 0x7f1103e6
co.auter.hcorp:color/material_personalized_primary_text_disable_only = 0x7f0502be
co.auter.hcorp:attr/shapeAppearanceMediumComponent = 0x7f0303aa
co.auter.hcorp:dimen/mtrl_extended_fab_min_height = 0x7f0602b1
co.auter.hcorp:style/Theme.MaterialComponents.DayNight.Dialog = 0x7f11027e
co.auter.hcorp:string/mtrl_picker_text_input_year_abbr = 0x7f1000d2
co.auter.hcorp:anim/btn_checkbox_to_checked_icon_null_animation = 0x7f01000e
co.auter.hcorp:style/Base.Widget.AppCompat.SearchView = 0x7f1100f8
co.auter.hcorp:attr/windowSplashScreenBackground = 0x7f0304bb
co.auter.hcorp:dimen/m3_navigation_item_vertical_padding = 0x7f0601c9
co.auter.hcorp:dimen/m3_card_elevation = 0x7f0600ed
co.auter.hcorp:color/design_default_color_on_secondary = 0x7f050056
co.auter.hcorp:style/TextAppearance.Material3.ActionBar.Title = 0x7f1101f9
co.auter.hcorp:dimen/abc_action_bar_default_height_material = 0x7f060002
co.auter.hcorp:layout/mtrl_layout_snackbar = 0x7f0b0058
co.auter.hcorp:anim/m3_side_sheet_exit_to_left = 0x7f01002d
co.auter.hcorp:dimen/mtrl_calendar_navigation_height = 0x7f06028f
co.auter.hcorp:attr/tabTextColor = 0x7f030416

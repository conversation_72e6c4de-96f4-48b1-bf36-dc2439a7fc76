import AsyncStorage from '@react-native-async-storage/async-storage';

export const saveBookingAcceptanceRequest = async(bookingID)=> {
  try {
    // Get existing bookings
    const stored = await AsyncStorage.getItem('new_bookings_to_accept');
    let bookingIDs = stored ? JSON.parse(stored) : [];

    // Add new bookingID at the front
    bookingIDs.unshift({bookingID, timestamp: Date.now() });

    // Trim to latest 50
    if (bookingIDs.length > 50) {
      bookingIDs = bookingIDs.slice(0, 50);
    }
    // Save back
    await AsyncStorage.setItem('new_bookings_to_accept', JSON.stringify(bookingIDs));
  } catch (err) {
    console.error("Error saving booking ID:", err);
  }
}
export const getRecentBookingAcceptanceRequest = async () => {
  try {
    const stored = await AsyncStorage.getItem("new_bookings_to_accept");
    return stored ? JSON.parse(stored) : [];
  } catch (err) {
    console.error("Error retrieving booking IDs:", err);
    return [];
  }
};
/**
 * @function
 * @param {string} key
 * @param {object} value
 * @returns {Promise.<void>}
 */
const storeLocalStorage = async (key, value) => {
  try {
    await AsyncStorage.setItem(key, JSON.stringify(value));
  } catch (error) {
  }
};

/**
 * @function
 * @param {string} key
 * @returns {object}
 */
const getLocalStorage = async key => {
  try {
    const jsonValue = await AsyncStorage.getItem(key);
    return jsonValue != null ? JSON.parse(jsonValue) : null;
  } catch (error) {
    return null;
  }
};

export const clearLocalStorage = async () => {
  await AsyncStorage.clear();
};

export class Cache {
  static clearCache = async () => {
    await clearLocalStorage();
  };
  static storeIsSecondLogin = () => {
    storeLocalStorage('is_second_login', JSON.stringify(true));
  };

  static getDriverID = async () => {
    const driverID = await getLocalStorage('driver_id');
    try {
      return driverID;
    } catch (error) {
      return null;
    }
  };

  static storeDriverID = driverID => {
    storeLocalStorage('driver_id', driverID);
  };

  static getIsSecondLogin = async () => {
    const isSecondLogin = await getLocalStorage('is_second_login');
    try {
      return JSON.parse(isSecondLogin);
    } catch (error) {
      return false;
    }
  };

  static storeDriverLocation = location => {
    storeLocalStorage('user_location', JSON.stringify(location));
  };

  static getDriverLocation = async () => {
    const locationStringData = await getLocalStorage('user_location');
    try {
      return JSON.parse(locationStringData);
    } catch (error) {
      return null;
    }
  };
}

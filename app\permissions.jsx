import {
  Alert,
  Linking,
  Platform,
  RefreshControl,
  SafeAreaView,
  ScrollView,
  StatusBar,
  StyleSheet,
  Text,
  View,
} from 'react-native';
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'react-native-paper';
import { GLOBAL_STYLES } from '../constants/globalStyles';
import { theme } from '../constants/theme';

import { MaterialCommunityIcons } from '@expo/vector-icons';
import { useRouter } from 'expo-router';
import { EmptyListPlaceholder } from '../components/ui/emptyListPlaceholder';
import { CONSTANTS } from '../constants/constants';
import { usePermissionsActions, usePermissionsState } from '../contexts/permissionsContext';

const PermissionsScreen = () => {
  const router = useRouter();
  const { isPermissionsLoading, permissionGranted, remainingPermissions } =
    usePermissionsState();
  const { checkAndRequestRequiredPermissions } = usePermissionsActions();

  const handleOnRefresh = () => {
    checkAndRequestRequiredPermissions();
  };

  const openSettings = () => {
    Alert.alert(
      'Open Settings',
      'You will be redirected to the app settings. Please grant the required permissions and return to the app.',
      [
        {
          text: 'Cancel',
          style: 'cancel',
        },
        {
          text: 'Open Settings',
          onPress: () => {
            if (Platform.OS === 'ios') {
              Linking.openURL('app-settings:');
            } else {
              Linking.openSettings();
            }
          },
        },
      ]
    );
  };

  const getPermissionIcon = (permissionType) => {
    const iconMap = {
      location: 'map-marker',
      camera: 'camera',
      microphone: 'microphone',
      storage: 'folder',
      notification: 'bell',
      contacts: 'contacts',
      calendar: 'calendar',
      default: 'shield-alert',
    };

    // Extract permission type from permission string
    const type = permissionType?.toLowerCase();
    for (const [key, icon] of Object.entries(iconMap)) {
      if (type?.includes(key)) return icon;
    }
    return iconMap.default;
  };

  const renderPermissionItem = (permission) => (
    <Card key={permission.permission} style={styles.permissionCard} elevation={0}>
      <Card.Content style={styles.permissionCardContent}>
        <Text style={styles.permissionDescription}>
          {permission.message}
        </Text>
      </Card.Content>
    </Card>
  );

  return (
    <SafeAreaView style={styles.permissionMain}>
      <StatusBar
        barStyle="dark-content"
        backgroundColor={theme.colors.background}
      />
      <Appbar.Header mode="small" style={styles.appbarHeader}>
        <Appbar.Action
          icon="chevron-left"
          iconColor={theme.colors.onSurface}
          onPress={() => router.back()}
        />
        <Appbar.Content
          title={CONSTANTS.STRINGS.PERMISSIONS_SCREEN_TITLE}
          titleStyle={styles.appbarTitle}
        />
      </Appbar.Header>

      <ScrollView
        contentContainerStyle={styles.permissionScrollMain}
        style={styles.permissionScrollView}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={isPermissionsLoading}
            onRefresh={handleOnRefresh}
            colors={[theme.colors.primary]}
            tintColor={theme.colors.primary}
          />
        }
      >
        {remainingPermissions?.length > 0 ? (
          <>
            <Text style={styles.sectionTitle}>
              Permissions Required ({remainingPermissions.length})
            </Text>
            {remainingPermissions.map(renderPermissionItem)}
          </>
        ) : (
          <EmptyListPlaceholder
            title="All Permissions Granted"
            message="Your app is ready to use with full functionality"
            style={styles.emptyPlaceholder}
            iconSize={64}
            iconColor={theme.colors.onPrimary}
            icon="check-circle"
            backgroundColor={theme.colors.successContainer}
          />
        )}

        <Card style={styles.noteCard} elevation={0}>
          <Card.Content>
            <View style={styles.noteHeader}>
              <MaterialCommunityIcons
                name="information"
                size={20}
                color={theme.colors.primary}
              />
              <Text style={styles.noteTitle}>Privacy Notice</Text>
            </View>
            <Text style={styles.noteText}>
              The permissions mentioned above are necessary for Hover's application to operate properly.
              Any data collected, including location information, is solely intended to support you with
              various offers and vehicle locations. We respect your privacy and handle your data securely.
            </Text>
          </Card.Content>
        </Card>
        {remainingPermissions?.length > 0 && (
          <Button
            onPress={openSettings}
            labelStyle={GLOBAL_STYLES.button_text}
            mode="contained"
            style={{marginTop: 24, ...GLOBAL_STYLES.primary_contained_button}}

          >
            Open Settings
          </Button>
        )}
      </ScrollView>

      
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  permissionMain: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  appbarHeader: {
    backgroundColor: theme.colors.surface,
    elevation: 2,
  },
  appbarTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.onSurface,
  },
  permissionScrollView: {
    flex: 1,
  },
  permissionScrollMain: {
    paddingTop: 16,
    paddingHorizontal: 16,
    paddingBottom: 120, // Space for fixed button
  },
  sectionTitle: {
    fontSize: 14,
    fontFamily: 'Montserrat-Bold',
    color: theme.colors.secondary,
    marginBottom: 14,
    fontWeight: '700',
  },
  permissionCard: {
    marginBottom: 12,
    backgroundColor: theme.colors.inverseOnSurface,
    elevation:0,
    borderRadius:10
  },
  permissionCardContent: {
    paddingVertical: 16,
  },
  permissionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 8,
  },
  permissionChip: {
    backgroundColor: theme.colors.errorContainer,
  },
  chipText: {
    fontSize: 12,
    color: theme.colors.error,
  },
  permissionTitle: {
    fontSize: 16,
    fontFamily: 'Montserrat-SemiBold',
    color: theme.colors.onSurface,
    marginBottom: 4,
    textTransform: 'capitalize',
  },
  permissionDescription: {
    fontSize: 14,
    fontFamily: 'Montserrat-Regular',
    color: theme.colors.onSurfaceVariant,
    lineHeight: 20,
  },
  emptyPlaceholder: {
    paddingTop: 100,
    paddingBottom: 0,
    backgroundColor: theme.colors.error
  },
  noteCard: {
    marginTop: 24,
    backgroundColor: theme.colors.inverseOnSurface,
    opacity: 0.8,
    elevation: 0,
    borderRadius:10
  },
  noteHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  noteTitle: {
    fontSize: 14,
    fontFamily: 'Montserrat-SemiBold',
    color: theme.colors.primary,
    marginLeft: 8,
  },
  noteText: {
    fontSize: 12,
    fontFamily: 'Montserrat-Regular',
    color: theme.colors.primary,
    lineHeight: 18,
    textAlign: 'justify',
  },
  permissionButtonContainer: {
    backgroundColor: theme.colors.surface,
    position: 'absolute',
    bottom: 0,
    right: 0,
    left: 0,
    flexDirection: 'column',
    justifyContent: 'flex-start',
    alignItems: 'stretch',
    width: '100%',
    padding: 16,
    paddingBottom:20,
    gap: 8,
    ...GLOBAL_STYLES.elevation_style,
    borderTopWidth: StyleSheet.hairlineWidth,
    borderColor: theme.colors.outline,
  },
  permissionOpenSettingsButton: {
    ...GLOBAL_STYLES.primary_contained_button,
  },
  refreshButton: {
    borderColor: theme.colors.primary,
  },
  secondaryButtonText: {
    color: theme.colors.primary,
    fontFamily: 'Montserrat-SemiBold',
  },
  buttonContent: {
    paddingVertical: 8,
  },
});

export default PermissionsScreen;
{"installationFolder": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\prefab_package\\debug\\prefab", "gradlePath": ":react-native-reanimated", "packageInfo": {"packageName": "react-native-reanimated", "packageVersion": "3.17.5", "packageSchemaVersion": 2, "packageDependencies": [], "modules": [{"moduleName": "reanimated", "moduleHeaders": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-reanimated\\android\\build\\prefab-headers\\reanimated", "moduleExportLibraries": [], "abis": [{"abiName": "arm64-v8a", "abiApi": 24, "abiNdkMajor": 27, "abiStl": "c++_shared", "abiLibrary": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\Debug\\2wc3o114\\obj\\arm64-v8a\\libreanimated.so", "abiAndroidGradleBuildJsonFile": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\2wc3o114\\arm64-v8a\\android_gradle_build.json"}]}, {"moduleName": "worklets", "moduleHeaders": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-reanimated\\android\\build\\prefab-headers\\worklets", "moduleExportLibraries": [], "abis": [{"abiName": "arm64-v8a", "abiApi": 24, "abiNdkMajor": 27, "abiStl": "c++_shared", "abiLibrary": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-reanimated\\android\\build\\intermediates\\cxx\\Debug\\2wc3o114\\obj\\arm64-v8a\\libworklets.so", "abiAndroidGradleBuildJsonFile": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-reanimated\\android\\.cxx\\Debug\\2wc3o114\\arm64-v8a\\android_gradle_build.json"}]}]}}
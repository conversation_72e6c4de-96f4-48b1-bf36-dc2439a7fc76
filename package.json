{"name": "driver", "main": "expo-router/entry", "version": "1.0.0", "scripts": {"start": "expo start", "reset-project": "node ./scripts/reset-project.js", "android": "expo run:android", "ios": "expo run:ios", "web": "expo start --web", "lint": "expo lint"}, "dependencies": {"@expo/vector-icons": "^14.1.0", "@react-native-async-storage/async-storage": "2.1.2", "@react-native-firebase/app": "^23.0.1", "@react-native-firebase/auth": "^23.0.1", "@react-native-firebase/crashlytics": "^23.0.1", "@react-navigation/bottom-tabs": "^7.3.10", "@react-navigation/elements": "^2.3.8", "@react-navigation/native": "^7.1.6", "@tanstack/react-query": "^5.85.3", "axios": "^1.11.0", "expo": "~53.0.20", "expo-blur": "~14.1.5", "expo-build-properties": "^0.14.8", "expo-constants": "~17.1.7", "expo-eas-client": "^0.14.4", "expo-font": "~13.3.2", "expo-haptics": "~14.1.4", "expo-image": "~2.4.0", "expo-intent-launcher": "~12.1.5", "expo-json-utils": "^0.15.0", "expo-linking": "~7.1.7", "expo-location": "^18.1.6", "expo-manifests": "~0.16.6", "expo-notifications": "~0.31.4", "expo-router": "~5.1.4", "expo-splash-screen": "~0.30.10", "expo-status-bar": "~2.2.3", "expo-structured-headers": "^4.1.0", "expo-symbols": "~0.4.5", "expo-system-ui": "~5.0.10", "expo-task-manager": "^13.1.6", "expo-updates": "~0.28.17", "expo-web-browser": "~14.2.0", "fiction-expo-restart": "^2.0.0", "formik": "^2.4.6", "moment": "^2.30.1", "react": "19.0.0", "react-dom": "19.0.0", "react-native": "0.79.5", "react-native-gesture-handler": "~2.24.0", "react-native-paper": "^5.14.5", "react-native-reanimated": "~3.17.4", "react-native-safe-area-context": "5.4.0", "react-native-screens": "~4.11.1", "react-native-web": "~0.20.0", "react-native-webview": "13.13.5", "socket.io-client": "^4.8.1", "react-native-maps": "1.20.1"}, "devDependencies": {"@babel/core": "^7.25.2", "@types/react": "~19.0.10", "eslint": "^9.25.0", "eslint-config-expo": "~9.2.0", "typescript": "~5.8.3"}, "private": true}
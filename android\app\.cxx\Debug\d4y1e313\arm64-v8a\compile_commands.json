[{"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dappmodules_EXPORTS -ID:/PROJECTS/ORDERS/driver/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -ID:/PROJECTS/ORDERS/driver/android/app/build/generated/autolinking/src/main/jni -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni -ID:/PROJECTS/ORDERS/driver/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles\\appmodules.dir\\D_\\PROJECTS\\ORDERS\\driver\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\android\\app\\build\\generated\\autolinking\\src\\main\\jni\\autolinking.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dappmodules_EXPORTS -ID:/PROJECTS/ORDERS/driver/node_modules/react-native/ReactAndroid/cmake-utils/default-app-setup -ID:/PROJECTS/ORDERS/driver/android/app/build/generated/autolinking/src/main/jni -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni -ID:/PROJECTS/ORDERS/driver/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -Wall -Werror -Wno-error=cpp -fexceptions -frtti -std=c++20 -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o CMakeFiles\\appmodules.dir\\OnLoad.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native\\ReactAndroid\\cmake-utils\\default-app-setup\\OnLoad.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/PROJECTS/ORDERS/driver/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ComponentDescriptors.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/PROJECTS/ORDERS/driver/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\EventEmitters.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/PROJECTS/ORDERS/driver/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\Props.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\Props.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\Props.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/PROJECTS/ORDERS/driver/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\ShadowNodes.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/PROJECTS/ORDERS/driver/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\States.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\States.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\States.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/PROJECTS/ORDERS/driver/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnasyncstorage\\rnasyncstorageJSI-generated.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/PROJECTS/ORDERS/driver/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/@react-native-async-storage/async-storage/android/build/generated/source/codegen/jni/react/renderer/components/rnasyncstorage -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnasyncstorage_autolinked_build\\CMakeFiles\\react_codegen_rnasyncstorage.dir\\rnasyncstorage-generated.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\rnasyncstorage-generated.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\@react-native-async-storage\\async-storage\\android\\build\\generated\\source\\codegen\\jni\\rnasyncstorage-generated.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\ComponentDescriptors.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ComponentDescriptors.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ComponentDescriptors.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\EventEmitters.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\EventEmitters.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\EventEmitters.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\Props.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\Props.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\Props.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\ShadowNodes.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ShadowNodes.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\ShadowNodes.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\react\\renderer\\components\\rngesturehandler_codegen\\States.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\States.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\States.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\bac033cd950586cef66695376748dd33\\rngesturehandler_codegenJSI-generated.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\rngesturehandler_codegenJSI-generated.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rngesturehandler_codegen\\rngesturehandler_codegenJSI-generated.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-gesture-handler/android/build/generated/source/codegen/jni/react/renderer/components/rngesturehandler_codegen -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rngesturehandler_codegen_autolinked_build\\CMakeFiles\\react_codegen_rngesturehandler_codegen.dir\\rngesturehandler_codegen-generated.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\rngesturehandler_codegen-generated.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-gesture-handler\\android\\build\\generated\\source\\codegen\\jni\\rngesturehandler_codegen-generated.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\ComponentDescriptors.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ComponentDescriptors.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ComponentDescriptors.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\EventEmitters.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\EventEmitters.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\EventEmitters.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\Props.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\Props.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\Props.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\ShadowNodes.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ShadowNodes.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\ShadowNodes.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\States.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\States.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\States.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\react\\renderer\\components\\rnreanimated\\rnreanimatedJSI-generated.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\rnreanimatedJSI-generated.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnreanimated\\rnreanimatedJSI-generated.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-reanimated/android/build/generated/source/codegen/jni/react/renderer/components/rnreanimated -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnreanimated_autolinked_build\\CMakeFiles\\react_codegen_rnreanimated.dir\\rnreanimated-generated.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\rnreanimated-generated.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-reanimated\\android\\build\\generated\\source\\codegen\\jni\\rnreanimated-generated.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\8eaad7529768fbf569a7251e75c5b99c\\renderer\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewShadowNode.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\30172c2ed0a9506450cf9a9047f16b0e\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-safe-area-context\\common\\cpp\\react\\renderer\\components\\safeareacontext\\RNCSafeAreaViewState.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\3b85e0bf474567fe7d71ba89553ee7a3\\react\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ComponentDescriptors.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\1bb55cb7841f1b50c115080e817facf4\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\EventEmitters.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\abfc378b31af3d49feea6e40519b976a\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\Props.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\1bb55cb7841f1b50c115080e817facf4\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\ShadowNodes.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\abfc378b31af3d49feea6e40519b976a\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\States.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\8c17a226098205fb99a385bbfff9ae0b\\components\\safeareacontext\\safeareacontextJSI-generated.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\safeareacontextJSI-generated.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\safeareacontext\\safeareacontextJSI-generated.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_safeareacontext_EXPORTS -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/../../../../common/cpp -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-safe-area-context/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/safeareacontext -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o safeareacontext_autolinked_build\\CMakeFiles\\react_codegen_safeareacontext.dir\\5c8c205b9b76812d56c24d2973d1938f\\build\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-safe-area-context\\android\\build\\generated\\source\\codegen\\jni\\safeareacontext-generated.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\11c93fbc9931c2fe3e9e597d53c7e22a\\cpp\\react\\renderer\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSFullWindowOverlayShadowNode.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\7aed37dbfd44d4f2e319961fca8c8a89\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSModalScreenShadowNode.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\7aed37dbfd44d4f2e319961fca8c8a89\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenShadowNode.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\b2b3818809b989a01cac18f18cbc284d\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigShadowNode.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\11c93fbc9931c2fe3e9e597d53c7e22a\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderConfigState.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\b2b3818809b989a01cac18f18cbc284d\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewShadowNode.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\11c93fbc9931c2fe3e9e597d53c7e22a\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenStackHeaderSubviewState.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\7aed37dbfd44d4f2e319961fca8c8a89\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-screens\\common\\cpp\\react\\renderer\\components\\rnscreens\\RNSScreenState.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\rnscreens.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-screens\\android\\src\\main\\jni\\rnscreens.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-screens\\android\\src\\main\\jni\\rnscreens.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\12618fc9ced23d1402b40e5d8a04b52c\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ComponentDescriptors.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\12618fc9ced23d1402b40e5d8a04b52c\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\EventEmitters.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\4a8d875e9c0067152d2ba7b85a21f032\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\Props.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\12618fc9ced23d1402b40e5d8a04b52c\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\ShadowNodes.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\4a8d875e9c0067152d2ba7b85a21f032\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\States.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -Dreact_codegen_rnscreens_EXPORTS -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../../common/cpp -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-screens/android/src/main/jni/../../../build/generated/source/codegen/jni/react/renderer/components/rnscreens -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -fexceptions -frtti -std=c++20 -Wall -Wpedantic -Wno-gnu-zero-variadic-macro-arguments -Wno-dollar-in-identifier-extension -DLOG_TAG=\\\"ReactNative\\\" -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o rnscreens_autolinked_build\\CMakeFiles\\react_codegen_rnscreens.dir\\3e47f9424794ee12f18a4165a0e6db46\\codegen\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-screens\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\rnscreens\\rnscreensJSI-generated.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\RNCWebViewSpec-generated.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\RNCWebViewSpec-generated.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\RNCWebViewSpec-generated.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\ComponentDescriptors.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\ComponentDescriptors.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\ComponentDescriptors.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\EventEmitters.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\EventEmitters.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\EventEmitters.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\Props.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\Props.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\Props.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\RNCWebViewSpecJSI-generated.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\RNCWebViewSpecJSI-generated.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\RNCWebViewSpecJSI-generated.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\ShadowNodes.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\ShadowNodes.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\ShadowNodes.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-webview/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-webview/android/build/generated/source/codegen/jni/react/renderer/components/RNCWebViewSpec -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNCWebViewSpec_autolinked_build\\CMakeFiles\\react_codegen_RNCWebViewSpec.dir\\react\\renderer\\components\\RNCWebViewSpec\\States.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\States.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-webview\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNCWebViewSpec\\States.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build\\CMakeFiles\\react_codegen_RNEdgeToEdge.dir\\RNEdgeToEdge-generated.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\RNEdgeToEdge-generated.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\RNEdgeToEdge-generated.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build\\CMakeFiles\\react_codegen_RNEdgeToEdge.dir\\react\\renderer\\components\\RNEdgeToEdge\\ComponentDescriptors.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\ComponentDescriptors.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\ComponentDescriptors.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build\\CMakeFiles\\react_codegen_RNEdgeToEdge.dir\\react\\renderer\\components\\RNEdgeToEdge\\EventEmitters.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\EventEmitters.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\EventEmitters.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build\\CMakeFiles\\react_codegen_RNEdgeToEdge.dir\\react\\renderer\\components\\RNEdgeToEdge\\Props.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\Props.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\Props.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build\\CMakeFiles\\react_codegen_RNEdgeToEdge.dir\\react\\renderer\\components\\RNEdgeToEdge\\RNEdgeToEdgeJSI-generated.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\RNEdgeToEdgeJSI-generated.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\RNEdgeToEdgeJSI-generated.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build\\CMakeFiles\\react_codegen_RNEdgeToEdge.dir\\react\\renderer\\components\\RNEdgeToEdge\\ShadowNodes.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\ShadowNodes.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\ShadowNodes.cpp"}, {"directory": "D:/PROJECTS/ORDERS/driver/android/app/.cxx/Debug/d4y1e313/arm64-v8a", "command": "C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\ndk\\27.1.12297006\\toolchains\\llvm\\prebuilt\\windows-x86_64\\bin\\clang++.exe --target=aarch64-none-linux-android24 --sysroot=C:/Users/<USER>/AppData/Local/Android/Sdk/ndk/27.1.12297006/toolchains/llvm/prebuilt/windows-x86_64/sysroot -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/. -ID:/PROJECTS/ORDERS/driver/node_modules/react-native-edge-to-edge/android/build/generated/source/codegen/jni/react/renderer/components/RNEdgeToEdge -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/940c6befb88fdf8d1bc6da42a2c94d46/transformed/fbjni-0.7.0/prefab/modules/fbjni/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/jsi/include -isystem C:/Users/<USER>/.gradle/caches/8.13/transforms/d3dbf06d81207983406d2bec1cfbc8a3/transformed/react-android-0.79.5-debug/prefab/modules/reactnative/include -g -DANDROID -fdata-sections -ffunction-sections -funwind-tables -fstack-protector-strong -no-canonical-prefixes -D__BIONIC_NO_PAGE_SIZE_MACRO -D_FORTIFY_SOURCE=2 -Wformat -Werror=format-security   -fno-limit-debug-info  -fPIC -DLOG_TAG=\\\"ReactNative\\\" -fexceptions -frtti -std=c++20 -Wall -DFOLLY_NO_CONFIG=1 -DFOLLY_HAVE_CLOCK_GETTIME=1 -DFOLLY_USE_LIBCPP=1 -DFOLLY_CFG_NO_COROUTINES=1 -DFOLLY_MOBILE=1 -DFOLLY_HAVE_RECVMMSG=1 -DFOLLY_HAVE_PTHREAD=1 -DFOLLY_HAVE_XSI_STRERROR_R=1 -o RNEdgeToEdge_autolinked_build\\CMakeFiles\\react_codegen_RNEdgeToEdge.dir\\react\\renderer\\components\\RNEdgeToEdge\\States.cpp.o -c D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\States.cpp", "file": "D:\\PROJECTS\\ORDERS\\driver\\node_modules\\react-native-edge-to-edge\\android\\build\\generated\\source\\codegen\\jni\\react\\renderer\\components\\RNEdgeToEdge\\States.cpp"}]
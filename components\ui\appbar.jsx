import { Platform, StatusBar, StyleSheet, Text, View } from 'react-native';
import { Button, IconButton, Surface } from 'react-native-paper';
import { useSafeAreaInsets } from 'react-native-safe-area-context';
import { CONSTANTS } from '../../constants/constants';
import { theme } from '../../constants/theme';

export const AppBar = ({
  navigation,
  onBack,
  title,
  titleStyle,
  subTitle,
  color,
  backgroundColor,
  actionText,
  actionHandler,
  headerStyle,
  firstActionStyle,
  mode = 'native',
  elevation = 0,
  showShadow = true,
  centerTitle = false,
  rightActions = [],
  statusBarColor,
  statusBarStyle = 'light-content',
}) => {
  const insets = useSafeAreaInsets();

  const _handleAppbarLeftAction = onBack
    ? onBack
    : navigation && mode == 'native'
      ? () => {
        navigation.goBack();
      }
      : navigation && mode == 'drawer'
        ? () => {
          navigation.navigate(CONSTANTS.ROUTES.DRAWER.ROUTE);
        }
        : navigation
          ? () => {
            navigation.goBack();
          }
          : () => { };

  const getLeftIcon = () => {
    switch (mode) {
      case 'native':
        return Platform.OS === 'ios' ? 'chevron-left' : 'arrow-left';
      case 'drawer':
        return 'menu';
      case 'close':
        return 'close';
      default:
        return Platform.OS === 'ios' ? 'chevron-left' : 'arrow-left';
    }
  };

  const appBarBackgroundColor = backgroundColor || theme.colors.surface;
  const textColor = color || theme.colors.onSurface;
  const finalStatusBarColor = statusBarColor || appBarBackgroundColor;

  return (
    <>
      <StatusBar
        backgroundColor={finalStatusBarColor}
        barStyle={statusBarStyle}
        translucent={false}
      />
      <Surface
        style={[
          styles.appbar_container,
          showShadow && styles.appbar_shadow,
          { backgroundColor: appBarBackgroundColor },
          headerStyle,
        ]}
        elevation={elevation}
      >
        <View
          style={[
            styles.appbar_content,
            { paddingTop: Platform.OS === 'android' ? 8 : insets.top },
          ]}
        >
          {/* Left Action */}
          <View style={styles.left_section}>
            {navigation && (
              <IconButton
                icon={getLeftIcon()}
                size={24}
                iconColor={textColor}
                onPress={_handleAppbarLeftAction}
                style={[styles.nav_button, firstActionStyle]}
                mode="contained-tonal"
                containerColor="transparent"
              />
            )}
          </View>

          {/* Title Section */}
          <View style={[
            styles.title_section,
            centerTitle && styles.title_centered,
            !navigation && styles.title_no_nav
          ]}>
            <View style={styles.title_container}>
              <Text
                style={[
                  styles.appbar_title,
                  { color: textColor },
                  titleStyle,
                ]}
                numberOfLines={1}
                ellipsizeMode="tail"
              >
                {title}
              </Text>
              {subTitle && (
                <Text
                  style={[
                    styles.appbar_subtitle,
                    { color: textColor + '80' }, // 50% opacity
                  ]}
                  numberOfLines={1}
                  ellipsizeMode="tail"
                >
                  {subTitle}
                </Text>
              )}
            </View>
          </View>

          {/* Right Actions */}
          <View style={styles.right_section}>
            {rightActions.map((action, index) => (
              <IconButton
                key={index}
                icon={action.icon}
                size={action.size || 24}
                iconColor={action.color || textColor}
                onPress={action.onPress}
                style={[styles.action_button, action.style]}
                mode="contained-tonal"
                containerColor="transparent"
              />
            ))}

            {actionText && actionHandler && (
              <Button
                mode="text"
                textColor={textColor}
                onPress={actionHandler}
                style={styles.text_action_button}
                labelStyle={styles.action_text}
                compact
              >
                {actionText}
              </Button>
            )}
          </View>
        </View>

        {/* Bottom border for subtle separation */}
        {showShadow && (
          <View style={[
            styles.bottom_border,
            { backgroundColor: theme.colors.outline + '20' }
          ]} />
        )}
      </Surface>
    </>
  );
};

const styles = StyleSheet.create({
  appbar_container: {
    width: '100%',
    zIndex: 1001,
  },
  appbar_shadow: {
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.1,
    shadowRadius: 3.84,
    elevation: 3,
  },
  appbar_content: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 4,
    paddingBottom: 12,
    minHeight: 56,
  },
  left_section: {
    width: 56,
    justifyContent: 'center',
    alignItems: 'flex-start',
  },
  title_section: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: 8,
  },
  title_centered: {
    alignItems: 'center',
  },
  title_no_nav: {
    paddingLeft: 16,
  },
  right_section: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'flex-end',
    minWidth: 56,
  },
  title_container: {
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'flex-start',
    maxWidth: '100%',
  },
  appbar_title: {
    fontSize: 20,
    fontWeight: '600',
    lineHeight: 24,
    letterSpacing: 0.15,
  },
  appbar_subtitle: {
    fontSize: 14,
    fontWeight: '400',
    lineHeight: 18,
    letterSpacing: 0.25,
    marginTop: 2,
  },
  nav_button: {
    margin: 0,
  },
  action_button: {
    margin: 0,
    marginLeft: 4,
  },
  text_action_button: {
    marginLeft: 8,
    marginRight: 4,
  },
  action_text: {
    fontSize: 14,
    fontWeight: '600',
    letterSpacing: 0.25,
  },
  bottom_border: {
    height: StyleSheet.hairlineWidth,
    width: '100%',
  },
});

// Enhanced AppBar with preset variants
export const AppBarVariants = {
  // Primary variant for main screens
  Primary: (props) => (
    <AppBar
      backgroundColor={theme.colors.primary}
      color={theme.colors.onPrimary}
      statusBarColor={theme.colors.primary}
      statusBarStyle="light-content"
      showShadow={true}
      {...props}
    />
  ),

  // Surface variant for secondary screens
  Surface: (props) => (
    <AppBar
      backgroundColor={theme.colors.surface}
      color={theme.colors.onSurface}
      statusBarColor={theme.colors.surface}
      statusBarStyle="dark-content"
      showShadow={true}
      {...props}
    />
  ),

  // Transparent variant for overlays
  Transparent: (props) => (
    <AppBar
      backgroundColor="transparent"
      color={theme.colors.onSurface}
      statusBarColor="transparent"
      statusBarStyle="dark-content"
      showShadow={false}
      elevation={0}
      {...props}
    />
  ),

  // Modal variant for modal screens
  Modal: (props) => (
    <AppBar
      backgroundColor={theme.colors.surface}
      color={theme.colors.onSurface}
      mode="close"
      showShadow={true}
      centerTitle={true}
      {...props}
    />
  ),
};

// Usage examples:
// <AppBar title="Home" navigation={navigation} />
// <AppBarVariants.Primary title="Dashboard" navigation={navigation} />
// <AppBar
//   title="Settings"
//   navigation={navigation}
//   rightActions={[
//     { icon: 'cog', onPress: () => console.log('Settings') },
//     { icon: 'bell', onPress: () => console.log('Notifications') }
//   ]}
// />
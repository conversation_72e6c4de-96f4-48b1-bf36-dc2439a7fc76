co.auter.hcorp.app-browser-1.6.0-0 C:\Users\<USER>\.gradle\caches\8.13\transforms\0208c1dcf45a74103da4e70fe6ed0843\transformed\browser-1.6.0\res
co.auter.hcorp.app-lifecycle-viewmodel-ktx-2.6.2-1 C:\Users\<USER>\.gradle\caches\8.13\transforms\021a56f71cd4e7aa5fef8ac5a45aac67\transformed\lifecycle-viewmodel-ktx-2.6.2\res
co.auter.hcorp.app-lifecycle-livedata-core-2.6.2-2 C:\Users\<USER>\.gradle\caches\8.13\transforms\091ee6867fec2a48446e1efe70b63de7\transformed\lifecycle-livedata-core-2.6.2\res
co.auter.hcorp.app-expo.modules.filesystem-18.1.11-3 C:\Users\<USER>\.gradle\caches\8.13\transforms\0ec2b2613645c132734cc6d66ce0081b\transformed\expo.modules.filesystem-18.1.11\res
co.auter.hcorp.app-sqlite-framework-2.4.0-4 C:\Users\<USER>\.gradle\caches\8.13\transforms\188dcb74882d709e4310aadb898126e8\transformed\sqlite-framework-2.4.0\res
co.auter.hcorp.app-androidsvg-aar-1.4-5 C:\Users\<USER>\.gradle\caches\8.13\transforms\1c0bc357c30a319ad23642cd6094dd5a\transformed\androidsvg-aar-1.4\res
co.auter.hcorp.app-annotation-experimental-1.4.0-6 C:\Users\<USER>\.gradle\caches\8.13\transforms\1f25699b0c81390b7cf938a5dd021de1\transformed\annotation-experimental-1.4.0\res
co.auter.hcorp.app-media-1.0.0-7 C:\Users\<USER>\.gradle\caches\8.13\transforms\2843053f73262039422a00515378d8c2\transformed\media-1.0.0\res
co.auter.hcorp.app-autofill-1.1.0-8 C:\Users\<USER>\.gradle\caches\8.13\transforms\2f094c86d2a0f98a52d8c57a21bf3650\transformed\autofill-1.1.0\res
co.auter.hcorp.app-activity-1.8.0-9 C:\Users\<USER>\.gradle\caches\8.13\transforms\2f0c01a0ab4c29602558033327494c8a\transformed\activity-1.8.0\res
co.auter.hcorp.app-constraintlayout-2.0.1-10 C:\Users\<USER>\.gradle\caches\8.13\transforms\34082ba9f018526172ab8c3c89a3dbc2\transformed\constraintlayout-2.0.1\res
co.auter.hcorp.app-credentials-play-services-auth-1.2.0-rc01-11 C:\Users\<USER>\.gradle\caches\8.13\transforms\3eb942687d01e04e9e71a24476541a9b\transformed\credentials-play-services-auth-1.2.0-rc01\res
co.auter.hcorp.app-lifecycle-viewmodel-2.6.2-12 C:\Users\<USER>\.gradle\caches\8.13\transforms\3f18e4f967f6fa377cc0a1d40124a640\transformed\lifecycle-viewmodel-2.6.2\res
co.auter.hcorp.app-room-runtime-2.6.1-13 C:\Users\<USER>\.gradle\caches\8.13\transforms\443563b72932f48155563e107642094d\transformed\room-runtime-2.6.1\res
co.auter.hcorp.app-datastore-preferences-release-14 C:\Users\<USER>\.gradle\caches\8.13\transforms\44602d3f3c2c366879be2d1d19f90bba\transformed\datastore-preferences-release\res
co.auter.hcorp.app-firebase-crashlytics-20.0.0-15 C:\Users\<USER>\.gradle\caches\8.13\transforms\49e07a6d149d84a56b3d37c19168b5e4\transformed\firebase-crashlytics-20.0.0\res
co.auter.hcorp.app-lifecycle-runtime-2.6.2-16 C:\Users\<USER>\.gradle\caches\8.13\transforms\4ad34a15c2be9e07b271fbc38e4493d4\transformed\lifecycle-runtime-2.6.2\res
co.auter.hcorp.app-credentials-1.2.0-rc01-17 C:\Users\<USER>\.gradle\caches\8.13\transforms\4b777fbb16acd4827e263db9b1a694ca\transformed\credentials-1.2.0-rc01\res
co.auter.hcorp.app-gif-3.0.5-18 C:\Users\<USER>\.gradle\caches\8.13\transforms\4c4b574170d6be3e80d21c7970215775\transformed\gif-3.0.5\res
co.auter.hcorp.app-apng-3.0.5-19 C:\Users\<USER>\.gradle\caches\8.13\transforms\4ca9f376f00bd5bdddaff0afa1c0eb88\transformed\apng-3.0.5\res
co.auter.hcorp.app-awebp-3.0.5-20 C:\Users\<USER>\.gradle\caches\8.13\transforms\4e716c4e9966c57c3e7a2bd19cc8a51e\transformed\awebp-3.0.5\res
co.auter.hcorp.app-savedstate-ktx-1.2.1-21 C:\Users\<USER>\.gradle\caches\8.13\transforms\5011584881caf3e00ebc0fb8c1b21e8a\transformed\savedstate-ktx-1.2.1\res
co.auter.hcorp.app-savedstate-1.2.1-22 C:\Users\<USER>\.gradle\caches\8.13\transforms\51519baa605e410a3d563cbe20382ca7\transformed\savedstate-1.2.1\res
co.auter.hcorp.app-glide-plugin-3.0.5-23 C:\Users\<USER>\.gradle\caches\8.13\transforms\558e617ed635fa4ef0b0d0ff9626112a\transformed\glide-plugin-3.0.5\res
co.auter.hcorp.app-lifecycle-runtime-ktx-2.6.2-24 C:\Users\<USER>\.gradle\caches\8.13\transforms\5a5036ff070448ccc87b231e253b1256\transformed\lifecycle-runtime-ktx-2.6.2\res
co.auter.hcorp.app-expo.modules.notifications-0.31.4-25 C:\Users\<USER>\.gradle\caches\8.13\transforms\5d65c824d88110cca740100bf88add36\transformed\expo.modules.notifications-0.31.4\res
co.auter.hcorp.app-play-services-auth-21.4.0-26 C:\Users\<USER>\.gradle\caches\8.13\transforms\5ecb085eabbca858f83719bb406d133e\transformed\play-services-auth-21.4.0\res
co.auter.hcorp.app-coordinatorlayout-1.2.0-27 C:\Users\<USER>\.gradle\caches\8.13\transforms\5f320ea45e09fefe0dff3f7c5428ed0d\transformed\coordinatorlayout-1.2.0\res
co.auter.hcorp.app-glide-4.16.0-28 C:\Users\<USER>\.gradle\caches\8.13\transforms\6ddb8dfafaa55118a9e09c4c311183c6\transformed\glide-4.16.0\res
co.auter.hcorp.app-BlurView-version-2.0.6-29 C:\Users\<USER>\.gradle\caches\8.13\transforms\6ea56a3d40e0fc8606a6b2aea4ddec02\transformed\BlurView-version-2.0.6\res
co.auter.hcorp.app-play-services-base-18.5.0-30 C:\Users\<USER>\.gradle\caches\8.13\transforms\6f32c56df1f09404e35eae2beaed3ad9\transformed\play-services-base-18.5.0\res
co.auter.hcorp.app-fragment-1.6.1-31 C:\Users\<USER>\.gradle\caches\8.13\transforms\75de0fd7e41da4892fb437a267215d8b\transformed\fragment-1.6.1\res
co.auter.hcorp.app-datastore-release-32 C:\Users\<USER>\.gradle\caches\8.13\transforms\7d669e6eda28eee0ad5fc3d333305fb6\transformed\datastore-release\res
co.auter.hcorp.app-drawerlayout-1.1.1-33 C:\Users\<USER>\.gradle\caches\8.13\transforms\8032ff8918cb63e390c52f1cd890e3a0\transformed\drawerlayout-1.1.1\res
co.auter.hcorp.app-transition-1.5.0-34 C:\Users\<USER>\.gradle\caches\8.13\transforms\82e140510d10116f2f7640da82c17a29\transformed\transition-1.5.0\res
co.auter.hcorp.app-firebase-common-22.0.0-35 C:\Users\<USER>\.gradle\caches\8.13\transforms\83bcdcd1a4185502f68fcb8b6dd4be22\transformed\firebase-common-22.0.0\res
co.auter.hcorp.app-frameanimation-3.0.5-36 C:\Users\<USER>\.gradle\caches\8.13\transforms\876b32ee9374c02dc46a1424dc63c50a\transformed\frameanimation-3.0.5\res
co.auter.hcorp.app-cardview-1.0.0-37 C:\Users\<USER>\.gradle\caches\8.13\transforms\8991b273e00c6d82eb7719d5afc83843\transformed\cardview-1.0.0\res
co.auter.hcorp.app-tracing-ktx-1.2.0-38 C:\Users\<USER>\.gradle\caches\8.13\transforms\8a83e509b35df39f447f2e0efdb772be\transformed\tracing-ktx-1.2.0\res
co.auter.hcorp.app-room-ktx-2.6.1-39 C:\Users\<USER>\.gradle\caches\8.13\transforms\90e867b6304206cc2677902aa8ee66c4\transformed\room-ktx-2.6.1\res
co.auter.hcorp.app-datastore-core-release-40 C:\Users\<USER>\.gradle\caches\8.13\transforms\916cd886076be6dd2b77b7180360f1ce\transformed\datastore-core-release\res
co.auter.hcorp.app-expo.modules.systemui-5.0.10-41 C:\Users\<USER>\.gradle\caches\8.13\transforms\9678fba2f76be7955c974290b2290389\transformed\expo.modules.systemui-5.0.10\res
co.auter.hcorp.app-activity-ktx-1.8.0-42 C:\Users\<USER>\.gradle\caches\8.13\transforms\9b6d3f1f9e7fb31f1514dab156f4756c\transformed\activity-ktx-1.8.0\res
co.auter.hcorp.app-core-runtime-2.2.0-43 C:\Users\<USER>\.gradle\caches\8.13\transforms\9ce7855c367d38ce7c97ca93df1c4da1\transformed\core-runtime-2.2.0\res
co.auter.hcorp.app-recyclerview-1.1.0-44 C:\Users\<USER>\.gradle\caches\8.13\transforms\a2801c2cb71cd81119d7c21d6c76d9b4\transformed\recyclerview-1.1.0\res
co.auter.hcorp.app-fragment-ktx-1.6.1-45 C:\Users\<USER>\.gradle\caches\8.13\transforms\a92053466797ac484d2abe2685fa78cf\transformed\fragment-ktx-1.6.1\res
co.auter.hcorp.app-core-1.13.1-46 C:\Users\<USER>\.gradle\caches\8.13\transforms\aabce3f69efc1e684aed892eac0d7652\transformed\core-1.13.1\res
co.auter.hcorp.app-emoji2-views-helper-1.3.0-47 C:\Users\<USER>\.gradle\caches\8.13\transforms\ac4207e40467f616d5f00f116a86a608\transformed\emoji2-views-helper-1.3.0\res
co.auter.hcorp.app-sqlite-2.4.0-48 C:\Users\<USER>\.gradle\caches\8.13\transforms\b9ae3391b2f45a397a2e5ca0daee1830\transformed\sqlite-2.4.0\res
co.auter.hcorp.app-expo.modules.splashscreen-0.30.10-49 C:\Users\<USER>\.gradle\caches\8.13\transforms\bdda44796f721b57f7ba9dc705b2ac73\transformed\expo.modules.splashscreen-0.30.10\res
co.auter.hcorp.app-core-splashscreen-1.2.0-alpha02-50 C:\Users\<USER>\.gradle\caches\8.13\transforms\c1886a8679c1be95a5bdb049477a2746\transformed\core-splashscreen-1.2.0-alpha02\res
co.auter.hcorp.app-drawee-3.6.0-51 C:\Users\<USER>\.gradle\caches\8.13\transforms\c30eca784d868a84711c81c906afd268\transformed\drawee-3.6.0\res
co.auter.hcorp.app-play-services-basement-18.5.0-52 C:\Users\<USER>\.gradle\caches\8.13\transforms\c840b354751b833ba967f669749ddf08\transformed\play-services-basement-18.5.0\res
co.auter.hcorp.app-lifecycle-livedata-core-ktx-2.6.2-53 C:\Users\<USER>\.gradle\caches\8.13\transforms\c93144285b5c2db9703b5efe81f36197\transformed\lifecycle-livedata-core-ktx-2.6.2\res
co.auter.hcorp.app-lifecycle-viewmodel-savedstate-2.6.2-54 C:\Users\<USER>\.gradle\caches\8.13\transforms\cb8e6a43b16357da7bfdbe0cea8a5072\transformed\lifecycle-viewmodel-savedstate-2.6.2\res
co.auter.hcorp.app-core-ktx-1.13.1-55 C:\Users\<USER>\.gradle\caches\8.13\transforms\ce9fad65628ff0e71590f3dcab17e6bc\transformed\core-ktx-1.13.1\res
co.auter.hcorp.app-appcompat-resources-1.7.0-56 C:\Users\<USER>\.gradle\caches\8.13\transforms\d2e5969a57ec7d219f7fd39aa8ae2b79\transformed\appcompat-resources-1.7.0\res
co.auter.hcorp.app-react-android-0.79.5-debug-57 C:\Users\<USER>\.gradle\caches\8.13\transforms\d3dbf06d81207983406d2bec1cfbc8a3\transformed\react-android-0.79.5-debug\res
co.auter.hcorp.app-swiperefreshlayout-1.1.0-58 C:\Users\<USER>\.gradle\caches\8.13\transforms\d8a5bf697dc9fef78c1d1a621d8f749d\transformed\swiperefreshlayout-1.1.0\res
co.auter.hcorp.app-viewpager2-1.0.0-59 C:\Users\<USER>\.gradle\caches\8.13\transforms\d9e611442bd8622b0977b7be6536cb36\transformed\viewpager2-1.0.0\res
co.auter.hcorp.app-avif-3.0.5-60 C:\Users\<USER>\.gradle\caches\8.13\transforms\e03460a1d55ff44da0370138fe02210e\transformed\avif-3.0.5\res
co.auter.hcorp.app-material-1.12.0-61 C:\Users\<USER>\.gradle\caches\8.13\transforms\e7b600ce1a3b12fd88ac7a70edae70a3\transformed\material-1.12.0\res
co.auter.hcorp.app-emoji2-1.3.0-62 C:\Users\<USER>\.gradle\caches\8.13\transforms\eaf3da4e3df11c1fb7af91ceff1b438b\transformed\emoji2-1.3.0\res
co.auter.hcorp.app-firebase-messaging-25.0.0-63 C:\Users\<USER>\.gradle\caches\8.13\transforms\eb053893dfd80c0208d2029bf61dadd3\transformed\firebase-messaging-25.0.0\res
co.auter.hcorp.app-tracing-1.2.0-64 C:\Users\<USER>\.gradle\caches\8.13\transforms\edf6aab25478bed6adc82683162d814c\transformed\tracing-1.2.0\res
co.auter.hcorp.app-profileinstaller-1.3.1-65 C:\Users\<USER>\.gradle\caches\8.13\transforms\f056e4e5eaf6441194751899f29f1962\transformed\profileinstaller-1.3.1\res
co.auter.hcorp.app-core-common-2.0.3-66 C:\Users\<USER>\.gradle\caches\8.13\transforms\f0e8fdd275e211cd77bb2a75e22921bc\transformed\core-common-2.0.3\res
co.auter.hcorp.app-lifecycle-process-2.6.2-67 C:\Users\<USER>\.gradle\caches\8.13\transforms\f81b77428bec6ca6b761e4004bb78112\transformed\lifecycle-process-2.6.2\res
co.auter.hcorp.app-startup-runtime-1.1.1-68 C:\Users\<USER>\.gradle\caches\8.13\transforms\fa5b062acf451054aaa59d19d62842c6\transformed\startup-runtime-1.1.1\res
co.auter.hcorp.app-appcompat-1.7.0-69 C:\Users\<USER>\.gradle\caches\8.13\transforms\fb136d748d22122296a1ea8ffd25a4a4\transformed\appcompat-1.7.0\res
co.auter.hcorp.app-res-70 D:\PROJECTS\ORDERS\driver\android\app\build\generated\res\injectCrashlyticsMappingFileIdDebug
co.auter.hcorp.app-res-71 D:\PROJECTS\ORDERS\driver\android\app\build\generated\res\injectCrashlyticsVersionControlInfoDebug
co.auter.hcorp.app-pngs-72 D:\PROJECTS\ORDERS\driver\android\app\build\generated\res\pngs\debug
co.auter.hcorp.app-res-73 D:\PROJECTS\ORDERS\driver\android\app\build\generated\res\processDebugGoogleServices
co.auter.hcorp.app-resValues-74 D:\PROJECTS\ORDERS\driver\android\app\build\generated\res\resValues\debug
co.auter.hcorp.app-packageDebugResources-75 D:\PROJECTS\ORDERS\driver\android\app\build\intermediates\incremental\debug\packageDebugResources\merged.dir
co.auter.hcorp.app-packageDebugResources-76 D:\PROJECTS\ORDERS\driver\android\app\build\intermediates\incremental\debug\packageDebugResources\stripped.dir
co.auter.hcorp.app-debug-77 D:\PROJECTS\ORDERS\driver\android\app\build\intermediates\merged_res\debug\mergeDebugResources
co.auter.hcorp.app-debug-78 D:\PROJECTS\ORDERS\driver\android\app\src\debug\res
co.auter.hcorp.app-main-79 D:\PROJECTS\ORDERS\driver\android\app\src\main\res
co.auter.hcorp.app-debug-80 D:\PROJECTS\ORDERS\driver\node_modules\@react-native-async-storage\async-storage\android\build\intermediates\packaged_res\debug\packageDebugResources
co.auter.hcorp.app-debug-81 D:\PROJECTS\ORDERS\driver\node_modules\@react-native-firebase\app\android\build\intermediates\packaged_res\debug\packageDebugResources
co.auter.hcorp.app-debug-82 D:\PROJECTS\ORDERS\driver\node_modules\@react-native-firebase\auth\android\build\intermediates\packaged_res\debug\packageDebugResources
co.auter.hcorp.app-debug-83 D:\PROJECTS\ORDERS\driver\node_modules\@react-native-firebase\crashlytics\android\build\intermediates\packaged_res\debug\packageDebugResources
co.auter.hcorp.app-debug-84 D:\PROJECTS\ORDERS\driver\node_modules\expo-constants\android\build\intermediates\packaged_res\debug\packageDebugResources
co.auter.hcorp.app-debug-85 D:\PROJECTS\ORDERS\driver\node_modules\expo-eas-client\android\build\intermediates\packaged_res\debug\packageDebugResources
co.auter.hcorp.app-debug-86 D:\PROJECTS\ORDERS\driver\node_modules\expo-json-utils\android\build\intermediates\packaged_res\debug\packageDebugResources
co.auter.hcorp.app-debug-87 D:\PROJECTS\ORDERS\driver\node_modules\expo-manifests\android\build\intermediates\packaged_res\debug\packageDebugResources
co.auter.hcorp.app-debug-88 D:\PROJECTS\ORDERS\driver\node_modules\expo-modules-core\android\build\intermediates\packaged_res\debug\packageDebugResources
co.auter.hcorp.app-debug-89 D:\PROJECTS\ORDERS\driver\node_modules\expo-structured-headers\android\build\intermediates\packaged_res\debug\packageDebugResources
co.auter.hcorp.app-debug-90 D:\PROJECTS\ORDERS\driver\node_modules\expo-updates-interface\android\build\intermediates\packaged_res\debug\packageDebugResources
co.auter.hcorp.app-debug-91 D:\PROJECTS\ORDERS\driver\node_modules\expo-updates\android\build\intermediates\packaged_res\debug\packageDebugResources
co.auter.hcorp.app-debug-92 D:\PROJECTS\ORDERS\driver\node_modules\expo\android\build\intermediates\packaged_res\debug\packageDebugResources
co.auter.hcorp.app-debug-93 D:\PROJECTS\ORDERS\driver\node_modules\react-native-edge-to-edge\android\build\intermediates\packaged_res\debug\packageDebugResources
co.auter.hcorp.app-debug-94 D:\PROJECTS\ORDERS\driver\node_modules\react-native-gesture-handler\android\build\intermediates\packaged_res\debug\packageDebugResources
co.auter.hcorp.app-debug-95 D:\PROJECTS\ORDERS\driver\node_modules\react-native-reanimated\android\build\intermediates\packaged_res\debug\packageDebugResources
co.auter.hcorp.app-debug-96 D:\PROJECTS\ORDERS\driver\node_modules\react-native-safe-area-context\android\build\intermediates\packaged_res\debug\packageDebugResources
co.auter.hcorp.app-debug-97 D:\PROJECTS\ORDERS\driver\node_modules\react-native-screens\android\build\intermediates\packaged_res\debug\packageDebugResources
co.auter.hcorp.app-debug-98 D:\PROJECTS\ORDERS\driver\node_modules\react-native-webview\android\build\intermediates\packaged_res\debug\packageDebugResources

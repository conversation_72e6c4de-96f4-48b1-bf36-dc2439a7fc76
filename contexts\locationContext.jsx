// LocationContext.js
import AsyncStorage from "@react-native-async-storage/async-storage";
import * as IntentLauncher from "expo-intent-launcher";
import * as Linking from "expo-linking";
import * as Location from "expo-location";
import * as Notifications from "expo-notifications";
import * as TaskManager from "expo-task-manager";
import { createContext, useContext, useEffect, useRef, useState } from "react";
import { AppState, Platform } from "react-native";
import io from "socket.io-client";
import { CONSTANTS } from "../constants/constants";

const LOCATION_TASK_NAME = "background-location-task";
const PREF_KEY_SHOULD_TRACK = "shouldTrack";
const ANDROID_CHANNEL_ID = "location-tracking";
const HEARTBEAT_MS = 30_000; // check every 30s; adjust as needed

TaskManager.defineTask(LOCATION_TASK_NAME, async ({ data, error }) => {
    if (error) {
        console.error("🚨 Location task error:", error);
        return;
    }

    if (!data) return;

    const { locations } = data;

    try {
        const driverID = await AsyncStorage.getItem("driverID");
        if (!driverID) {
            console.error("🚨 Driver ID not found");
            return;
        }

        // Initialize socket
        const socket = io(CONSTANTS.SOCKET_SERVER, { transports: ["websocket"] });
        

        // Wait for connection
        await new Promise ((resolve, reject) => {
            const timeout = setTimeout(() => reject(new Error("Socket connect timeout")), 5000);

            socket.on("connect", () => {
                clearTimeout(timeout);
                resolve();
            });

            socket.on("connect_error", (err) => {
                clearTimeout(timeout);
                reject(err);
            });
        });

        // Send each location
        for (const loc of locations) {
            socket.emit(CONSTANTS.SOCKET_EMIT_EVENTS.DRIVER_LOCATION_DATA, {
                driverID,
                lat: loc.coords.latitude,
                lng: loc.coords.longitude,
                timestamp: loc.timestamp,
            });
            console.log("Location sent:", loc);
        }

        socket.disconnect(); // cleanup

        console.log("📍 Background location sent successfully:", JSON.stringify(locations));
    } catch (err) {
        console.error("🚨 Task handler exception:", err);
    }
});

// ---- Contexts ----
const LocationStateContext = createContext(undefined);
const LocationActionsContext = createContext(undefined);

// ---- Helpers ----
async function ensureAndroidChannel() {
    if (Platform.OS !== "android") return;
    await Notifications.setNotificationChannelAsync(ANDROID_CHANNEL_ID, {
        name: "Location Tracking",
        importance: Notifications.AndroidImportance.HIGH,
        // Don't play sounds/vibrate for a silent status indicator:
        sound: false,
        vibrationPattern: [0],
        showBadge: false,
        bypassDnd: false,
        lockscreenVisibility: Notifications.AndroidNotificationVisibility.PUBLIC,
    });
}

async function ensurePermissions() {
    const fg = await Location.requestForegroundPermissionsAsync();
    if (fg.status !== "granted") {
        throw new Error("Foreground location permission denied");
    }
    const bg = await Location.requestBackgroundPermissionsAsync();
    if (bg.status !== "granted") {
        throw new Error("Background location permission denied");
    }
}

async function postStickyStatus() {
    // A separate "sticky" notification, purely for user clarity.
    // (The real FGS notification is owned by expo-location.)
    try {
        await Notifications.scheduleNotificationAsync({
            content: {
                title: "Location tracking is ON",
                body: "Your location is being tracked in the background.",
                sticky: true, // ongoing style
            },
            trigger: null,
        });
    } catch (e) {
        // best-effort: some OEMs may behave differently
        console.warn("Sticky notification failed:", e?.message);
    }
}

async function clearAllStatusNotifications() {
    try {
        await Notifications.dismissAllNotificationsAsync();
        await Notifications.cancelAllScheduledNotificationsAsync();
    } catch { }
}

async function saveShouldTrack(v) {
    await AsyncStorage.setItem(PREF_KEY_SHOULD_TRACK, v ? "1" : "0");
}
async function getShouldTrack() {
    return (await AsyncStorage.getItem(PREF_KEY_SHOULD_TRACK)) === "1";
}

// Optional: open battery optimization settings on Android
async function openBatteryOptimizationSettings() {
    if (Platform.OS !== "android") return;
    try {
        await IntentLauncher.startActivityAsync(
            "android.settings.IGNORE_BATTERY_OPTIMIZATION_SETTINGS"
        );
    } catch {
        // fallback: open app settings
        try {
            await Linking.openSettings();
        } catch { }
    }
}

// ---- Provider ----
export const LocationContextProvider = ({ children }) => {
    const [isTracking, setIsTracking] = useState(false);
    const [hasBgPermission, setHasBgPermission] = useState(null);
    const appState = useRef(AppState.currentState);
    const heartbeat = useRef(null);

    console.log("isTracking", isTracking);

    const refreshTrackingState = async () => {
        try {
            const running = await Location.hasStartedLocationUpdatesAsync(
                LOCATION_TASK_NAME
            );
            setIsTracking(running);
        } catch (e) {
            console.warn("hasStartedLocationUpdatesAsync failed:", e?.message);
            setIsTracking(false);
        }
    };

    const startHeartbeat = () => {
        if (heartbeat.current) return;
        heartbeat.current = setInterval(async () => {
            try {
                const running = await Location.hasStartedLocationUpdatesAsync(
                    LOCATION_TASK_NAME
                );
                const shouldTrack = await getShouldTrack();
                // If user wants tracking and for some reason it's off, restart:
                if (shouldTrack && !running) {
                    console.warn("Heartbeat: task not running, restarting…");
                    await startTracking();
                }
            } catch (e) {
                console.warn("Heartbeat check failed:", e?.message);
            }
        }, HEARTBEAT_MS);
    };

    const stopHeartbeat = () => {
        if (heartbeat.current) {
            clearInterval(heartbeat.current);
            heartbeat.current = null;
        }
    };

    const startTracking = async () => {
        console.log("▶️ startTracking");
        try {
            await ensurePermissions();
            setHasBgPermission(true);
        } catch (e) {
            setHasBgPermission(false);
            console.error("Permission denied:", e?.message);
            throw e;
        }

        await ensureAndroidChannel();

        // Avoid duplicate registrations:
        const already = await Location.hasStartedLocationUpdatesAsync(
            LOCATION_TASK_NAME
        );

        if (!already) {
            try {
                await Location.startLocationUpdatesAsync(LOCATION_TASK_NAME, {
                    // Balance battery & precision to appease OEMs; tune as needed
                    accuracy: Location.Accuracy.Balanced, // or Highest for trips
                    timeInterval: 5000, // ms
                    distanceInterval: 0, // report by time
                    pausesUpdatesAutomatically: false,
                    showsBackgroundLocationIndicator: true, // iOS breadcrumb
                    // foreground service config (Android)
                    foregroundService: {
                        notificationTitle: "Tracking location",
                        notificationBody: "Running in background.",
                        notificationColor: "#ff0000",
                        notificationChannelId: ANDROID_CHANNEL_ID,
                    },
                });
                await postStickyStatus();
            } catch (err) {
                console.error("🚨 startLocationUpdatesAsync error:", err?.message);
                throw err;
            }
        } else {
            console.log("⏭️ Task already running; skipping re-registration.");
        }

        await saveShouldTrack(true);
        await refreshTrackingState();
        startHeartbeat();
    };

    const stopTracking = async () => {
        console.log("⏹️ stopTracking");
        try {
            const running = await Location.hasStartedLocationUpdatesAsync(
                LOCATION_TASK_NAME
            );
            if (running) {
                await Location.stopLocationUpdatesAsync(LOCATION_TASK_NAME);
            }
        } catch (err) {
            console.warn("stopLocationUpdatesAsync error:", err?.message);
        }
        await clearAllStatusNotifications();
        await saveShouldTrack(false);
        await refreshTrackingState();
        stopHeartbeat();
    };

    // On mount: sync state; if user opted in, restart
    useEffect(() => {
        (async () => {
            try {
                const bg = await Location.getBackgroundPermissionsStatusAsync();
                setHasBgPermission(bg.status === "granted");
            } catch { }
            await ensureAndroidChannel();
            await refreshTrackingState();

            const should = await getShouldTrack();
            if (should) {
                // If user opted in, ensure running
                const running = await Location.hasStartedLocationUpdatesAsync(
                    LOCATION_TASK_NAME
                );
                if (!running) {
                    try {
                        await startTracking();
                    } catch (e) {
                        console.warn("Auto-restart failed:", e?.message);
                    }
                } else {
                    startHeartbeat();
                }
            }
        })();

        // AppState listener: when app returns to foreground, resync/repair
        const sub = AppState.addEventListener("change", async (next) => {
            const prev = appState.current;
            appState.current = next;
            if (prev.match(/inactive|background/) && next === "active") {
                await refreshTrackingState();
                const should = await getShouldTrack();
                const running = await Location.hasStartedLocationUpdatesAsync(
                    LOCATION_TASK_NAME
                );
                if (should && !running) {
                    console.warn("App resume: task not running, restarting…");
                    try {
                        await startTracking();
                    } catch (e) {
                        console.warn("Resume restart failed:", e?.message);
                    }
                }
            }
        });

        return () => {
            sub.remove();
            stopHeartbeat();
        };
    }, []);

    return (
        <LocationStateContext.Provider
            value={{
                isTracking,
                hasBgPermission,
            }}
        >
            <LocationActionsContext.Provider
                value={{
                    startTracking,
                    stopTracking,
                    refreshTrackingState,
                    openBatteryOptimizationSettings, // optional helper
                }}
            >
                {children}
            </LocationActionsContext.Provider>
        </LocationStateContext.Provider>
    );
};

// ---- Hooks ----
export const useLocationState = () => {
    const ctx = useContext(LocationStateContext);
    if (!ctx) throw new Error("useLocationState must be used inside LocationContextProvider");
    return ctx;
};
export const useLocationActions = () => {
    const ctx = useContext(LocationActionsContext);
    if (!ctx) throw new Error("useLocationActions must be used inside LocationContextProvider");
    return ctx;
};

import { theme } from "@/constants/theme";
import { useAuthActions, useAuthState } from "@/contexts/authContext";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useRouter } from "expo-router";
import React, { useEffect } from "react";
import {
  SafeAreaView,
  ScrollView,
  StatusBar,
  StyleSheet,
  TouchableOpacity,
  View
} from "react-native";
import {
  ActivityIndicator,
  Avatar,
  Card,
  IconButton,
  Switch,
  Text
} from "react-native-paper";
import { CONSTANTS } from "../constants/constants";
import { useLocationActions, useLocationState } from "../contexts/locationContext";
import { DriverRepository } from "../data/repository/driver";


const HomeScreen = ({ }) => {
  const queryClient = useQueryClient();
  const { startTracking, stopTracking } = useLocationActions();
  const { isTracking, hasBgPermission } = useLocationState();
  const { driver } = useAuthState();
  const router = useRouter();
  console.log("driver", driver);
  const { refetchDBDriver } = useAuthActions();

  const [driverAvailabilityStatus, setDriverAvailabilityStatus] = React.useState(
    driver?.isAvailable == true
      ? CONSTANTS.DRIVER_AVAILABILITY_STATUS.AVAILABLE.value
      : CONSTANTS.DRIVER_AVAILABILITY_STATUS.UNAVAILABLE.value
  );

  useEffect(() => {
    setDriverAvailabilityStatus(
      driver?.isAvailable == true
        ? CONSTANTS.DRIVER_AVAILABILITY_STATUS.AVAILABLE.value
        : CONSTANTS.DRIVER_AVAILABILITY_STATUS.UNAVAILABLE.value
    );
  }, [driver]);

  const {
    mutate: updateAvailabilityStatus,
    isPending: isUpdatingDriverAvailabilityStatus,
    error: updateDriverAvailabilityStatusError,
  } = useMutation({
    mutationFn: async ({ driverAvailabilityStatus }) => {
      console.log("driverAvailabilityStatus", driverAvailabilityStatus);
      return await DriverRepository.updateAvailabilityStatus({ driverAvailabilityStatus });
    },
    retry: 1,
    onSuccess: () => {
      queryClient.invalidateQueries([CONSTANTS.REACT_QUERY_KEYS.DB_DRIVER]);
      // Update local state immediately for better UX
      setDriverAvailabilityStatus(
        driverAvailabilityStatus == CONSTANTS.DRIVER_AVAILABILITY_STATUS.AVAILABLE.value
          ? CONSTANTS.DRIVER_AVAILABILITY_STATUS.UNAVAILABLE.value
          : CONSTANTS.DRIVER_AVAILABILITY_STATUS.AVAILABLE.value
      );
    },
  });

  const _handleOnDriverAvailabilityStatusChange = () => {
    if (isUpdatingDriverAvailabilityStatus) return; // Prevent multiple calls

    updateAvailabilityStatus({
      driverAvailabilityStatus: driverAvailabilityStatus == CONSTANTS.DRIVER_AVAILABILITY_STATUS.AVAILABLE.value
        ? CONSTANTS.DRIVER_AVAILABILITY_STATUS.UNAVAILABLE.value
        : CONSTANTS.DRIVER_AVAILABILITY_STATUS.AVAILABLE.value
    });
  };

  const isAvailable = driverAvailabilityStatus == CONSTANTS.DRIVER_AVAILABILITY_STATUS.AVAILABLE.value;
  const statusColor = isAvailable ? theme.colors.success : theme.colors.error;
  const statusText = isAvailable ? 'Available' : 'Offline';

  const isLocationTracking = isTracking;
  const locationStatusText = isLocationTracking ? 'Tracking' : 'Not Tracking';
  const locationStatusColor = isLocationTracking ? theme.colors.success : theme.colors.error;
  const locationStatusDescription = isLocationTracking ? 'Location is being tracked' : hasBgPermission? 'Location tracking is not active' : 'Location is not being tracked. Please check permissions.';

  const _navigateToHistoryScreen = () => {
    router.push(CONSTANTS.ROUTES.HISTORY.ROUTE);
  };

  const _navigateToAcceptanceHistoryScreen = () => {
    router.push(CONSTANTS.ROUTES.BOOKING_ACCEPTANCE.ROUTE);
  };

  const _navigateToProfileScreen = () => {
    router.push(CONSTANTS.ROUTES.PROFILE.ROUTE);
  };

  const _handleStartLocation = () => {
    startTracking();
  };

  const _handleStopLocation = () => {
    stopTracking();
  };

  const _handleOnPermissionsChange = () => {
    router.push(CONSTANTS.ROUTES.PERMISSION.ROUTE);
  };

  return (
    <SafeAreaView style={styles.home_main}>
      <StatusBar
        barStyle={'dark-content'}
      />
      {/* <AppBar  /> */}

      <ScrollView
        contentContainerStyle={styles.home_scroll_content}
        style={styles.scrollView}
        showsVerticalScrollIndicator={false}
      >
        {/* Header Section */}
        <View style={styles.headerSection}>
          <Text style={styles.welcomeText}>Welcome back,</Text>
          <Text style={styles.driverName}>{driver?.firstName || 'Driver'}</Text>
        </View>

        {/* Driver Profile Card */}
        <Card style={styles.profileCard} elevation={0}>
          <Card.Content style={styles.profileCardContent}>
            <View style={styles.profileHeader}>
              <Avatar.Icon
                size={60}
                icon="account"
                style={styles.avatar}
              />
              <View style={styles.profileInfo}>
                <Text style={styles.profileName}>{driver?.firstName || 'John Doe'}</Text>
                <Text style={styles.profileId}>ID: {driver?.driverID || 'HM001'}</Text>
                <Text style={styles.profilePhone}>{driver?.phoneNumber || '+91 98765 43210'}</Text>
              </View>
              <IconButton
                icon="account-edit"
                size={24}
                iconColor={theme.colors.primary}
                onPress={_navigateToProfileScreen}
              />
            </View>

            {/* <Divider style={styles.divider} />

            
            <View style={styles.statsRow}>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{driver?.totalRides || '0'}</Text>
                <Text style={styles.statLabel}>Total Rides</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{driver?.rating || '4.5'}</Text>
                <Text style={styles.statLabel}>Rating</Text>
              </View>
              <View style={styles.statItem}>
                <Text style={styles.statNumber}>{driver?.earnings || '₹0'}</Text>
                <Text style={styles.statLabel}>Earnings</Text>
              </View>
            </View> */}
          </Card.Content>
        </Card>

        {/* Availability Status Card */}
        <Card style={styles.availabilityCard} elevation={0}>
          <Card.Content style={styles.availabilityContent}>
            <View style={styles.availabilityHeader}>
              <View style={styles.availabilityInfo}>
                <Text style={styles.availabilityTitle}>Driver Status</Text>
                <View style={styles.statusRow}>
                  <View style={[styles.statusDot, { backgroundColor: statusColor }]} />
                  <Text style={[styles.statusText, { color: theme.colors.primary }]}>
                    {statusText}
                  </Text>
                </View>
              </View>

              <View style={styles.switchContainer}>
                {isUpdatingDriverAvailabilityStatus && (
                  <ActivityIndicator
                    size="small"
                    color={theme.colors.primary}
                    style={styles.loadingIndicator}
                  />
                )}
                <Switch
                  value={isAvailable}
                  onValueChange={_handleOnDriverAvailabilityStatusChange}
                  disabled={isUpdatingDriverAvailabilityStatus}
                  trackColor={{
                    false: theme.colors.surfaceVariant,
                    true: theme.colors.onPrimary
                  }}
                  thumbColor={isAvailable ? theme.colors.primary : theme.colors.outline}
                />
              </View>
              
            </View>

            {updateDriverAvailabilityStatusError && (
              <Text style={styles.errorText}>
                Failed to update status. Please try again.
              </Text>
            )}

            <Text style={styles.availabilityDescription}>
              {isAvailable
                ? CONSTANTS.DRIVER_AVAILABILITY_STATUS.AVAILABLE.message
                : CONSTANTS.DRIVER_AVAILABILITY_STATUS.UNAVAILABLE.message
              }
            </Text>
            
          </Card.Content>
        </Card>

        {/* Location Tracking Status Card */}
        <Card style={styles.availabilityCard} elevation={0}>
          <Card.Content style={styles.availabilityContent}>
            <View style={styles.availabilityHeader}>
              <View style={styles.availabilityInfo}>
                <Text style={styles.availabilityTitle}>Location Status</Text>
                <View style={styles.statusRow}>
                  <View style={[styles.statusDot, { backgroundColor: locationStatusColor }]} />
                  <Text style={[styles.statusText, { color: theme.colors.primary }]}>
                    {locationStatusText}
                  </Text>
                </View>
              </View>
              {isTracking?<View style={styles.switchContainer}>
                <IconButton icon='close' size={24} iconColor={locationStatusColor} onPress={_handleStopLocation} />
              </View>:<View style={styles.switchContainer}>
                <IconButton icon='refresh' size={24} iconColor={locationStatusColor} onPress={_handleStartLocation} />
              </View>}
            </View>
            <Text style={styles.availabilityDescription}>
              {locationStatusDescription
              }
            </Text>

          </Card.Content>
        </Card>

        {/* Quick Actions */}
        <View style={styles.quickActionsSection}>
          <Text style={styles.sectionTitle}>Quick Actions</Text>
          <View style={styles.quickActionsGrid}>
            {/* <TouchableOpacity style={styles.actionButton}>
              <IconButton
                icon="map-marker"
                size={24}
                iconColor={theme.colors.primary}
                style={styles.actionIcon}
              />
              <Text style={styles.actionText}>Current Location</Text>
            </TouchableOpacity> */}

            <TouchableOpacity style={styles.actionButton}>
              <IconButton
                icon="history"
                size={24}
                iconColor={theme.colors.primary}
                style={styles.actionIcon}
                onPress={_navigateToHistoryScreen}
              />
              <Text style={styles.actionText}>Ride History</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton}>
              <IconButton
                icon="car"
                size={24}
                iconColor={theme.colors.primary}
                style={styles.actionIcon}
                onPress={_navigateToAcceptanceHistoryScreen}
              />
              <Text style={styles.actionText}>Ride History</Text>
            </TouchableOpacity>

            {/* <TouchableOpacity style={styles.actionButton}>
              <IconButton
                icon="wallet"
                size={24}
                iconColor={theme.colors.primary}
                style={styles.actionIcon}
              />
              <Text style={styles.actionText}>Earnings</Text>
            </TouchableOpacity> */}

            <TouchableOpacity style={styles.actionButton} onPress={_handleOnPermissionsChange}>
              <IconButton
                icon="alert-rhombus"
                size={24}
                iconColor={theme.colors.primary}
                style={styles.actionIcon}
                
              />
              <Text style={styles.actionText}>Permissions</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.actionButton}>
              <IconButton
                icon="help-circle"
                size={24}
                iconColor={theme.colors.primary}
                style={styles.actionIcon}
                onPress={_handleOnPermissionsChange}
              />
              <Text style={styles.actionText}>Support</Text>
            </TouchableOpacity>
          </View>
        </View>

        {/* Today's Summary */}
        {/* <Card style={styles.summaryCard} elevation={2}>
          <Card.Content>
            <Text style={styles.sectionTitle}>Today's Summary</Text>
            <View style={styles.summaryGrid}>
              <View style={styles.summaryItem}>
                <Text style={styles.summaryNumber}>{driver?.todayRides || '0'}</Text>
                <Text style={styles.summaryLabel}>Rides Completed</Text>
              </View>
              <View style={styles.summaryItem}>
                <Text style={styles.summaryNumber}>{driver?.todayHours || '0h'}</Text>
                <Text style={styles.summaryLabel}>Hours Online</Text>
              </View>
              <View style={styles.summaryItem}>
                <Text style={styles.summaryNumber}>{driver?.todayEarnings || '₹0'}</Text>
                <Text style={styles.summaryLabel}>Today's Earnings</Text>
              </View>
            </View>
          </Card.Content>
        </Card> */}
      </ScrollView>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  home_main: {
    flex: 1,
    backgroundColor: theme.colors.surface,
  },
  scrollView: {
    flex: 1,
  },
  home_scroll_content: {
    paddingHorizontal: 16,
    paddingBottom: 40,
    paddingTop:40
  },
  headerSection: {
    paddingVertical: 20,
    paddingHorizontal: 4,
  },
  welcomeText: {
    fontSize: 16,
    color: theme.colors.onSurfaceVariant,
    fontWeight: '400',
  },
  driverName: {
    fontSize: 28,
    fontWeight: 'bold',
    color: theme.colors.onSurface,
    marginTop: 4,
  },
  profileCard: {
    marginBottom: 16,
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    elevation: 0,
    borderColor: theme.colors.backdrop,
    borderWidth: StyleSheet.hairlineWidth,
  },
  profileCardContent: {
    padding: 20,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatar: {
    backgroundColor: theme.colors.onPrimary,
  },
  profileInfo: {
    flex: 1,
    marginLeft: 16,
  },
  profileName: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.onSurface,
  },
  profileId: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
    marginTop: 2,
  },
  profilePhone: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
    marginTop: 2,
  },
  divider: {
    marginVertical: 16,
    backgroundColor: theme.colors.outline,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statNumber: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  statLabel: {
    fontSize: 12,
    color: theme.colors.onSurfaceVariant,
    marginTop: 4,
  },
  availabilityCard: {
    marginBottom: 16,
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
    borderColor: theme.colors.backdrop,
    borderWidth: StyleSheet.hairlineWidth,
  },
  availabilityContent: {
    padding: 20,
  },
  availabilityHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  availabilityInfo: {
    flex: 1,
  },
  availabilityTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.onSurface,
    marginBottom: 8,
  },
  statusRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 8,
  },
  statusText: {
    fontSize: 16,
    fontWeight: '600',
  },
  switchContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  loadingIndicator: {
    marginRight: 12,
  },
  availabilityDescription: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
    marginTop: 12,
    lineHeight: 20,
  },
  errorText: {
    fontSize: 14,
    color: theme.colors.error,
    marginTop: 8,
  },
  quickActionsSection: {
    marginBottom: 16,
  },
  sectionTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.onSurface,
    marginBottom: 16,
    paddingHorizontal: 4,
  },
  quickActionsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  actionButton: {
    width: '48%',
    backgroundColor: theme.colors.surface,
    borderRadius: 12,
    padding: 16,
    alignItems: 'center',
    marginBottom: 12,
    borderColor: theme.colors.backdrop,
    borderWidth: StyleSheet.hairlineWidth,
  },
  actionIcon: {
    margin: 0,
    backgroundColor: theme.colors.onPrimary,
  },
  actionText: {
    fontSize: 14,
    color: theme.colors.onSurface,
    textAlign: 'center',
    marginTop: 8,
    fontWeight: '500',
  },
  summaryCard: {
    backgroundColor: theme.colors.surface,
    borderRadius: 16,
  },
  summaryGrid: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 16,
  },
  summaryItem: {
    alignItems: 'center',
  },
  summaryNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    color: theme.colors.primary,
  },
  summaryLabel: {
    fontSize: 12,
    color: theme.colors.onSurfaceVariant,
    marginTop: 4,
    textAlign: 'center',
  },
});

export default HomeScreen;
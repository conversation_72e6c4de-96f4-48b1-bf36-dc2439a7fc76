import { MaterialIcons } from "@expo/vector-icons";
import { useQuery } from "@tanstack/react-query";
import { useLocalSearchParams } from "expo-router";
import { useEffect, useState } from "react";
import { Animated, RefreshControl, SafeAreaView, ScrollView, StyleSheet, View } from "react-native";
import {
  <PERSON>ppbar,
  Button,
  Card,
  Divider,
  Text
} from "react-native-paper";
import { CONSTANTS } from "./constants";
import { theme } from "./theme";
import { BookingRepository } from "../data/repository/booking";

import moment from "moment";
import { EmptyListPlaceholder } from "../components/ui/emptyListPlaceholder";
import { LoadingComponent } from "../components/ui/loadingComponent";

const BookingAcceptanceScreen = () => {
  const params = useLocalSearchParams();
  const [fadeAnim] = useState(new Animated.Value(0));
  const [slideAnim] = useState(new Animated.Value(50));
  console.log("params", params);

  const {
    isLoading: isLoadingBooking,
    data: booking,
    error: bookingError,
    refetch,
  } = useQuery({
    queryKey: [CONSTANTS.REACT_QUERY_KEYS.BOOKINGS, params?.bookingID],
    queryFn: () => { return BookingRepository.getBookingByID({ bookingID: parseInt(params?.bookingID) }) },
    enabled: Boolean(params?.bookingID),
  });


  useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 600,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 600,
        useNativeDriver: true,
      }),
    ]).start();
  }, []);

  const handleAcceptBooking = () => {
    console.log("Booking accepted:", booking?.bookingID);
    // Add navigation or API call here
  };

  const handleDeclineBooking = () => {
    console.log("Booking declined:", booking?.bookingID);
    // Add navigation or API call here
  };

  return (
    <SafeAreaView style={styles.container}>
      <Appbar.Header mode="small" style={styles.appbarHeader}>
        <Appbar.Action icon="chevron-left" color={theme.colors.onSurface} onPress={() => router.back()} />
        <Appbar.Content title="Booking Request" titleStyle={{ fontSize: 20, fontWeight: 'bold' }} />
      </Appbar.Header>

      {isLoadingBooking ? (
        <LoadingComponent />
      ) : booking && booking.bookingStatus == CONSTANTS.BOOKING_STATUS.PENDING ? (
        <ScrollView refreshControl={<RefreshControl refreshing={isLoadingBooking} onRefresh={() => refetch()} />} style={styles.container} >
          <Animated.View
            style={[
              styles.content,
              {
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }],
              },
            ]}
          >
            {/* Trip Details */}
            <Card style={styles.tripCard} elevation={0}>
              <Card.Content>
                <Text variant="titleMedium" style={styles.sectionTitle}>
                  Trip Details
                </Text>

                {/* Pickup Location */}
                <View style={styles.locationContainer}>
                  <View style={styles.locationIcon}>
                    <MaterialIcons name="radio-button-checked" size={20} color="#4CAF50" />
                  </View>
                  <View style={styles.locationDetails}>
                    <Text variant="labelMedium" style={styles.locationLabel}>
                      Pickup
                    </Text>
                    <Text variant="bodyLarge" style={styles.locationText}>
                      {booking.bookingStartAddress}
                    </Text>
                  </View>
                </View>

                {/* Route Line */}
                <View style={styles.routeLine} />

                {/* Drop-off Location */}
                <View style={styles.locationContainer}>
                  <View style={styles.locationIcon}>
                    <MaterialIcons name="location-on" size={20} color="#FF5722" />
                  </View>
                  <View style={styles.locationDetails}>
                    <Text variant="labelMedium" style={styles.locationLabel}>
                      Drop-off
                    </Text>
                    <Text variant="bodyLarge" style={styles.locationText}>
                      {booking.bookingEndAddress}
                    </Text>
                  </View>
                </View>
              </Card.Content>
            </Card>

            {/* Time & Distance Info */}
            <Card style={styles.infoCard} elevation={0}>
              <Card.Content>
                <View style={styles.infoGrid}>
                  <View style={styles.infoItem}>
                    <MaterialIcons name="schedule" size={24} color="#2196F3" />
                    <Text variant="labelMedium" style={styles.infoLabel}>
                      PICKUP TIME
                    </Text>
                    <Text variant="titleMedium" style={styles.infoValue}>
                      {booking.bookingType == CONSTANTS.BOOKING_TYPE.SCHEDULED ? booking.scheduledBookingSlot : booking.rentalBookingSlot}
                    </Text>
                    <Text variant="bodySmall" style={styles.infoSubtext}>
                      {booking.bookingType == CONSTANTS.BOOKING_TYPE.SCHEDULED ? moment(booking.scheduledBookingDate).format('DD MMM YYYY') : moment(booking.rentalBookingDate).format('DD MMM YYYY')}
                    </Text>
                  </View>

                  <Divider style={styles.verticalDivider} />

                  <View style={styles.infoItem}>
                    <MaterialIcons name="directions-car" size={24} color="#FF9800" />
                    <Text variant="labelMedium" style={styles.infoLabel}>
                      DURATION
                    </Text>
                    <Text variant="titleMedium" style={styles.infoValue}>
                      {booking.bookingType == CONSTANTS.BOOKING_TYPE.RENTAL ? booking.rentalBookingDuration : '--'}
                    </Text>
                    <Text variant="bodySmall" style={styles.infoSubtext}>
                      {'--'}
                    </Text>
                  </View>


                </View>
              </Card.Content>
            </Card>

            {/* Action Buttons */}
            <View style={styles.buttonContainer}>
              <Button
                mode="outlined"
                onPress={handleDeclineBooking}
                style={styles.declineButton}
                labelStyle={styles.declineButtonText}
              >
                Decline
              </Button>
              <Button
                mode="contained"
                onPress={handleAcceptBooking}
                style={styles.acceptButton}
                labelStyle={styles.acceptButtonText}
              >
                Accept Booking
              </Button>
            </View>
          </Animated.View>
        </ScrollView>
      ) :
        <ScrollView refreshControl={<RefreshControl refreshing={isLoadingBooking} onRefresh={() => refetch()} />} style={{ ...styles.container, paddingTop: 40 }} ><EmptyListPlaceholder
          title="No Booking Found"
          message="The booking you are looking for does not exist or has been cancelled."
          iconSize={64}
          icon="alert-circle"
          iconColor={theme.colors.error}
          titleStyle={{ color: theme.colors.error, fontSize: 18, fontWeight: 'bold' }}
          textStyle={{ color: theme.colors.primary, fontSize: 14, textAlign: 'center' }}

        />
        </ScrollView>
      }
    </SafeAreaView>

  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: theme.colors.background,
  },
  content: {
    padding: 16,
    paddingBottom: 32,
  },
  header: {
    padding: 20,
    marginBottom: 16,
    borderRadius: 12,
    backgroundColor: "#fff",
  },
  headerContent: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 8,
  },
  headerTitle: {
    fontWeight: "600",
    color: "#1a1a1a",
  },
  statusChip: {
    backgroundColor: "#FFF3E0",
  },
  chipText: {
    color: "#E65100",
    fontSize: 12,
    fontWeight: "600",
  },
  bookingId: {
    color: "#666",
    fontWeight: "500",
  },
  customerCard: {
    marginBottom: 16,
    borderRadius: 12,
    elevation: 2,
  },
  customerInfo: {
    flexDirection: "row",
    alignItems: "center",
  },
  avatar: {
    backgroundColor: "#2196F3",
    marginRight: 16,
  },
  customerDetails: {
    flex: 1,
  },
  customerName: {
    fontWeight: "600",
    color: "#1a1a1a",
    marginBottom: 4,
  },
  ratingContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  rating: {
    marginLeft: 4,
    color: "#666",
    fontWeight: "500",
  },
  tripCard: {
    marginBottom: 16,
    borderRadius: 12,
    elevation: 0,
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: theme.colors.outline,

  },
  sectionTitle: {
    fontWeight: "600",
    color: "#1a1a1a",
    marginBottom: 16,
  },
  locationContainer: {
    flexDirection: "row",
    alignItems: "flex-start",
    marginBottom: 8,
  },
  locationIcon: {
    width: 32,
    alignItems: "center",
    paddingTop: 2,
  },
  locationDetails: {
    flex: 1,
    marginLeft: 8,
  },
  locationLabel: {
    color: "#666",
    fontWeight: "600",
    marginBottom: 4,
    fontSize: 11,
  },
  locationText: {
    color: "#1a1a1a",
    lineHeight: 22,
    fontWeight:'600'
  },
  routeLine: {
    width: 2,
    height: 20,
    backgroundColor: "#E0E0E0",
    marginLeft: 15,
    marginVertical: 8,
  },
  infoCard: {
    marginBottom: 24,
    borderRadius: 12,
    elevation: 0,
    borderWidth: StyleSheet.hairlineWidth,
    borderColor: theme.colors.outline,
  },
  infoGrid: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
  },
  infoItem: {
    flex: 1,
    alignItems: "center",
  },
  infoLabel: {
    color: "#666",
    fontWeight: "600",
    marginTop: 8,
    marginBottom: 4,
    fontSize: 11,
  },
  infoValue: {
    fontWeight: "700",
    color: "#1a1a1a",
    marginBottom: 2,
  },
  infoSubtext: {
    color: "#888",
  },
  verticalDivider: {
    width: 1,
    height: 40,
    backgroundColor: "#E0E0E0",
    marginHorizontal: 8,
  },
  buttonContainer: {
    flexDirection: "row",
    gap: 12,
  },
  declineButton: {
    flex: 1,
    borderColor: "#E0E0E0",
    borderWidth: 2,
    paddingVertical: 8,
    borderRadius: 12,
  },
  declineButtonText: {
    color: "#666",
    fontWeight: "600",
    fontSize: 16,
  },
  acceptButton: {
    flex: 2,
    backgroundColor: "#4CAF50",
    paddingVertical: 8,
    borderRadius: 12,
    elevation: 3,
  },
  acceptButtonText: {
    color: "#fff",
    fontWeight: "700",
    fontSize: 16,
  },
});

export default BookingAcceptanceScreen;
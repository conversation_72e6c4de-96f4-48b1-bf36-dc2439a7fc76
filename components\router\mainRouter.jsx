import { useAuthState } from "@/contexts/authContext";
import { Stack } from "expo-router";
import { CONSTANTS } from "../../constants/constants";
import { LoadingComponent } from "../ui/loadingComponent";

export const MainRouter = () => {
    const { isFirebaseAuthLoading, firebaseDriver, authError, isLoadingDriver, driverError, driver } = useAuthState();
    return isFirebaseAuthLoading || isLoadingDriver ? <LoadingComponent /> : (
        <Stack>
            <Stack.Protected guard={!isFirebaseAuthLoading && firebaseDriver && driver}>
                <Stack.Screen name={CONSTANTS.ROUTES.HOME.ROUTE} options={{ headerShown: false, }} />
            </Stack.Protected>
            <Stack.Protected guard={!isFirebaseAuthLoading && firebaseDriver && driver}>
                <Stack.Screen name={CONSTANTS.ROUTES.HISTORY.ROUTE} options={{ headerShown: false, }} />
            </Stack.Protected>
            <Stack.Protected guard={!isFirebaseAuthLoading && firebaseDriver && driver}>
                <Stack.Screen name={CONSTANTS.ROUTES.PROFILE.ROUTE} options={{ headerShown: false, }} />
            </Stack.Protected>
            <Stack.Protected guard={!isFirebaseAuthLoading && (!firebaseDriver || !driver)}>
                <Stack.Screen name={CONSTANTS.ROUTES.SIGN_IN.ROUTE} options={{ headerShown: false, }} />
            </Stack.Protected>
            <Stack.Protected guard={!isFirebaseAuthLoading && firebaseDriver && driver}>
                <Stack.Screen name={CONSTANTS.ROUTES.BOOKING_ACCEPTANCE.ROUTE} options={{ headerShown: false, }} />
            </Stack.Protected>
            <Stack.Screen name={CONSTANTS.ROUTES.PERMISSION.ROUTE} options={{ headerShown: false, }} />
            <Stack.Screen name="+not-found" options={{ headerShown: false, }} />
        </Stack>)
};

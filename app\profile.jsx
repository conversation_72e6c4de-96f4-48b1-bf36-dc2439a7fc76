import { useMutation } from '@tanstack/react-query';
import { useRouter } from 'expo-router';
import { useFormik } from 'formik';
import { useEffect, useState } from 'react';
import {
  Alert,
  KeyboardAvoidingView,
  Platform,
  RefreshControl,
  SafeAreaView,
  ScrollView,
  StatusBar,
  StyleSheet,
  ToastAndroid,
  View
} from 'react-native';
import {
  Appbar,
  Button,
  Card,
  IconButton,
  Surface,
  Text,
  TextInput
} from 'react-native-paper';
import { SimpleTextAlert } from '../components/ui/alerts';
import { CONSTANTS } from '../constants/constants';
import { theme } from '../constants/theme';
import { useAuthActions, useAuthState } from '../contexts/authContext';
import { DriverRepository } from '../data/repository/driver';

const ProfileScreen = ({ }) => {
  const router = useRouter();
  const [showSuccessMessage, setShowSuccessMessage] = useState(false);
  const [profileCompletion, setProfileCompletion] = useState(0);

  const {
    driver,
    isLoadingDriver,
    isFetchingDriver,
  } = useAuthState();
  const { refetchDBDriver } = useAuthActions();

  const { mutate: updateProfile, isPending: isUpdatingDriverProfile, error: updateDriverError } =
    useMutation({
      mutationFn: DriverRepository.updateProfile,
      retry: 1,
      onSuccess: () => {
        if (Platform.OS === 'android') {
          ToastAndroid.show('Profile updated successfully', ToastAndroid.SHORT);
        } else {
          Alert.alert('Success', 'Profile updated successfully');
        }
        setShowSuccessMessage(true);
        setTimeout(() => setShowSuccessMessage(false), 3000);
        refetchDBDriver();
      },
      onError: (error) => {
        if (Platform.OS === 'android') {
          ToastAndroid.show('Failed to update profile', ToastAndroid.SHORT);
        } else {
          Alert.alert('Error', 'Failed to update profile');
        }
      }
    });

  const profileUpdateForm = useFormik({
    initialValues: {
      firstName: '',
      lastName: '',
      email: '',
      address1: '',
    },
    validateOnMount: false,
    validateOnChange: false,
    validate: values => {
      const errors = {};
      if (!values.firstName?.trim()) {
        errors.firstName = 'First name is required';
      } else if (values.firstName.trim().length < 2) {
        errors.firstName = 'First name must be at least 2 characters';
      }

      if (!values.lastName?.trim()) {
        errors.lastName = 'Last name is required';
      } else if (values.lastName.trim().length < 2) {
        errors.lastName = 'Last name must be at least 2 characters';
      }

      if (!values.email?.trim()) {
        errors.email = 'Email address is required';
      } else if (!/^[A-Z0-9._%+-]+@[A-Z0-9.-]+\.[A-Z]{2,4}$/i.test(values.email)) {
        errors.email = 'Please enter a valid email address';
      }

      if (!values.address1?.trim()) {
        errors.address1 = 'Address is required';
      } else if (values.address1.trim().length < 10) {
        errors.address1 = 'Address must be at least 10 characters long';
      }

      return errors;
    },
    onSubmit: async values => {
      // Trim all values before submission
      const trimmedValues = {
        firstName: values.firstName.trim(),
        lastName: values.lastName.trim(),
        email: values.email.trim(),
        address1: values.address1.trim(),
      };
      await updateProfile(trimmedValues);
    },
  });

  // Calculate profile completion percentage
  useEffect(() => {
    if (driver) {
      const fields = ['firstName', 'lastName', 'email', 'address1'];
      const completedFields = fields.filter(field => {
        const value = profileUpdateForm.values[field] || driver[field];
        return value && value.trim().length > 0;
      });
      setProfileCompletion((completedFields.length / fields.length) * 100);
    }
  }, [driver, profileUpdateForm.values]);

  useEffect(() => {
    if (driver) {
      profileUpdateForm.setFieldValue('firstName', driver.firstName || '');
      profileUpdateForm.setFieldValue('lastName', driver.lastName || '');
      profileUpdateForm.setFieldValue('email', driver.email || '');
      profileUpdateForm.setFieldValue('address1', driver.address1 || '');
    }
  }, [driver]);


  const renderFormField = (fieldName, label, placeholder, keyboardType = 'default', multiline = false) => {
    const hasError = profileUpdateForm.touched[fieldName] && profileUpdateForm.errors[fieldName];
    const hasValue = profileUpdateForm.values[fieldName]?.trim().length > 0;

    return (
      <View style={styles.form_field_container}>
        <Text style={styles.form_input_label}>{label}</Text>
        <TextInput
          value={profileUpdateForm.values[fieldName]}
          keyboardType={keyboardType}
          onBlur={profileUpdateForm.handleBlur(fieldName)}
          placeholder={placeholder}
          onChangeText={profileUpdateForm.handleChange(fieldName)}
          style={[
            styles.form_input,
            hasError && styles.form_input_error,
            hasValue && styles.form_input_filled
          ]}
          mode="outlined"
          theme={{roundness:9}}
          error={hasError}
          right={hasValue && !hasError ? <TextInput.Icon icon="check-circle" iconColor={theme.colors.primary} /> : null}
        />
        {hasError && (
          <View style={styles.error_container}>
            <Text style={styles.error_text}>{profileUpdateForm.errors[fieldName]}</Text>
          </View>
        )}
      </View>
    );
  };

  return (
    <SafeAreaView style={styles.profile_main}>
      <StatusBar
        barStyle={'dark-content'}
        backgroundColor={theme.colors.onSurface}
      />
      <Appbar.Header mode='small' style={styles.appbar_header}>
        <Appbar.Action icon="chevron-left" color={theme.colors.onSurface} onPress={() => router.back()} />
        <Appbar.Content title={CONSTANTS.STRINGS.PROFILE_SCREEN_TITLE} titleStyle={{ fontSize: 20, fontWeight: 'bold' }} />

      </Appbar.Header>
      <KeyboardAvoidingView
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
        style={{ flex: 1, flexDirection: 'column', justifyContent: 'center' }}
      >
        <ScrollView
          contentContainerStyle={styles.scroll_content}
          style={styles.scroll_view}
          refreshControl={
            <RefreshControl
              refreshing={isLoadingDriver}
              onRefresh={refetchDBDriver}
              colors={[theme.colors.primary]}
              tintColor={theme.colors.primary}
            />
          }
          showsVerticalScrollIndicator={false}
        >
          {driver ? (
            <>


              {/* Profile Incomplete Warning */}
              {!driver.isProfileComplete?.() && (
                <SimpleTextAlert
                  message={CONSTANTS.STRINGS.PROFILE_SCREEN_SUBTITLE}
                  style={{ marginTop: 16 }}
                  icon="alert-circle"
                  iconColor={theme.colors.warning}
                />
              )}

              {/* Form Card */}
              <View style={styles.form_card}>
                {/* <View style={styles.form_header}>
                  <Text style={styles.form_title}>Personal Information</Text>
                  <Text style={styles.form_subtitle}>Update your profile details below</Text>
                </View> */}

                {/* <Divider style={styles.form_divider} /> */}

                {/* Form Fields */}
                {renderFormField('firstName', 'First Name', 'Enter your first name', 'default')}
                {renderFormField('lastName', 'Last Name', 'Enter your last name', 'default')}
                {renderFormField('email', 'Email Address', 'Enter your email address', 'email-address')}
                {renderFormField('address1', 'Address', 'Enter your complete address', 'default', true)}

                {/* Save Button */}
                <Button
                  onPress={profileUpdateForm.handleSubmit}
                  loading={isUpdatingDriverProfile}
                  disabled={isUpdatingDriverProfile || Object.keys(profileUpdateForm.errors).length > 0}
                  mode="contained"
                  style={styles.save_button}
                  labelStyle={styles.save_button_text}

                >
                  {isUpdatingDriverProfile ? 'Updating Profile...' : 'Save Changes'}
                </Button>

                {/* Form Validation Summary */}
                {Object.keys(profileUpdateForm.errors).length > 0 && (
                  <Surface style={styles.validation_summary} elevation={0}>
                    <Text style={styles.validation_title}>Please fix the following issues:</Text>
                    {Object.values(profileUpdateForm.errors).map((error, index) => (
                      <Text key={index} style={styles.validation_item}>• {error}</Text>
                    ))}
                  </Surface>
                )}
              </View>
            </>
          ) : (
            <Card style={styles.error_card} elevation={0}>
              <Card.Content style={styles.error_content}>
                <IconButton
                  icon="alert-circle"
                  size={48}
                  iconColor={theme.colors.error}
                  style={styles.error_icon}
                />
                <Text style={styles.error_title}>Unable to Load Profile</Text>
                <Text style={styles.error_subtitle}>
                  {CONSTANTS.STRINGS.PROFILE_SCREEN_ERROR_LOADING_PROFILE}
                </Text>
                <Button
                  mode="outlined"
                  onPress={refetchDBDriver}
                  style={styles.retry_button}
                  loading={isLoadingDriver}
                >
                  Retry
                </Button>
              </Card.Content>
            </Card>
          )}
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>

  );
};

const styles = StyleSheet.create({
  appbar_header:{
    backgroundColor: theme.colors.surface,
    elevation: 2,
  },
  profile_main: {
    flex: 1,
    backgroundColor: theme.colors.background,
    paddingBottom: 40
  },
  scroll_view: {
    flex: 1,
  },
  scroll_content: {
    paddingTop: 0,
    paddingHorizontal: 16,
    paddingBottom: 24,
  },
  success_banner: {
    marginBottom: 16,
    backgroundColor: theme.colors.primaryContainer,
  },
  profile_header_card: {
    marginBottom: 16,
    backgroundColor: theme.colors.surface,
    borderRadius: 10,
  },
  profile_header_content: {
    padding: 20,
  },
  profile_header_row: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  avatar_container: {
    position: 'relative',
    marginRight: 16,
  },
  profile_avatar: {
    backgroundColor: theme.colors.primaryContainer,
  },
  avatar_edit_overlay: {
    position: 'absolute',
    bottom: -4,
    right: -4,
    backgroundColor: theme.colors.primary,
    borderRadius: 10,
    borderWidth: 2,
    borderColor: theme.colors.surface,
  },
  camera_icon: {
    margin: 0,
  },
  profile_header_info: {
    flex: 1,
  },
  profile_header_name: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.onSurface,
    marginBottom: 4,
  },
  profile_header_id: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
    marginBottom: 2,
  },
  profile_header_phone: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
    marginBottom: 12,
  },
  completion_container: {
    marginTop: 8,
  },
  completion_text: {
    fontSize: 12,
    color: theme.colors.onSurfaceVariant,
    marginBottom: 4,
  },
  progress_bar: {
    height: 6,
    borderRadius: 10,
  },
  status_chips: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  status_chip: {
    marginRight: 0,
  },
  warning_card: {
    marginBottom: 16,
    backgroundColor: theme.colors.warningContainer,
    borderRadius: 10,
  },
  warning_content: {
    padding: 16,
  },
  warning_row: {
    flexDirection: 'row',
    alignItems: 'flex-start',
  },
  warning_icon: {
    margin: 0,
    marginRight: 12,
  },
  warning_text_container: {
    flex: 1,
  },
  form_card: {
    backgroundColor: theme.colors.surface,
    borderRadius: 10,
    marginTop: 10
  },
  form_content: {
    padding: 20,
  },
  form_header: {
    marginBottom: 16,
  },
  form_title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: theme.colors.onSurface,
    marginBottom: 4,
  },
  form_subtitle: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
  },
  form_divider: {
    backgroundColor: theme.colors.outline,
    marginBottom: 20,
  },
  form_field_container: {
    marginBottom: 20,
  },
  form_input_label: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.onSurface,
    marginBottom: 8,
  },
  form_input: {
    backgroundColor: theme.colors.surface,
    fontSize: 16,
    borderRadius: 10,
  },
  form_input_error: {
    borderColor: theme.colors.error,
  },
  form_input_filled: {
    borderColor: theme.colors.primary,
  },
  error_container: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  error_text: {
    fontSize: 12,
    color: theme.colors.error,
    marginLeft: 4,
  },
  save_button: {
    marginTop: 24,
    paddingVertical: 6,
    borderRadius: 10,
  },
  save_button_text: {
    fontSize: 16,
    fontWeight: '600',
  },
  validation_summary: {
    marginTop: 16,
    padding: 12,
    backgroundColor: theme.colors.errorContainer,
    borderRadius: 10,
  },
  validation_title: {
    fontSize: 14,
    fontWeight: '600',
    color: theme.colors.onErrorContainer,
    marginBottom: 8,
  },
  validation_item: {
    fontSize: 12,
    color: theme.colors.onErrorContainer,
    marginBottom: 2,
  },
  error_card: {
    backgroundColor: theme.colors.surface,
    borderRadius: 10,
    marginTop: 24,
  },
  error_content: {
    padding: 32,
    alignItems: 'center',
  },
  error_icon: {
    margin: 0,
    marginBottom: 16,
  },
  error_title: {
    fontSize: 18,
    fontWeight: 'bold',
    color: theme.colors.onSurface,
    marginBottom: 8,
    textAlign: 'center',
  },
  error_subtitle: {
    fontSize: 14,
    color: theme.colors.onSurfaceVariant,
    textAlign: 'center',
    marginBottom: 24,
  },
  retry_button: {
    paddingHorizontal: 24,
  },
});

export default ProfileScreen;
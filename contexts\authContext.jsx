import AsyncStorage from '@react-native-async-storage/async-storage';
import { signOut as firebaseSignOut, getAuth, onAuthStateChanged, signInWithPhoneNumber } from '@react-native-firebase/auth';
import { useMutation, useQuery } from "@tanstack/react-query";
import * as Notifications from "expo-notifications";
import { Restart } from 'fiction-expo-restart';
import { createContext, useContext, useEffect, useState } from "react";
import { CONSTANTS } from '../constants/constants';
import { DriverRepository } from "../data/repository/driver";

const AuthStateContext = createContext(undefined);
const AuthActionsContext = createContext(undefined);

export const AuthContextProvider = ({ children }) => {
    const [resendOTPState, setResendOTPState] = useState({
        prevSentAt: null,
        resendPhoneNumber: null,
        isResendable: false,
    });
    const [isFirebaseAuthLoading, setIsFirebaseAuthLoading] = useState(true);
    const [firebaseDriver, setFirebaseDriver] = useState(null);
    const [firebaseToken, setFirebaseToken] = useState(null);
    const [authError, setAuthError] = useState(null);

    const [signInState, setSignInState] = useState({
        isLoading: false,
        success: false,
        error: null,
        confirmationResult: null,
    });

    const [signOutState, setSignOutState] = useState({
        isLoading: false,
        success: false,
        error: null,
    });

    
    // Fetch driver profile when token is available
    const {
        isLoading: isLoadingDriver,
        data: driver,
        error: driverError,
        refetch: refetchDBDriver,
    } = useQuery({
        queryKey: [CONSTANTS.REACT_QUERY_KEYS.DB_DRIVER],
        queryFn: DriverRepository.getDBDriver,
        enabled: Boolean(firebaseToken),
        cacheTime: Infinity,
        staleTime: Infinity,
        retry: 1,
    });

    const { mutate: updateProfile, isPending: isUpdatingDriverProfile, error: updateDriverError } =
        useMutation({
            mutationFn: DriverRepository.updateProfile,
            retry: 1,
            onSuccess: () => refetchDBDriver(),
        });

    const { mutate: updateFCMToken } = useMutation({
        mutationFn: DriverRepository.updateFCMToken,
        retry: 1,
        onSuccess: () => {
            console.log("FCM token updated successfully");
        },
    });

    const updatePushNotificationToken = async () => {
        try {
            const token = await Notifications.getDevicePushTokenAsync();
            console.log("token", token);
            await updateFCMToken({ token: token.data });
            return token.data;
        } catch (error) {
            console.error("Failed to fetch push notification token:", error);
            return null;
        }
    };
    // Watch Firebase auth state
    useEffect(() => {
        updatePushNotificationToken();
        const unsub = onAuthStateChanged(getAuth(), async (user) => {
            if (user) {
                const token = await user.getIdToken();
                setFirebaseToken(token);
                setFirebaseDriver(user);
                setAuthError(null);
                setIsFirebaseAuthLoading(false);
            } else {
                setFirebaseDriver(null);
                setFirebaseToken(null);
                setAuthError("USER_AUTH_TOKEN_NOT_FOUND");
                setIsFirebaseAuthLoading(false);
            }
        });
        return unsub;
    }, []);

    useEffect(() => {
        const setDriverID = async () => {
            try {
                console.log("driver.driverID", driver.driverID);
                await AsyncStorage.setItem("driverID", driver.driverID?.toString());
            } catch (err) {
                console.error("Failed to set driver ID:", err);
            }
        };
        if(driver?.driverID) {
            setDriverID();
        }
    }, [driver]);

    // Send OTP
    const sendOTP = async ({ phoneNumber }) => {
        try {
            setSignInState({ isLoading: true, success: false, error: null, confirmationResult: null });

            const confirmationResult = await signInWithPhoneNumber(getAuth(), `+91${phoneNumber}`);
            setResendOTPState({
                prevSentAt: Date.now(),
                resendPhoneNumber: phoneNumber,
                isResendable: false,
            });

            setSignInState({
                isLoading: false,
                success: false,
                error: null,
                confirmationResult,
            });
        } catch (error) {
            setSignInState({
                isLoading: false,
                success: false,
                error,
                confirmationResult: null,
            });
        }
    };

    // Confirm OTP
    const confirmOTP = async (OTP) => {
        if (!signInState.confirmationResult) return;
        setSignInState((prev) => ({ ...prev, isLoading: true, error: null }));

        try {
            await signInState.confirmationResult.confirm(OTP);
            setSignInState({
                isLoading: false,
                success: true,
                error: null,
                confirmationResult: null,
            });
        } catch (error) {
            setSignInState((prev) => ({ ...prev, isLoading: false, success: false, error }));
        }
    };

    // Sign out
    const signOut = async () => {
        try {
            setSignOutState({ isLoading: true, success: false, error: null });
            await firebaseSignOut(getAuth());
            setSignOutState({ isLoading: false, success: true, error: null });
            Restart();
        } catch (error) {
            setSignOutState({ isLoading: false, success: false, error });
        }
    };

    // const isAuthLoading = isLoadingDriver || firebaseDriver === null;
    
    // const combinedAuthError = driverError || authError;
    const combinedAuthError = authError;

    // console.log("isFirebaseAuthLoading", isFirebaseAuthLoading);
    // console.log("firebaseDriver", firebaseDriver);
    // console.log("firebaseToken", firebaseToken);
    // console.log("combinedAuthError", combinedAuthError);

    return (
        <AuthStateContext.Provider
            value={{
                isFirebaseAuthLoading,
                firebaseDriver,
                firebaseToken,
                authError: combinedAuthError,
                driver,
                driverError,
                isLoadingDriver,
                signInState,
                signOutState,
                resendOTPState,
                isUpdatingDriverProfile,
                updateDriverError,
            }}
        >
            <AuthActionsContext.Provider
                value={{
                    sendOTP,
                    confirmOTP,
                    signOut,
                    setResendOTPState,
                    // refetchDBDriver,
                    updateProfile,
                    // updatePushNotificationToken,
                }}
            >
                {children}
            </AuthActionsContext.Provider>
        </AuthStateContext.Provider>
    );
};

export const useAuthState = () => {
    const ctx = useContext(AuthStateContext);
    if (!ctx) throw new Error("useAuthState must be used inside AuthContextProvider");
    return ctx;
};

export const useAuthActions = () => {
    const ctx = useContext(AuthActionsContext);
    if (!ctx) throw new Error("useAuthActions must be used inside AuthContextProvider");
    return ctx;
};
